#encoding:gbk

交易北交所='是' #否
path  = r'C:\Users\<USER>\Desktop\warning.txt' #预警设置的导出文件路径


class G():
    pass

import time,json,csv
import numpy as np
import pandas as pd
g = G()
g.select= False
g.sell = []
import os


g.position_data = {}

# 增加竞价相关参数
g.min_buy_sell_ratio = 2.0  # 委买/委卖最小比例
g.max_stock_price = 30.0    # 最高股价限制
g.min_price_change = 0.0    # 最小涨幅限制
g.max_price_change = 0.08   # 最大涨幅限制 (从8%提高到12%)
g.profit_take = 0.15        # 最终止盈比例
g.stop_loss = -0.15         # 止损比例
g.batch_buy = True          # 是否分批买入
g.first_batch_percent = 0.4 # 第一批买入比例
g.macd_short = 12           # MACD短周期
g.macd_long = 26           # MACD长周期
g.macd_mid = 9             # MACD平滑周期
g.macd_checked = set()     # 已检查过MACD的股票集合

# 分批止盈设置
g.profit_take_levels = [0.03, 0.05, 0.15]  # 分批止盈点：3%, 5%, 15%
g.profit_take_percents = [0.3, 0.3, 0.4]   # 每次止盈的仓位比例：30%, 30%, 40%
g.profit_take_records = {}  # 记录每只股票已经执行的止盈级别 {股票代码: [已执行的止盈级别]}

def load_param():
    if not os.path.exists('position1.csv'):
        return {}
    else:
        res = {}
        with open('position1.csv','r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                res[row['stockId']] = row['date']
        return res
g.position_data = load_param()
def save(data):
    path = 'position1.csv'
    with open(path,'w',newline='') as f:

        writer = csv.DictWriter(f,delimiter=',',fieldnames=['stockId','date',])
        writer.writeheader()
        for k in data:
            writer.writerow({
                'date':data[k],
                'stockId':k,
            })

def init(ContextInfo):
    ContextInfo.set_account(account)
    g.macd_checked = set()  # 初始化时重置MACD检查记录
    g.sell = []  # 初始化时重置卖出记录
    g.profit_take_records = {}  # 初始化分批止盈记录
    
# 实现"看透主力"指标
def calculate_master_view(ContextInfo, stock_code, periods=60):
    """
    计算"看透主力"指标
    返回字典：{'signal': 信号类型, 'strength': 信号强度, 'color': 柱子颜色}
    信号类型: 1=吸, 2=拉, 3=派, 4=落, 0=无信号
    柱子颜色: 'red'=红色, 'yellow_red'=黄中带红, 'green'=绿色, 'none'=无颜色
    """
    try:
        # 获取K线数据
        data = ContextInfo.get_market_data_ex(['close', 'volume', 'high', 'low'], [stock_code], period='1d', count=periods, subscribe=True)
        if stock_code not in data:
            return {'signal': 0, 'strength': 0, 'color': 'none'}
            
        df = pd.DataFrame({
            'close': data[stock_code]['close'],
            'volume': data[stock_code]['volume'],
            'high': data[stock_code]['high'],
            'low': data[stock_code]['low']
        })
        
        # 严格按照源码计算指标
        # VA:=IF(CLOSE>REF(CLOSE,1),VOL,-VOL);
        df['prev_close'] = df['close'].shift(1)
        df['VA'] = np.where(df['close'] > df['prev_close'], df['volume'], -df['volume'])
        
        # OBV1:=SUM(IF(CLOSE=REF(CLOSE,1),0,VA),0);
        df['VA_adjusted'] = np.where(df['close'] == df['prev_close'], 0, df['VA'])
        df['OBV1'] = df['VA_adjusted'].cumsum()
        
        # OBV2:=EMA(OBV1,3)-MA(OBV1,9);
        df['EMA_OBV1_3'] = df['OBV1'].ewm(span=3, adjust=False).mean()
        df['MA_OBV1_9'] = df['OBV1'].rolling(window=9).mean()
        df['OBV2'] = df['EMA_OBV1_3'] - df['MA_OBV1_9']
        
        # OBV3:=EMA(IF(OBV2>0,OBV2,0),3);
        df['OBV2_positive'] = np.where(df['OBV2'] > 0, df['OBV2'], 0)
        df['OBV3'] = df['OBV2_positive'].ewm(span=3, adjust=False).mean()
        
        # MAC3:=MA(C,3);
        df['MAC3'] = df['close'].rolling(window=3).mean()
        
        # SHORT:=12;LONG:=26;MID:=9;
        SHORT, LONG, MID = 12, 26, 9
        
        # DIF:EMA(CLOSE,SHORT)-EMA(CLOSE,LONG)
        df['EMA_SHORT'] = df['close'].ewm(span=SHORT, adjust=False).mean()
        df['EMA_LONG'] = df['close'].ewm(span=LONG, adjust=False).mean()
        df['DIF'] = df['EMA_SHORT'] - df['EMA_LONG']
        
        # DEA:EMA(DIF,MID)
        df['DEA'] = df['DIF'].ewm(span=MID, adjust=False).mean()
        
        # MACD:=(DIF-DEA)*2;
        df['MACD'] = (df['DIF'] - df['DEA']) * 2
        
        # 判断技术形态
        # OBV3和MAC3趋势判断
        obv3_up = df['OBV3'].iloc[-1] > df['OBV3'].iloc[-2]
        mac3_up = df['MAC3'].iloc[-1] > df['MAC3'].iloc[-2]
        
        # DIF趋势
        dif_up = df['DIF'].iloc[-1] > df['DIF'].iloc[-2]
        
        # 形态识别
        macd_value = df['MACD'].iloc[-1]
        dif_value = df['DIF'].iloc[-1]
        dea_value = df['DEA'].iloc[-1]
        
        # 输出详细的调试信息
        print(f"\n{stock_code} 详细指标数据:")
        print(f"MACD值: {macd_value:.6f}")
        print(f"DIF值: {dif_value:.6f}")
        print(f"DEA值: {dea_value:.6f}")
        print(f"DIF趋势: {'上升' if dif_up else '下降'}")
        print(f"OBV3趋势: {'上升' if obv3_up else '下降'}")
        print(f"MAC3趋势: {'上升' if mac3_up else '下降'}")
        
        # 根据源码判断柱子颜色
        # STICKLINE(OBV3>REF(OBV3,1) AND MAC3>REF(MAC3,1),0,MACD,2,0),COLORYELLOW;
        # 从源码看，当OBV3>REF(OBV3,1) AND MAC3>REF(MAC3,1)时，柱子为黄色
        # 其他信号柱子颜色：吸-浅红色，拉-红色，派-绿色，落-白色
        
        bar_color = 'none'
        
        # 根据源码中的信号判断逻辑确定柱子颜色
        # 吸筹信号 - 浅红色
        if obv3_up and mac3_up and dif_up and dif_value < 0 and macd_value < 0:
            signal = 1  # 吸
            strength = abs(macd_value) * 0.8 + abs(dif_value) * 0.2
            bar_color = 'light_red'
            print(f"{stock_code} 判定为吸筹信号 - 浅红色柱")
            
        # 拉升信号 - 红色
        elif obv3_up and mac3_up and dif_up and dif_value > 0 and macd_value > 0:
            signal = 2  # 拉
            strength = abs(macd_value) * 0.6 + abs(dif_value) * 0.4
            bar_color = 'red'
            print(f"{stock_code} 判定为拉升信号 - 红色柱")
            
        # 派发信号 - 绿色
        elif not obv3_up and not mac3_up and not dif_up and dif_value > 0:
            signal = 3  # 派
            strength = abs(macd_value) * 0.7 + abs(dif_value) * 0.3
            bar_color = 'green'
            print(f"{stock_code} 判定为派发信号 - 绿色柱")
            
        # 落袋信号 - 白色
        elif not obv3_up and not mac3_up and not dif_up and dif_value < 0:
            signal = 4  # 落
            strength = abs(macd_value) * 0.5 + abs(dif_value) * 0.5
            bar_color = 'white'
            print(f"{stock_code} 判定为落袋信号 - 白色柱")
            
        # 黄色柱子 - 当OBV3和MAC3都上升时
        elif obv3_up and mac3_up:
            signal = 0  # 无明确信号
            strength = abs(macd_value) * 0.4
            bar_color = 'yellow'
            print(f"{stock_code} 判定为黄色柱 - OBV3和MAC3都上升")
            
        else:
            # 默认使用MACD值的正负来判断颜色
            if macd_value > 0:
                bar_color = 'red'
                print(f"{stock_code} 默认判定为红色柱: MACD值({macd_value:.6f}) > 0")
            else:
                bar_color = 'green'
                print(f"{stock_code} 默认判定为绿色柱: MACD值({macd_value:.6f}) <= 0")
            signal = 0
            strength = abs(macd_value) * 0.3
            print(f"{stock_code} 无明确信号")
            
        return {'signal': signal, 'strength': strength, 'color': bar_color}
    except Exception as e:
        print(f"计算'看透主力'指标错误: {e}")
        return {'signal': 0, 'strength': 0, 'color': 'none'}

def handlebar(ContextInfo):
    if not ContextInfo.is_last_bar():
        return
    trade_day = ContextInfo.get_trading_dates('SH','','********',10)
    print(trade_day)
    now = time.strftime('%H%M%S')
    
    # 每天开盘时重置MACD检查记录
    if '093000' <= now <= '093100':
        g.macd_checked = set()
        g.sell = []
        g.profit_take_records = {}
        print("新交易日开始，重置MACD检查记录和分批止盈记录")
    
    if not '093000'<= now<='150000':
        return
    # 卖出：
    # 三、止盈止损
  
    positions = get_trade_detail_data(account, accountType, 'POSITION')
    profit_ratio = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_dProfitRate for p in positions}
    hold_vol = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_nCanUseVolume for p in positions if p.m_nCanUseVolume>0}
    full_tick = ContextInfo.get_full_tick([s for s in hold_vol])
    print(profit_ratio)
    if hold_vol:
        # 检查持仓股票的MACD柱子颜色，如果变成绿色则卖出
        for s in hold_vol:
            if s not in g.sell and s not in g.macd_checked:
                # 计算"看透主力"指标，检查MACD柱子颜色
                signal_data = calculate_master_view(ContextInfo, s)
                if signal_data['color'] == 'green':
                    print(f"{s} MACD变成绿色柱子，执行卖出")
                    passorder(24, 1101, account, s, 8, 0, hold_vol[s], '', 2, '', ContextInfo)
                    g.sell.append(s)
                else:
                    # 记录已检查过的股票，避免重复计算
                    g.macd_checked.add(s)
                    print(f"{s} MACD柱子颜色: {signal_data['color']}, 继续持有")
        
        # 分批止盈逻辑
        for s in hold_vol:
            if s in g.sell:
                continue
                
            current_profit_ratio = profit_ratio.get(s, 0)
            
            # 初始化该股票的止盈记录
            if s not in g.profit_take_records:
                g.profit_take_records[s] = []
            
            # 检查各个止盈点
            for i, level in enumerate(g.profit_take_levels):
                # 如果已经执行过该级别的止盈，则跳过
                if i in g.profit_take_records[s]:
                    continue
                    
                # 如果达到止盈点，执行相应比例的止盈
                if current_profit_ratio >= level:
                    # 计算本次需要卖出的数量
                    sell_percent = g.profit_take_percents[i]
                    sell_volume = int(hold_vol[s] * sell_percent)
                    
                    # 确保卖出数量至少为1
                    if sell_volume < 1:
                        sell_volume = 1 if hold_vol[s] >= 1 else 0
                    
                    # 确保不超过可用数量
                    sell_volume = min(sell_volume, hold_vol[s])
                    
                    if sell_volume > 0:
                        print(f"{s} 达到{level*100}%止盈点，卖出{sell_percent*100}%仓位，数量:{sell_volume}")
                        passorder(24, 1101, account, s, 8, 0, sell_volume, '', 2, '', ContextInfo)
                        
                        # 记录已执行的止盈级别
                        g.profit_take_records[s].append(i)
                        
                        # 更新可用数量
                        hold_vol[s] -= sell_volume
                        
                        # 如果全部卖出，添加到已卖出列表
                        if hold_vol[s] <= 0:
                            g.sell.append(s)
                            break
        
        # 总体仓位止盈止损 (保留原有逻辑，但调整为只在总盈亏超过阈值时触发)
        if sum([profit_ratio[s] for s in hold_vol])>g.profit_take:
            for s in hold_vol:
                if s not in g.sell:
                    print(s,'总体止盈 持仓股票的总盈利大于15%')
                    passorder(24, 1101, account, s, 8, 0,hold_vol[s],'',2,'',ContextInfo)
                    g.sell.append(s)
        
        if sum([profit_ratio[s] for s in hold_vol])<g.stop_loss:
            for s in hold_vol:
                if s not in g.sell:
                    print(s,'止损 持仓股票的总亏损大于15%')
                    passorder(24, 1101, account, s, 8, 0,hold_vol[s],'',2,'',ContextInfo)
                    g.sell.append(s)
        
        # 单个股票止损逻辑（保留，但不再单独处理止盈）
        for s in hold_vol:
            if s not in full_tick:
                print(s,'没有取到价格 跳过')
                continue
            ratio = full_tick[s]['lastPrice']/full_tick[s]['lastClose']-1
            
            # 单个股票止损逻辑
            if profit_ratio[s]<g.stop_loss and s not in g.sell:
                print(s,'止损 持亏损大于15%')
                passorder(24, 1101, account, s, 8, 0,hold_vol[s],'',2,'',ContextInfo)
                g.sell.append(s)
        
        # 持有超过7个交易日的股票在收盘前卖出
        if now >='143000':
            for s in hold_vol:
                record_date = g.position_data.get(s,'')
                if record_date not in trade_day:
                    g.position_data[s] = trade_day[-2]
                    continue
                open_index = trade_day.index(record_date)
                if s not in g.sell and open_index<=7:
                    passorder(24, 1101, account, s, 8, 0,hold_vol[s],'',2,'',ContextInfo)
                    print(s,'股票第二天14:30分，平仓持仓')
                    g.sell.append(s)
    
    # 优化选股逻辑
    if now >='093200' and not g.select:  # 开始选股
        stocks = list(set(read_alert()))
        if stocks:
            g.select = True
        print('选股结果：', stocks)
        
        if not stocks:
            print('没有选出股票')
            return
        
        print(len(stocks))
        accounts = get_trade_detail_data(account, accountType, 'account')
        if not accounts:
            print('账号错误或账号掉线')
        money = accounts[0].m_dBalance
        available = accounts[0].m_dAvailable
        
        # 优化1: 获取盘口数据进行筛选
        if len(stocks) > 0:
            # 获取所有候选股票的盘口数据
            full_tick = ContextInfo.get_full_tick(stocks)
            
            # 优化2: 筛选委买委卖比例、股价、涨幅合适的股票
            filtered_stocks = []
            for s in stocks:
                if s not in full_tick:
                    print(f"{s} 未获取到盘口数据，跳过")
                    continue
                    
                # 获取股票当前价格和涨幅
                price = full_tick[s]['lastPrice']
                price_change_ratio = price/full_tick[s]['lastClose']-1
                
                # 计算委买委卖比例 (如果交易系统支持)
                buy_sell_ratio = 0
                try:
                    total_bid_vol = sum([full_tick[s].get(f'bidSize{i}', 0) for i in range(1, 6)])
                    total_ask_vol = sum([full_tick[s].get(f'askSize{i}', 0) for i in range(1, 6)])
                    buy_sell_ratio = total_bid_vol / total_ask_vol if total_ask_vol > 0 else 999
                except:
                    buy_sell_ratio = 999  # 如果无法获取设为默认值
                
                # 打印每只股票的筛选条件详情
                print(f"\n股票 {s} 筛选条件详情:")
                print(f"  价格: {price:.2f} (限制: <= {g.max_stock_price:.2f})")
                print(f"  涨幅: {price_change_ratio*100:.2f}% (限制: {g.min_price_change*100}% < 涨幅 < {g.max_price_change*100}%)")
                print(f"  委买/委卖比例: {buy_sell_ratio:.2f} (限制: >= {g.min_buy_sell_ratio:.2f})")
                
                # 应用筛选条件
                price_ok = price <= g.max_stock_price
                change_ok = g.min_price_change < price_change_ratio < g.max_price_change
                ratio_ok = buy_sell_ratio >= g.min_buy_sell_ratio or buy_sell_ratio == 999
                
                if price_ok and change_ok and ratio_ok:
                    filtered_stocks.append(s)
                    print(f"  结果: 通过筛选")
                else:
                    print(f"  结果: 未通过筛选, 原因: " + 
                         (f"价格过高 " if not price_ok else "") +
                         (f"涨幅不符 " if not change_ok else "") +
                         (f"委比过低 " if not ratio_ok else ""))
            
            # 使用筛选后的股票列表
            stocks = filtered_stocks
            print('筛选后的股票列表:', stocks)
            
            # 如果没有符合条件的股票，直接返回
            if not stocks:
                print('筛选后没有符合条件的股票')
                return
                
            # 使用"看透主力"指标进行进一步筛选
            stock_signals = {}
            for s in stocks:
                signal_data = calculate_master_view(ContextInfo, s)
                stock_signals[s] = signal_data
                print(f"股票 {s} 的看透主力信号: {signal_data}")
            
            # 强制排除绿色柱子的股票
            stocks = [s for s in stocks if stock_signals[s]['color'] != 'green']
            print(f"排除绿色柱后的股票列表: {stocks}")
            
            # 如果没有符合条件的股票，直接返回
            if not stocks:
                print('排除绿色柱后没有符合条件的股票')
                return
            
            # 根据柱子颜色筛选股票
            red_stocks = []      # 红色柱子股票
            light_red_stocks = [] # 浅红色柱子股票
            yellow_stocks = []    # 黄色柱子股票
            white_stocks = []     # 白色柱子股票
            
            for s in stocks:
                color = stock_signals[s]['color']
                if color == 'red':
                    red_stocks.append(s)
                elif color == 'light_red':
                    light_red_stocks.append(s)
                elif color == 'yellow':
                    yellow_stocks.append(s)
                elif color == 'white':
                    white_stocks.append(s)
            
            # 根据信号强度排序
            red_stocks = sorted(red_stocks, key=lambda s: stock_signals[s]['strength'], reverse=True)
            light_red_stocks = sorted(light_red_stocks, key=lambda s: stock_signals[s]['strength'], reverse=True)
            yellow_stocks = sorted(yellow_stocks, key=lambda s: stock_signals[s]['strength'], reverse=True)
            white_stocks = sorted(white_stocks, key=lambda s: stock_signals[s]['strength'], reverse=True)
            
            # 合并排序后的列表，优先级: 红色柱 > 浅红色柱 > 黄色柱 > 白色柱
            sorted_stocks = red_stocks + light_red_stocks + yellow_stocks + white_stocks
            
            # 如果没有符合条件的股票，尝试从原始筛选列表中选择
            if not sorted_stocks and stocks:
                print("没有红色或黄色的股票，使用原始筛选列表")
                sorted_stocks = stocks
            
            # 更新筛选后的股票列表
            stocks = sorted_stocks
            print('按柱子颜色排序后的股票列表:', stocks)
            print('红色柱子股票:', red_stocks)
            print('浅红色柱子股票:', light_red_stocks)
            print('黄色柱子股票:', yellow_stocks)
            
            # 筛选出"吸"和"拉"信号的股票，优先选择"拉"信号
            primary_stocks = []    # 拉升信号
            secondary_stocks = []  # 吸筹信号
            other_signal_stocks = []      # 其他股票
            
            for s in stocks:
                signal = stock_signals[s]['signal']
                if signal == 2:  # 拉升信号
                    primary_stocks.append(s)
                elif signal == 1:  # 吸筹信号
                    secondary_stocks.append(s)
                else:
                    other_signal_stocks.append(s)
            
            # 按信号强度排序
            primary_stocks = sorted(primary_stocks, key=lambda s: stock_signals[s]['strength'], reverse=True)
            secondary_stocks = sorted(secondary_stocks, key=lambda s: stock_signals[s]['strength'], reverse=True)
            
            # 合并排序后的列表，优先级: 拉升 > 吸筹 > 其他
            signal_sorted_stocks = primary_stocks + secondary_stocks + other_signal_stocks
            
            # 更新筛选后的股票列表
            stocks = signal_sorted_stocks if signal_sorted_stocks else stocks
            print('按"看透主力"信号排序后的股票列表:', stocks)
        
        # 优化3: 计算技术指标辅助选股
        if len(stocks) > 5:
            print('选出股票数量多于5个')
            return
        elif 5 > len(stocks) > 3:
            ma18_dict = {}
            rsi_dict = {}  # 新增RSI指标

            if stocks:
                price_data = ContextInfo.get_market_data_ex(['close', 'high', 'low'], stocks, period='1d', count=-1, subscribe=True)
                for s in price_data:
                    # 计算18日均线
                    ma18_dict[s] = price_data[s]['close'].rolling(18).mean().iloc[-1]
                    
                    # 计算14日RSI
                    try:
                        close = price_data[s]['close']
                        delta = close.diff()
                        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                        rs = gain / loss
                        rsi_dict[s] = 100 - (100 / (1 + rs)).iloc[-1]
                    except:
                        rsi_dict[s] = 50  # 默认值

            # 优化4: 多因子选股，考虑均线偏离度和RSI
            def stock_score(stock):
                # 均线偏离度分数 (越接近均线分数越高)
                ma_score = 1 - abs(full_tick[stock]['lastPrice'] / ma18_dict[stock] - 1) if stock in ma18_dict else 0
                # RSI分数 (RSI在40-60之间最佳)
                rsi_score = 1 - abs(rsi_dict[stock] - 50) / 50 if stock in rsi_dict else 0
                # 主力信号分数
                signal_score = 0
                if stock in stock_signals:
                    if stock_signals[stock]['signal'] == 2:  # 拉升信号
                        signal_score = 1.0
                    elif stock_signals[stock]['signal'] == 1:  # 吸筹信号
                        signal_score = 0.8
                    elif stock_signals[stock]['signal'] == 3:  # 派发信号
                        signal_score = 0.3
                    elif stock_signals[stock]['signal'] == 4:  # 落袋信号
                        signal_score = 0.2
                
                # 柱子颜色分数 (重要：决定是否被选中)
                color_score = 0
                if stock in stock_signals:
                    color = stock_signals[stock]['color']
                    if color == 'red':  # 红色柱
                        color_score = 2.0
                    elif color == 'light_red':  # 浅红色柱
                        color_score = 1.8
                    elif color == 'yellow':  # 黄色柱
                        color_score = 1.5
                    elif color == 'white':  # 白色柱
                        color_score = 0.5
                    elif color == 'green':  # 绿色柱
                        color_score = -2.0  # 负分，降低绿色柱的排名
                
                # 综合分数，增加柱子颜色权重
                return ma_score * 0.2 + rsi_score * 0.1 + signal_score * 0.3 + color_score * 0.4
            
            # 按照综合分数排序
            stocks = sorted(stocks, key=stock_score, reverse=True)
            print('排序后的股票:', stocks)
            print('均线数据:', ma18_dict)
            print('RSI数据:', rsi_dict)
            
            # 选择前2只股票买入
            for s in stocks[:2]:
                print(f'order{s}, avi:{available}, money:{money}')
                order_money = min(money/2, available)
                # 分批买入逻辑
                if g.batch_buy:
                    first_batch = order_money * g.first_batch_percent
                    passorder(23, 1102, account, s, 0, get_price(ContextInfo, s), first_batch, '', 2, '', ContextInfo)
                    available -= first_batch
                    # 记录第二批信息，等待开盘后10分钟再买入 (需要另外实现)
                else:
                    passorder(23, 1102, account, s, 0, get_price(ContextInfo, s), order_money, '', 2, '', ContextInfo)
                    available -= order_money

        elif 0 < len(stocks) <= 3:
            # 当股票数量≤3时，使用主力信号和柱子颜色进行排序
            def stock_priority(stock):
                if stock in stock_signals:
                    signal = stock_signals[stock]['signal']
                    strength = stock_signals[stock]['strength']
                    color = stock_signals[stock]['color']
                    
                    # 基础分：信号类型
                    base_score = 0
                    if signal == 2:  # 拉升信号
                        base_score = 3
                    elif signal == 1:  # 吸筹信号
                        base_score = 2
                    elif signal == 3:  # 派发信号
                        base_score = 1
                    elif signal == 4:  # 落袋信号
                        base_score = 0.5
                    
                    # 颜色加成
                    color_bonus = 0
                    if color == 'red':  # 红色柱
                        color_bonus = 5  # 最高优先级
                    elif color == 'light_red':  # 浅红色柱
                        color_bonus = 4  # 次高优先级
                    elif color == 'yellow':  # 黄色柱
                        color_bonus = 3  # 第三优先级
                    elif color == 'white':  # 白色柱
                        color_bonus = 1  # 第四优先级
                    elif color == 'green':  # 绿色柱
                        color_bonus = -5  # 负优先级，降低绿色柱
                    
                    # 总分 = 基础分 + 颜色加成 + 强度调整
                    return base_score + color_bonus + strength/100
                return 0
            
            # 按主力信号优先级排序
            stocks = sorted(stocks, key=stock_priority, reverse=True)
            print('按主力信号排序后的股票:', stocks)
            
            for s in stocks:
                order_money = min(money/len(stocks), available)
                print('order money', order_money)
                # 分批买入逻辑
                if g.batch_buy:
                    first_batch = order_money * g.first_batch_percent
                    passorder(23, 1102, account, s, 0, get_price(ContextInfo, s), first_batch, '', 2, '', ContextInfo)
                    available -= first_batch
                    # 记录第二批信息，等待开盘后10分钟再买入 (需要另外实现)
                else:
                    passorder(23, 1102, account, s, 0, get_price(ContextInfo, s), order_money, '', 2, '', ContextInfo)
                    available -= order_money
                
def deal_callback(C, d):
    stock = d.m_strInstrumentID+'.'+d.m_strExchangeID
    g.position_data[stock] = time.strftime('%Y%m%d')
    save(g.position_data)
    
    # 记录买入信息，用于后续风险控制
    if d.m_nOffsetFlag == 0:  # 开仓
        # 可以在这里记录开仓信息，用于后续设置动态止损等
        pass

def get_price(C, s):
    full = C.get_full_tick([s])
    return full[s]['lastPrice']*1.01

# 增加盘中动态止损函数 (需要在分钟K调用)
def dynamic_stop_loss(ContextInfo):
    """盘中动态止损，根据5分钟K线设置止损"""
    if not ContextInfo.is_last_bar():
        return
    
    positions = get_trade_detail_data(account, accountType, 'POSITION')
    hold_vol = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_nCanUseVolume for p in positions if p.m_nCanUseVolume>0}
    
    for s in hold_vol:
        if s in g.sell:
            continue
            
        # 获取5分钟K线
        k5 = ContextInfo.get_market_data_ex(['open', 'high', 'low', 'close'], [s], period='5m', count=10, subscribe=True)
        if s not in k5:
            continue
            
        # 计算最近开盘后的最低价
        low_since_open = k5[s]['low'].min()
        
        # 获取最新价格
        full_tick = ContextInfo.get_full_tick([s])
        if s not in full_tick:
            continue
            
        current_price = full_tick[s]['lastPrice']
        
        # 动态止损条件：如果价格跌破开盘后最低价的98%，执行止损
        if current_price < low_since_open * 0.98:
            print(s, '触发动态止损')
            passorder(24, 1101, account, s, 8, 0, hold_vol[s], '', 2, '', ContextInfo)
            g.sell.append(s)

def read_alert():
    if not os.path.exists(path):
        return []

    stocks = []
    with open(path, 'r') as f:
        for line in f:
            stock = line.split('\t')[0]
            if stock.startswith('6'):
                stock+='.SH'
            elif stock.startswith('0') or stock.startswith('3'):
                stock+='.SZ'
            elif stock.startswith('4') or stock.startswith('8') or stock.startswith('9'):
                stock+='.BJ'
            else:
                print('找不股票市场',stock)
            stocks.append(stock)
        if not 交易北交所=='是':
            stocks = [s for s in stocks if not s.endswith('BJ')]
    return stocks
















