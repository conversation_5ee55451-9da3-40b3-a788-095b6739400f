# 红白线交易策略优化说明

## 主要改进内容

### 1. 策略参数灵活化

- 添加了价格范围参数（`价格范围下限`和`价格范围上限`），提供更灵活的价格过滤机制（默认3-100元）
- 添加了`验证000756`开关，用于特别关注和详细分析000756.SZ股票
- 将`使用趋势判断`参数默认设置为False，使策略更加灵活，不强制要求上升趋势
- 新增`检查均线距离`参数（默认False），可选择是否将价格与均线距离作为筛选条件

### 2. 筛选条件优化

- 放宽了均线距离阈值（MA_DISTANCE_THRESHOLD）从0.5%提高到0.8%
- 降低了成交量强度阈值（VOLUME_STRENGTH_THRESHOLD）从1.3降至1.2
- 降低了K线实体占比阈值（CANDLE_BODY_RATIO）从0.6降至0.5
- 降低了做多成交量阈值（VOLUME_THRESHOLD_LONG）从1.2降至1.1
- 将价格与均线距离检查设为可选条件，解决因均线距离过滤掉潜在机会的问题

### 3. 趋势判断算法优化

采用了多维度综合评分系统来判断趋势：
- 结合收盘价与均线位置关系
- 考虑近期高点和低点的趋势走向
- 分析价格相对于前N日的变化情况
- 使用加权计分方式得出趋势评分，更客观地反映市场趋势

### 4. 成交量检查优化

优化成交量检查算法，采用多种比较方法：
- 与前3日均值比较（基本方法）
- 与前5日最大值比较（应对短期波动）
- 与10日均值比较（考虑中期趋势）
- 只要满足任一条件即认为成交量放大，更灵活地适应不同市场环境

### 5. 选股和日志优化

- 添加更详细的不选原因说明，便于分析和优化策略
- 添加了策略配置显示，运行时清晰展示关键参数
- 增强了对特定股票（如000756.SZ）的详细分析和日志输出
- 修正了K线实体比例计算，使用(c-o)/(h-l)而非(c-o)/(h-o)

### 6. 运行日志完善

- 每次策略运行开始和结束时添加明显标记
- 添加更多统计信息，便于分析策略效果
- 显示满足各条件的股票数量和比例
- 对选出的股票添加名称显示，便于识别

## 针对000756.SZ的特别调整

针对000756.SZ无法被选中的问题，我们进行了以下调整：

1. 关闭了趋势判断条件（`使用趋势判断 = False`），使下降趋势的股票也能被选中
2. 关闭了均线距离检查（`检查均线距离 = False`），避免因价格与均线距离过大而被过滤
3. 特别添加了该股票的详细日志输出，方便追踪筛选过程
4. 放宽了多项筛选条件的阈值，使边缘情况下的信号也能被捕捉

## 使用建议

- 建议保持`使用趋势判断 = False`设置，除非市场整体处于明显上升趋势
- 建议保持`检查均线距离 = False`设置，以捕捉更多交易机会，特别是价格快速突破的情况
- 可根据市场环境调整Config类中的各项阈值参数
- 对于个股特殊关注，可使用`验证000756 = True`类似的参数针对特定股票进行详细分析

## 风险提示

放宽筛选条件虽然能够捕捉更多潜在机会，但也可能增加误信号风险。建议结合以下措施控制风险：

1. 严格执行止损策略（已有7%的总体止损和8%的个股止损）
2. 持续观察价格与红线关系，及时止损
3. 使用分批止盈策略分散风险
4. 可考虑降低单次买入资金比例，分散投资风险 