#encoding:gbk

# ====================== 可调整的重要参数 ======================
# 交易设置
交易北交所='否'  # 是否交易北交所股票 '是'或'否'
path = r'C:\Users\<USER>\Desktop\warning.txt'  # 预警设置的导出文件路径
PRINT_ACCOUNT_INTERVAL = 1  # 打印账户信息的时间间隔(分钟)
CHECK_MANUAL_POSITIONS = True  # 是否检查并记录手动买入的持仓

# 资金管理参数
DAILY_POSITION_LIMIT = 0.2  # 单日最大仓位比例
SMALL_FUND_THRESHOLD = 10000  # 小资金阈值
SMALL_FUND_INVEST_RATIO = 1.0  # 小资金投入比例
LARGE_FUND_INVEST_RATIO = 0.7  # 大资金投入比例
MAX_SINGLE_STOCK_RATIO = 0.4  # 单只股票最大持仓比例(占总资金)

# 选股参数
SELECT_TIME = "145500"  # 选股时间点 14:55
MAX_PRICE_FILTER = 40  # 股价上限过滤
MAX_DROP_FILTER = 0.05  # 最大跌幅过滤(5%)
MARKET_DROP_THRESHOLD = 0.015  # 大盘下跌阈值(1.5%)

# 止盈参数
TOTAL_PROFIT_THRESHOLD = 0.05  # 总盈利阈值(5%)
HIGH_PROFIT_THRESHOLD = 0.08  # 高盈利阈值(8%)
MID_PROFIT_THRESHOLD = 0.05  # 中等盈利阈值(5%)
LOW_PROFIT_THRESHOLD = 0.03  # 低盈利阈值(3%)
HIGH_PROFIT_REDUCE_RATIO = 0.7  # 高盈利减仓比例(70%)
MID_PROFIT_REDUCE_RATIO = 0.5  # 中等盈利减仓比例(50%)
LOW_PROFIT_REDUCE_RATIO = 0.3  # 低盈利减仓比例(30%)

# 止损参数
LOSS_THRESHOLD = 0.05  # 止损阈值(5%)
MA_PERIOD = 18  # 均线周期
MA_DROP_THRESHOLD = 0.02  # 跌破均线阈值(2%)

# 时间管理参数
SHORT_HOLD_DAYS = 3  # 短期持仓天数
MIN_SHORT_HOLD_PROFIT = 0.02  # 短期持仓最低收益率(2%)
MAX_HOLD_DAYS = 3  # 最大持仓天数

# 买入价格参数
HIGH_RISE_THRESHOLD = 0.03  # 高涨幅阈值(3%)
HIGH_RISE_PREMIUM = 0.005  # 高涨幅溢价率(0.5%)
NORMAL_PREMIUM = 0.01  # 普通溢价率(1%)
# =============================================================


class G():
    pass

import time,json,csv
import numpy as np
import talib as ta
from datetime import datetime
g = G()
g.select= False
g.sell = []
g.last_account_print_time = 0  # 上次打印账户信息的时间
import os


g.position_data = {}
g.daily_limit = DAILY_POSITION_LIMIT  # 使用参数
g.time_points = [SELECT_TIME]  # 使用参数

def load_param():
    if not os.path.exists('position1.csv'):
        return {}
    else:
        res = {}
        with open('position1.csv','r') as f:
            reader = csv.DictReader(f)
            for row in reader:
                res[row['stockId']] = row['date']
        return res
g.position_data = load_param()
def save(data):
    path = 'position1.csv'
    with open(path,'w',newline='') as f:

        writer = csv.DictWriter(f,delimiter=',',fieldnames=['stockId','date',])
        writer.writeheader()
        for k in data:
            writer.writerow({
                'date':data[k],
                'stockId':k,
            })

def init(ContextInfo):
    ContextInfo.set_account(account)
    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 策略初始化完成，交易北交所：{交易北交所}，选股时间：{SELECT_TIME[:2]}:{SELECT_TIME[2:4]}:{SELECT_TIME[4:]}")
    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 止盈参数: 总盈利>{TOTAL_PROFIT_THRESHOLD*100}%, 高盈利>{HIGH_PROFIT_THRESHOLD*100}%, 中盈利>{MID_PROFIT_THRESHOLD*100}%, 低盈利>{LOW_PROFIT_THRESHOLD*100}%")
    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 止损参数: 亏损>{LOSS_THRESHOLD*100}%, 跌破{MA_PERIOD}日均线{MA_DROP_THRESHOLD*100}%")
    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 资金管理参数: 单日最大仓位比例={DAILY_POSITION_LIMIT*100}%, 小资金阈值={SMALL_FUND_THRESHOLD}元")
    
    # 策略启动时立即打印账户信息
    print_account_info()
    g.last_account_print_time = time.time()

def handlebar(ContextInfo):
    if not ContextInfo.is_last_bar():
        return
    trade_day = ContextInfo.get_trading_dates('SH','','********',10)
    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 交易日历: {trade_day}")
    now = time.strftime('%H%M%S')
    if not '093000'<= now<='150000':
        return
    
    # 强制每次调用handlebar时都打印账户信息
    print_account_info()
    g.last_account_print_time = time.time()
    
    # 检查并记录手动买入的持仓
    if CHECK_MANUAL_POSITIONS:
        check_manual_positions(trade_day)
    
    # 打印当前市场状态
    index_check = ContextInfo.get_full_tick(['000001.SH', '399001.SZ'])
    if '000001.SH' in index_check and '399001.SZ' in index_check:
        sh_change = index_check['000001.SH']['lastPrice']/index_check['000001.SH']['lastClose'] - 1
        sz_change = index_check['399001.SZ']['lastPrice']/index_check['399001.SZ']['lastClose'] - 1
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 大盘状态: 上证指数: {sh_change*100:.2f}%, 深证成指: {sz_change*100:.2f}%")
    
    # 卖出策略优化
    positions = get_trade_detail_data(account, accountType, 'POSITION')
    profit_ratio = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_dProfitRate for p in positions}
    hold_vol = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_nCanUseVolume for p in positions if p.m_nCanUseVolume>0}
    full_tick = ContextInfo.get_full_tick([s for s in hold_vol])
    
    # 打印持仓信息
    if hold_vol:
        total_profit = sum([profit_ratio[s] for s in hold_vol])
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 当前持仓: {len(hold_vol)}只, 总盈亏: {total_profit*100:.2f}%")
        for s in hold_vol:
            if s in profit_ratio:
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 持仓股票: {s}, 盈亏: {profit_ratio[s]*100:.2f}%, 持仓量: {hold_vol[s]}")
    
    # 持仓管理 - 动态止盈止损
    if hold_vol:
        # 分层止盈策略
        total_profit = sum([profit_ratio[s] for s in hold_vol])
        
        # 总体盈利分层止盈 - 适应弱势市场
        if total_profit > TOTAL_PROFIT_THRESHOLD:  # 使用参数
            for s in hold_vol:
                if s not in g.sell and profit_ratio[s] > 0:
                    sell_vol = int(hold_vol[s] * MID_PROFIT_REDUCE_RATIO)  # 使用参数
                    if sell_vol > 0:
                        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 分层止盈 总盈利>{TOTAL_PROFIT_THRESHOLD*100}%，减持{MID_PROFIT_REDUCE_RATIO*100}%盈利股票")
                        passorder(24, 1101, account, s, 8, 0, sell_vol,'',2,'',ContextInfo)
                        g.sell.append(s)
        
        # 个股止盈策略 - 梯度止盈 - 适应弱势市场
        for s in hold_vol:
            if s not in full_tick:
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 没有取到价格 跳过")
                continue
                
            # 高盈利梯度止盈 - 降低阈值
            if profit_ratio[s] > HIGH_PROFIT_THRESHOLD and s not in g.sell:  # 使用参数
                sell_vol = int(hold_vol[s] * HIGH_PROFIT_REDUCE_RATIO)  # 使用参数
                if sell_vol > 0:
                    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 梯度止盈 个股盈利>{HIGH_PROFIT_THRESHOLD*100}%，减持{HIGH_PROFIT_REDUCE_RATIO*100}%")
                    passorder(24, 1101, account, s, 8, 0, sell_vol,'',2,'',ContextInfo)
                    g.sell.append(s)
            elif profit_ratio[s] > MID_PROFIT_THRESHOLD and s not in g.sell:  # 使用参数
                sell_vol = int(hold_vol[s] * MID_PROFIT_REDUCE_RATIO)  # 使用参数
                if sell_vol > 0:
                    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 梯度止盈 个股盈利>{MID_PROFIT_THRESHOLD*100}%，减持{MID_PROFIT_REDUCE_RATIO*100}%")
                    passorder(24, 1101, account, s, 8, 0, sell_vol,'',2,'',ContextInfo)
                    g.sell.append(s)
            elif profit_ratio[s] > LOW_PROFIT_THRESHOLD and s not in g.sell:  # 使用参数
                sell_vol = int(hold_vol[s] * LOW_PROFIT_REDUCE_RATIO)  # 使用参数
                if sell_vol > 0:
                    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 梯度止盈 个股盈利>{LOW_PROFIT_THRESHOLD*100}%，减持{LOW_PROFIT_REDUCE_RATIO*100}%")
                    passorder(24, 1101, account, s, 8, 0, sell_vol,'',2,'',ContextInfo)
                    g.sell.append(s)
            
            # 优化止损 - 早止损，小止损
            if profit_ratio[s] < -LOSS_THRESHOLD and s not in g.sell:  # 使用参数
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 止损 个股亏损超过{LOSS_THRESHOLD*100}%，全部卖出")
                passorder(24, 1101, account, s, 8, 0, hold_vol[s],'',2,'',ContextInfo)
                g.sell.append(s)

            # 跌破均线止损
            if s in full_tick:
                price_data = ContextInfo.get_market_data_ex(['close'], [s], period='1d', count=30, subscribe=True)
                if s in price_data:
                    ma = price_data[s]['close'].rolling(MA_PERIOD).mean().iloc[-1]  # 使用参数
                    current_price = full_tick[s]['lastPrice']
                    
                    if current_price < ma * (1-MA_DROP_THRESHOLD) and profit_ratio[s] < 0 and s not in g.sell:  # 使用参数
                        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 技术止损 跌破{MA_PERIOD}日均线{MA_DROP_THRESHOLD*100}%，全部卖出")
                        passorder(24, 1101, account, s, 8, 0, hold_vol[s],'',2,'',ContextInfo)
                        g.sell.append(s)
        
        # 持仓时间管理 - 非强势股早平仓
        if now >= '143000':
            for s in hold_vol:
                record_date = g.position_data.get(s,'')
                if record_date not in trade_day:
                    g.position_data[s] = trade_day[-2]
                    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 更新持仓日期为 {trade_day[-2]}")
                    continue
                    
                open_index = trade_day.index(record_date)
                # 持仓超过3天且无盈利的股票提前平仓
                if s not in g.sell and open_index <= 7:
                    if open_index <= SHORT_HOLD_DAYS and profit_ratio[s] < MIN_SHORT_HOLD_PROFIT:  # 使用参数
                        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 绩效不佳平仓，持仓时间短且无显著收益，持仓天数: {open_index}天")
                        passorder(24, 1101, account, s, 8, 0, hold_vol[s],'',2,'',ContextInfo)
                        g.sell.append(s)
                    elif open_index > MAX_HOLD_DAYS:  # 使用参数
                        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 时间平仓，持仓超过{MAX_HOLD_DAYS}天，持仓天数: {open_index}天")
                        passorder(24, 1101, account, s, 8, 0, hold_vol[s],'',2,'',ContextInfo)
                        g.sell.append(s)
    
    # 根据截图优化选股逻辑 - 固定选股时间点
    if now >= SELECT_TIME and now <= '145959' and not g.select:  # 使用参数
        # 选股前打印账户信息
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 准备开始选股，打印当前账户状态:")
        print_account_info()
        
        # 只在设定时间开始选股
        stocks = list(set(read_alert()))
        
        if stocks:
            g.select = True
        
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 选股时间: {now}, 原始选股结果: {stocks}, 数量: {len(stocks)}只")
        
        if not stocks:
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 没有选出股票")
            return
        
        # 检查市场整体情况，避开大跌日
        index_check = ContextInfo.get_full_tick(['000001.SH', '399001.SZ'])
        if '000001.SH' in index_check and '399001.SZ' in index_check:
            sh_change = index_check['000001.SH']['lastPrice']/index_check['000001.SH']['lastClose'] - 1
            sz_change = index_check['399001.SZ']['lastPrice']/index_check['399001.SZ']['lastClose'] - 1
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 选股时大盘状态: 上证指数: {sh_change*100:.2f}%, 深证成指: {sz_change*100:.2f}%")
            if sh_change < -MARKET_DROP_THRESHOLD and sz_change < -MARKET_DROP_THRESHOLD:  # 使用参数
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 大盘弱势，暂停买入")
                return
        
        accounts = get_trade_detail_data(account, accountType, 'account')
        if not accounts:
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 账号错误或账号掉线")
            return
            
        money = accounts[0].m_dBalance
        available = accounts[0].m_dAvailable
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 账户资金: 总资金={money:.2f}, 可用资金={available:.2f}")
        
        # 控制单日最大仓位
        max_invest = money * DAILY_POSITION_LIMIT  # 使用常量而非g.daily_limit
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 单日最大仓位限制: {max_invest:.2f}元 (总资金的{DAILY_POSITION_LIMIT*100}%)")
        available = min(available, max_invest)
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 考虑仓位限制后的可用资金: {available:.2f}元")
        
        # 尾盘选股逻辑优化 - 按照截图中的规则
        full_tick = ContextInfo.get_full_tick(stocks)
        
        # 第一步：排除股价>40元和跌幅>5%的
        filtered_stocks = [s for s in full_tick if full_tick[s]['lastPrice'] < MAX_PRICE_FILTER]  # 使用参数
        filtered_stocks = [s for s in filtered_stocks if (full_tick[s]['lastPrice']/full_tick[s]['lastClose']-1) > -MAX_DROP_FILTER]  # 使用参数
        
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 筛选后股票: {filtered_stocks}, 数量: {len(filtered_stocks)}只")
        
        if not filtered_stocks:
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 过滤后没有符合条件的股票")
            return
        
        # 计算18日均线距离
        ma18_dict = {}
        price_dict = {}
        selected_stocks = []
        
        for s in filtered_stocks:
            price_hist = ContextInfo.get_market_data_ex(['close'], [s], period='1d', count=30, subscribe=True)
            
            if s not in price_hist or len(price_hist[s]['close']) < MA_PERIOD:  # 使用参数
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 历史数据不足，无法计算{MA_PERIOD}日均线")
                continue
                
            # 计算均线
            ma18 = price_hist[s]['close'].rolling(MA_PERIOD).mean().iloc[-1]  # 使用参数
            ma18_dict[s] = ma18
            price_dict[s] = full_tick[s]['lastPrice']
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 当前价: {price_dict[s]:.2f}, {MA_PERIOD}日均线: {ma18:.2f}, 距离: {(price_dict[s]/ma18-1)*100:.2f}%")
        
        # 按照不同票数规则选股
        if len(filtered_stocks) > 5:
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 选出股票数量多于5个，按规则选取")
            # 找出最高价和最低价
            if filtered_stocks:
                max_price_stock = max(filtered_stocks, key=lambda s: full_tick[s]['lastPrice'])
                min_price_stock = min(filtered_stocks, key=lambda s: full_tick[s]['lastPrice'])
                
                # 计算中间价
                middle_price = (full_tick[max_price_stock]['lastPrice'] + full_tick[min_price_stock]['lastPrice']) / 2
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 最高价股票: {max_price_stock} 价格: {full_tick[max_price_stock]['lastPrice']:.2f}, 最低价股票: {min_price_stock} 价格: {full_tick[min_price_stock]['lastPrice']:.2f}, 中间价: {middle_price:.2f}")
                
                # 选择价格接近中间价的股票
                closest_to_middle = min(filtered_stocks, 
                                       key=lambda s: abs(full_tick[s]['lastPrice'] - middle_price))
                selected_stocks = [closest_to_middle]
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 选择价格最接近中间价的股票: {closest_to_middle}, 价格: {full_tick[closest_to_middle]['lastPrice']:.2f}")
            
        elif len(filtered_stocks) == 4:
            # 四选一：选择股价处于中间的
            sorted_by_price = sorted(filtered_stocks, key=lambda s: full_tick[s]['lastPrice'])
            if len(sorted_by_price) >= 4:
                # 选择中间两只的均值接近的
                middle_price = (full_tick[sorted_by_price[1]]['lastPrice'] + 
                               full_tick[sorted_by_price[2]]['lastPrice']) / 2
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 4只股票排序: {[(s, full_tick[s]['lastPrice']) for s in sorted_by_price]}, 中间价: {middle_price:.2f}")
                
                selected_stocks = [min(filtered_stocks, 
                                      key=lambda s: abs(full_tick[s]['lastPrice'] - middle_price))]
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 选择价格最接近中间价的股票: {selected_stocks[0]}")
        
        elif len(filtered_stocks) == 3:
            # 三选一：选择股价处于中间的
            sorted_by_price = sorted(filtered_stocks, key=lambda s: full_tick[s]['lastPrice'])
            if len(sorted_by_price) >= 3:
                selected_stocks = [sorted_by_price[1]]  # 选中间价格的
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 3只股票排序: {[(s, full_tick[s]['lastPrice']) for s in sorted_by_price]}, 选择中间价格股票: {selected_stocks[0]}")
        
        elif len(filtered_stocks) == 2:
            # 两只票选一只：选择最接近18日均线的
            if len(ma18_dict) >= 2:
                selected_stocks = [min(ma18_dict.keys(), 
                                      key=lambda s: abs(price_dict[s] - ma18_dict[s]))]
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 2只股票: {[(s, price_dict[s], ma18_dict[s], abs(price_dict[s]-ma18_dict[s])) for s in ma18_dict]}, 选择最接近{MA_PERIOD}日均线的: {selected_stocks[0]}")
        
        elif len(filtered_stocks) == 1:
            # 只有一只票直接选择
            selected_stocks = filtered_stocks
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 只有1只股票: {selected_stocks[0]}, 直接选择")
        
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 最终选出股票: {selected_stocks}")
        
        # 资金分配
        if selected_stocks:
            # 根据可用资金情况决定投入比例
            if money < SMALL_FUND_THRESHOLD:  # 使用总资金判断是否为小资金
                invest_ratio = SMALL_FUND_INVEST_RATIO  # 使用参数
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 小资金账户(总资金<{SMALL_FUND_THRESHOLD}元)，使用{SMALL_FUND_INVEST_RATIO*100}%可用资金: {available * invest_ratio:.2f}元")
            else:  # 可用资金大于阈值
                invest_ratio = LARGE_FUND_INVEST_RATIO  # 使用参数
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 大资金账户(总资金>={SMALL_FUND_THRESHOLD}元)，使用{LARGE_FUND_INVEST_RATIO*100}%可用资金: {available * invest_ratio:.2f}元")
            
            # 计算总投资金额
            total_invest = available * invest_ratio
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 本次交易总投资金额: {total_invest:.2f}元")
            
            # 根据资金大小决定是否启用单只股票最大持仓限制
            if money < SMALL_FUND_THRESHOLD:  # 使用总资金判断是否为小资金
                # 小资金不启用单只股票最大持仓限制
                order_amount_per_stock = total_invest / len(selected_stocks)
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 小资金账户不启用单只股票最大持仓限制")
            else:
                # 大资金才启用单只股票最大持仓限制
                # 计算单只股票最大投资额
                max_single_stock_amount = money * MAX_SINGLE_STOCK_RATIO
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 启用单只股票最大投资额限制: {max_single_stock_amount:.2f}元 (总资金的{MAX_SINGLE_STOCK_RATIO*100}%)")
                
                # 平均分配资金，但考虑单只股票最大持仓限制
                order_amount_per_stock = min(total_invest / len(selected_stocks), max_single_stock_amount)
            
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 每只股票分配金额: {order_amount_per_stock:.2f}元")
            
            for s in selected_stocks:
                if s in price_dict:  # 确保有价格信息
                    order_money = min(order_amount_per_stock, total_invest)
                    buy_price = get_price(ContextInfo, s)
                    # 计算实际可买数量 (确保是100的整数倍)
                    buy_volume = int(order_money / buy_price / 100) * 100
                    if buy_volume == 0 and order_money / buy_price >= 1:
                        buy_volume = 100  # 至少买100股
                    # 计算实际花费金额
                    actual_money = buy_volume * buy_price
                    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 买入 {s}, 金额: {actual_money:.2f}, 价格: {buy_price:.2f}, 数量: {buy_volume}")
                    
                    # 无最小下单金额限制
                    if buy_volume > 0:
                        # 使用数量下单，而非金额
                        passorder(23, 1101, account, s, 0, buy_price, buy_volume, '', 2, '', ContextInfo)
                        total_invest -= actual_money
                        # 记录持仓日期
                        g.position_data[s] = time.strftime('%Y%m%d')
                        save(g.position_data)
                        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 更新{s}持仓日期为: {g.position_data[s]}")
                    else:
                        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 计算买入数量为0，跳过买入")

def deal_callback(C, d):
    stock = d.m_strInstrumentID+'.'+d.m_strExchangeID
    g.position_data[stock] = time.strftime('%Y%m%d')
    save(g.position_data)
    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 交易回调: 股票={stock}, 方向={d.m_nDirection}, 价格={d.m_dPrice:.3f}, 数量={d.m_nVolume}, 更新持仓日期为: {g.position_data[stock]}")
    
def get_price(C, s):
    full = C.get_full_tick([s])
    # 动态调整买入价，避免溢价过高
    last_price = full[s]['lastPrice']
    last_close = full[s]['lastClose']
    # 涨幅越大买入价越接近市价
    if last_price/last_close - 1 > HIGH_RISE_THRESHOLD:  # 使用参数
        buy_price = last_price * (1 + HIGH_RISE_PREMIUM)  # 使用参数
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 涨幅较大({(last_price/last_close-1)*100:.2f}%>={HIGH_RISE_THRESHOLD*100}%)，买入价设为市价+{HIGH_RISE_PREMIUM*100}%: {buy_price:.3f}")
        return buy_price
    else:
        buy_price = last_price * (1 + NORMAL_PREMIUM)  # 使用参数
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 {s} 涨幅一般({(last_price/last_close-1)*100:.2f}%<{HIGH_RISE_THRESHOLD*100}%)，买入价设为市价+{NORMAL_PREMIUM*100}%: {buy_price:.3f}")
        return buy_price
    
def read_alert():
    if not os.path.exists(path):
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 预警文件不存在: {path}")
        return []

    stocks = []
    try:
        with open(path, 'r') as f:
            for line in f:
                stock = line.split('\t')[0]
                if stock.startswith('6'):
                    stock+='.SH'
                elif stock.startswith('0') or stock.startswith('3'):
                    stock+='.SZ'
                elif stock.startswith('4') or stock.startswith('8') or stock.startswith('9'):
                    stock+='.BJ'
                else:
                    print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 找不到股票市场: {stock}")
                    continue
                stocks.append(stock)
            if not 交易北交所=='是':
                stocks = [s for s in stocks if not s.endswith('BJ')]
    except Exception as e:
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 读取预警文件出错: {str(e)}")
    
    return stocks

# 打印账户资金信息
def print_account_info():
    try:
        accounts = get_trade_detail_data(account, accountType, 'account')
        if not accounts:
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 获取账户信息失败")
            return
            
        acc = accounts[0]
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 ===== 账户资金信息 =====")
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 总资产: {acc.m_dBalance:.2f}元")
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 可用资金: {acc.m_dAvailable:.2f}元")
        
        # 计算持仓市值 (总资产 - 可用资金)
        position_value = acc.m_dBalance - acc.m_dAvailable
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 持仓市值: {position_value:.2f}元")
        
        # 计算并打印持仓盈亏情况
        positions = get_trade_detail_data(account, accountType, 'POSITION')
        if positions:
            # 尝试计算总盈亏
            try:
                total_profit = sum([p.m_dProfitRate * (p.m_dLastPrice * p.m_nVolume) for p in positions if p.m_nVolume > 0])
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 持仓总盈亏: {total_profit:.2f}元")
            except:
                print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 无法计算持仓总盈亏")
                
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 持仓数量: {len([p for p in positions if p.m_nVolume > 0])}只")
            
            # 打印每只持仓股票的详细信息
            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 ----- 持仓明细 -----")
            for p in positions:
                if p.m_nVolume > 0:
                    try:
                        stock_id = f"{p.m_strInstrumentID}.{p.m_strExchangeID}"
                        profit_amount = p.m_dProfitRate * p.m_dLastPrice * p.m_nVolume
                        
                        # 使用更安全的方式获取成本价
                        cost_price = 0
                        if hasattr(p, 'm_dCostPrice'):
                            cost_price = p.m_dCostPrice
                        elif hasattr(p, 'm_dAvgPrice'):
                            cost_price = p.m_dAvgPrice
                        else:
                            # 如果没有成本价属性，尝试通过现价和盈亏率反推
                            if p.m_dProfitRate != 0:
                                cost_price = p.m_dLastPrice / (1 + p.m_dProfitRate)
                            else:
                                cost_price = p.m_dLastPrice
                        
                        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 股票: {stock_id}, 持仓量: {p.m_nVolume}, 现价: {p.m_dLastPrice:.3f}, 成本: {cost_price:.3f}, 盈亏比例: {p.m_dProfitRate*100:.2f}%, 盈亏金额: {profit_amount:.2f}元")
                    except Exception as e:
                        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 打印股票 {p.m_strInstrumentID} 信息出错: {str(e)}")
                        # 尝试打印可用的属性名称
                        try:
                            attrs = [attr for attr in dir(p) if not attr.startswith('__')]
                            print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 可用属性: {attrs}")
                        except:
                            pass
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 ======================")
    except Exception as e:
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 打印账户信息出错: {str(e)}")

# 检查并记录手动买入的持仓
def check_manual_positions(trade_day):
    try:
        positions = get_trade_detail_data(account, accountType, 'POSITION')
        if not positions:
            return
            
        today = time.strftime('%Y%m%d')
        
        for p in positions:
            if p.m_nVolume > 0:
                stock_id = f"{p.m_strInstrumentID}.{p.m_strExchangeID}"
                # 检查是否已记录该持仓
                if stock_id not in g.position_data:
                    # 如果是新发现的持仓，记录为昨日买入
                    if len(trade_day) >= 2:
                        g.position_data[stock_id] = trade_day[-2]
                        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 发现手动买入持仓: {stock_id}, 记录买入日期为: {trade_day[-2]}")
                        save(g.position_data)
                    else:
                        g.position_data[stock_id] = today
                        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 发现手动买入持仓: {stock_id}, 记录买入日期为今日: {today}")
                        save(g.position_data)
    except Exception as e:
        print(f"【{time.strftime('%Y-%m-%d %H:%M:%S')}】 检查手动买入持仓出错: {str(e)}")
















