#encoding:utf-8
"""
市值过滤测试脚本 - 用于测试股票市值过滤功能
"""
import sys
import pandas as pd
import numpy as np

class G():
    pass

# 创建全局变量对象
g = G()
g.stock_list = []
g.estimated_volume_data = {}
g.show_volume_warnings = False
g.log_file = "test_log.txt"

def check_market_cap_limit(stock_details, stock_code, limit=300):
    """
    检查股票市值是否超过限制
    
    参数:
    stock_details: 股票详情数据
    stock_code: 股票代码（用于日志）
    limit: 市值上限（亿元）
    
    返回:
    bool: True表示市值超过限制，False表示未超过
    """
    market_cap = None
    cap_source = "无数据"
    
    # 尝试不同可能的市值字段
    if 'MarketValue' in stock_details:
        market_cap = stock_details['MarketValue']
        cap_source = "MarketValue"
    elif 'TotalMarketValue' in stock_details:
        market_cap = stock_details['TotalMarketValue']
        cap_source = "TotalMarketValue"
    elif 'TotalValue' in stock_details:
        market_cap = stock_details['TotalValue']
        cap_source = "TotalValue"
    elif 'CirculationMarketValue' in stock_details:  # 流通市值
        market_cap = stock_details['CirculationMarketValue']
        cap_source = "CirculationMarketValue"
    
    # 如果找到市值信息，检查是否需要单位转换
    if market_cap is not None:
        # 单位判断和转换
        # 如果市值特别大（超过10亿），可能单位是元，需要转换为亿元
        if market_cap > 1000000000:  # 超过10亿，单位可能是元
            market_cap = market_cap / 100000000  # 转换为亿元
            print(f"股票 {stock_code} 市值数据源: {cap_source}, 原始值较大: {market_cap*100000000:.0f}, 转换为: {market_cap:.2f}亿")
        else:
            print(f"股票 {stock_code} 市值数据源: {cap_source}, 市值: {market_cap:.2f}亿")
        
        if market_cap > limit:
            print(f"过滤股票 {stock_code}: 市值 {market_cap:.2f}亿 超过 {limit}亿 限制")
            return True
    else:
        # 如果没有找到市值信息，打印警告
        print(f"警告: 无法获取 {stock_code} 的市值信息")
        # 打印所有可能的键，帮助调试
        if stock_details:
            print(f"可用字段: {', '.join(stock_details.keys())}")
    
    return False

def test_market_cap_filter():
    """
    测试市值过滤功能
    """
    # 模拟ContextInfo对象
    class MockContextInfo:
        def __init__(self):
            self.instrument_details = {}
        
        def get_instrumentdetail(self, stock_code):
            # 如果已经有股票详情，直接返回
            if stock_code in self.instrument_details:
                return self.instrument_details[stock_code]
            
            # 随机生成股票详情
            detail = {
                'InstrumentName': f"测试股票{stock_code[-4:]}",
            }
            
            # 随机生成市值数据，单位: 亿元
            market_value_type = np.random.choice(['MarketValue', 'TotalMarketValue', 'CirculationMarketValue', 'TotalValue'])
            
            # 创建不同市值范围的数据
            market_cap_groups = [
                (0, 50),      # 0-50亿
                (50, 100),    # 50-100亿
                (100, 200),   # 100-200亿
                (200, 300),   # 200-300亿
                (300, 500),   # 300-500亿
                (500, 1000)   # 500-1000亿
            ]
            
            # 随机选择一个市值组
            group_idx = np.random.randint(0, len(market_cap_groups))
            min_val, max_val = market_cap_groups[group_idx]
            market_cap = np.random.uniform(min_val, max_val)
            
            # 50%的概率将单位转换为元
            if np.random.random() < 0.5 and market_cap > 0:
                market_cap = market_cap * 100000000  # 转换为元
            
            detail[market_value_type] = market_cap
            
            # 保存到缓存中
            self.instrument_details[stock_code] = detail
            return detail

    # 创建模拟的上下文
    mock_context = MockContextInfo()
    
    # 创建测试股票列表
    test_stocks = [f"000{i}.SZ" for i in range(100, 200)] + [f"600{i}.SH" for i in range(100, 200)]
    
    # 初始化全局变量
    g.stock_list = test_stocks
    
    print("开始测试市值过滤功能...")
    print(f"初始测试股票数量: {len(test_stocks)}")
    
    # 只运行市值过滤相关代码
    stocks_after_market_cap_filter = []
    filtered_by_market_cap = 0
    
    # 调试用：收集市值信息
    market_cap_info = []
    market_cap_distribution = {
        "0-50亿": 0,
        "50-100亿": 0,
        "100-200亿": 0,
        "200-300亿": 0,
        "300-500亿": 0,
        "500亿以上": 0,
        "无市值数据": 0
    }
    
    for s in g.stock_list:
        try:
            # 获取股票详情
            detail = mock_context.get_instrumentdetail(s)
            if detail is None:
                continue
            
            # 收集市值信息用于调试
            try:
                name = detail.get('InstrumentName', s)
                cap = None
                cap_source = "无数据"
                
                if 'MarketValue' in detail:
                    cap = detail['MarketValue']
                    cap_source = "MarketValue"
                elif 'TotalMarketValue' in detail:
                    cap = detail['TotalMarketValue']
                    cap_source = "TotalMarketValue"
                elif 'TotalValue' in detail:
                    cap = detail['TotalValue']
                    cap_source = "TotalValue"
                elif 'CirculationMarketValue' in detail:
                    cap = detail['CirculationMarketValue']
                    cap_source = "CirculationMarketValue"
                
                if cap is not None:
                    if cap > 1000000000:  # 可能是元为单位
                        cap = cap / 100000000  # 转换为亿元
                    
                    market_cap_info.append((s, name, cap, cap_source))
                    
                    # 统计市值分布
                    if cap < 50:
                        market_cap_distribution["0-50亿"] += 1
                    elif cap < 100:
                        market_cap_distribution["50-100亿"] += 1
                    elif cap < 200:
                        market_cap_distribution["100-200亿"] += 1
                    elif cap < 300:
                        market_cap_distribution["200-300亿"] += 1
                    elif cap < 500:
                        market_cap_distribution["300-500亿"] += 1
                    else:
                        market_cap_distribution["500亿以上"] += 1
                else:
                    market_cap_distribution["无市值数据"] += 1
            except Exception as e:
                print(f"收集{s}的市值信息时出错: {str(e)}")
                market_cap_distribution["无市值数据"] += 1
                
            # 检查市值是否超过限制
            if check_market_cap_limit(detail, s, 300):
                filtered_by_market_cap += 1
                continue
                
            stocks_after_market_cap_filter.append(s)
        except Exception as e:
            print(f"处理{s}的市值信息时出错: {str(e)}")
            # 出错时保留股票
            stocks_after_market_cap_filter.append(s)
    
    # 打印市值分布统计
    print("\n====== 市值分布统计 ======")
    for range_name, count in market_cap_distribution.items():
        print(f"{range_name}: {count}只股票")
    
    # 打印前十个最大市值股票和前十个最小市值股票
    if market_cap_info:
        market_cap_info.sort(key=lambda x: x[2] if x[2] is not None else 0, reverse=True)
        
        print("\n====== 市值最大的10只股票 ======")
        for i, (code, name, cap, source) in enumerate(market_cap_info[:10]):
            print(f"{i+1}. {code} {name}: {cap:.2f}亿 (数据来源: {source})")
        
        print("\n====== 市值最小的10只股票 ======")
        for i, (code, name, cap, source) in enumerate(market_cap_info[-10:]):
            print(f"{i+1}. {code} {name}: {cap:.2f}亿 (数据来源: {source})")
    
    print(f"过滤市值大于300亿的股票数量: {filtered_by_market_cap}")
    print(f"市值过滤后股票池数量: {len(stocks_after_market_cap_filter)}")

# 程序入口
if __name__ == "__main__":
    test_market_cap_filter() 