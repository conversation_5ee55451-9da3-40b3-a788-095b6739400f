#encoding:gbk

# ================= 策略参数设置 =================
# 开仓参数
open_long_num = 2        # 最大多头开仓次数
open_short_num = 2       # 最大空头开仓次数
hand = 2                 # 交易手数
sleep_time = 5           # 分钟，同方向连续开仓的最小时间间隔

# 入场条件参数
REQUIRE_TOUCH_RED_LINE = False  # 是否要求价格必须触及红线才能做多（否=不要求，是=必须触及）
REQUIRE_TOUCH_WHITE_LINE = False  # 是否要求价格必须触及白线才能做空（否=不要求，是=必须触及）
DAY_AVG_FILTER = False    # 是否启用日线均值过滤，True=启用，False=禁用
                         # 做多时要求价格高于日均值，做空时要求价格低于日均值
DAY_AVG_STRICT = False   # 日线均值严格模式，True=必须满足日均值条件，False=日均值仅作为参考

# ==================== 止盈止损参数（重要）==================== #
# 【期权买方】止损止盈说明：
# 对于期权买方(权利仓)：
# - 期权价格上涨时盈利，价格下跌时亏损！
# - 止损：期权价格下跌超过设定百分比(负值)时，分批平仓控制风险
# - 止盈：期权价格上涨超过设定百分比(正值)时，分批平仓锁定利润

# 止盈参数（期权价格上涨=盈利）
option_buyer_profit_1 = 5      # % 第一次止盈比例：期权价格上涨超过5%时平掉1/3仓位(盈利)
option_buyer_profit_2 = 10     # % 第二次止盈比例：期权价格上涨超过10%时平掉1/3仓位(盈利)
option_buyer_profit_3 = 15     # % 第三次止盈比例：期权价格上涨超过15%时平掉剩余仓位(盈利)

# 止损参数（期权价格下跌=亏损）
option_buyer_stop_loss_1 = -5   # % 第一次止损比例：期权价格下跌超过5%时平掉1/3仓位(亏损)
option_buyer_stop_loss_2 = -10  # % 第二次止损比例：期权价格下跌超过10%时平掉1/3仓位(亏损)
option_buyer_stop_loss_3 = -25  # % 第三次止损比例：期权价格下跌超过25%时平掉剩余仓位(亏损)

switch_days_before_expiry = 15  # 期权到期前多少天换月

# 移动止损参数
moving_tick = 0.020      # 移动止损点位，期权波动单位为0.0001
fs_line = 0.010          # FS即时价位—分时均线价位的最大差值

# 分时均线价格过滤参数
B1_sample_period = 20  # 计算B1平均值和标准差的周期
B1_std_multiplier = 1.5  # B1标准差倍数
volume_check_ratio = 1.2  # 成交量放大倍数
atr_period = 14  # 计算ATR的周期
atr_threshold = 1.5  # ATR阈值倍数
ma_periods = [5, 10]  # 多周期均线

# 红白线距离过滤参数 
RED_LINE_DISTANCE_THRESHOLD = 0.0012   # 红线距离阈值(0.3%)
WHITE_LINE_DISTANCE_THRESHOLD = 0.0012  # 白线距离阈值(0.3%)

# 订单管理参数
ORDER_TIMEOUT = 30       # 订单超时时间(秒)

# 成交量阈值
VOLUME_THRESHOLD = 1.1  # 成交量阈值（1.1表示增加10%）

# 期权换月参数
OPTION_ROLL_DAYS = 5   # 降低到5天，避免过早切换

# 期权合约选择参数
OPTION_TYPE = "ITM"      # 期权类型选择: "ITM"=实值期权, "OTM"=虚值期权, "ATM"=平值期权
OPTION_LEVEL = 1         # 期权档位: 1=一档, 2=二档, 3=三档
OPTION_DELTA_MIN = 0.2   # 最小Delta要求(0-1之间)
OPTION_DELTA_MAX = 0.8   # 最大Delta要求(0-1之间)
AUTO_CONTRACT_SELECT = True  # 是否启用自动合约选择功能
VOLATILITY_WINDOW = 20   # 波动率计算窗口(K线数量)
VOLATILITY_LOW = 0.5     # 低波动阈值(%)
VOLATILITY_HIGH = 1.5    # 高波动阈值(%)

# 策略阈值参数
EFFECTIVE_BREAK_THRESHOLD = 0.002  # 有效突破阈值(0.2%)

# 策略控制参数
REQUIRE_TOUCH_RED_LINE = False  # 是否要求价格必须触及红线才能确认"回调到红线做多"
REQUIRE_TOUCH_WHITE_LINE = False  # 是否要求价格必须触及白线才能确认"反弹到白线做空"

# 时间过滤参数
time_filter2 = True      # 通用时间过滤开关，控制所有交易信号（做多和做空）
                         # 作用：当设为False时，会禁用所有交易信号，包括做多和做空
                         # 影响：直接控制kaiduo_normal、kaiduo_fast、kaikong_normal和kaikong_fast条件
                         # 调整建议：在异常波动市场、开盘前30分钟或收盘前10分钟可设为False以避免风险

time_filter_break = True # 突破信号专用时间过滤开关，控制所有突破类信号（做多和做空）
                         # 作用：当设为False时，会禁用所有突破类信号，包括"有效突破白线做多"和"有效跌破红线做空"
                         # 影响：只影响kaiduo_fast和kaikong_fast条件中的突破部分，不影响常规反弹信号
                         # 调整建议：在震荡市场中可设为False以减少假突破带来的损失，保留其他交易信号

# 市场状态判断
market_condition = "normal"  # 市场状态: "normal"=正常, "volatile"=波动剧烈, "flat"=盘整
                             # "抄底"=抄底模式，在该模式下不触发做空信号
AUTO_MARKET_CONDITION = True # 是否启用市场状态自动判断，False=手动设置，True=自动判断

# ================= 以下为策略代码 =================

import math
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta


class G():
	pass


g = G()
g.buy_long = 0
g.buy_short = 0
g.hold = 0
g.hold_price = 0
g.open_price = 0
g.trace_time_long = 0
g.trace_time_short = 0
g.opened_t = []
g.hold_code = ''
g.curr_hold = None
g.closed_ratio = 0  # 已平仓的比例：0, 0.33, 0.67, 1
g.remaining_hands = hand      # 追踪剩余持仓手数
g.order_submit_time = {}  # 记录订单提交时间
g.order_timeout = ORDER_TIMEOUT  # 订单超时时间(秒)

def init(ContextInfo):
	g.remark = ContextInfo.request_id[-10:]
	g.call_one = None
	g.put_one = None
	ContextInfo.set_account(account)
	g.undl_code = g.code = g.stock = ContextInfo.stockcode+'.'+ContextInfo.market
	g.curr_hold = None
	
	# 重置分批平仓相关状态
	g.closed_ratio = 0  # 初始化已平仓比例
	g.remaining_hands = 0  # 初始应为0，而不是hand
	g.last_profit_take_time = 0
	g.profit_take_orders = []
	g.order_submit_time = {}  # 确保订单时间字典被初始化


def after_init(ContextInfo):
	download_history_data(g.code,'1d','********','********')
	
	# 增强持仓同步代码
	print("开始同步持仓状态...")
	account_to_use = account
	
	try:
		# 同步策略持仓状态与实际账户持仓
		positions = get_positions(account_to_use)
		print(f"获取到 {len(positions)} 个持仓")
		
		found_option_position = False  # 标记是否找到期权持仓
		
		for pos in positions:
			try:
				if hasattr(pos, 'symbol') and pos.symbol:
					print(f"检查持仓: {pos.symbol}, 数量: {pos.current if hasattr(pos, 'current') else '未知'}")
					
					# 检查是否是期权持仓 (10位以上的代码一般是期权)
					if len(pos.symbol.split('.')[0]) >= 8:
						found_option_position = True
						print(f"找到期权持仓: {pos.symbol}, 方向: {pos.side if hasattr(pos, 'side') else '未知'}")
						
						# 设置持仓变量
						g.curr_hold = pos.symbol
						g.hold = 1 if hasattr(pos, 'side') and pos.side > 0 else (
                                -1 if hasattr(pos, 'side') and pos.side < 0 else 1)  # 默认多头
						g.remaining_hands = pos.current if hasattr(pos, 'current') else hand
						
						# 更新call_one/put_one
						if g.hold == 1:
							g.call_one = pos.symbol
							print(f"设置call_one = {g.call_one}")
						else:
							g.put_one = pos.symbol
							print(f"设置put_one = {g.put_one}")
						
						# 设置开仓价格
						if hasattr(pos, 'cost') and pos.cost > 0:
							g.open_price = pos.cost
						else:
							# 尝试获取当前市场价格作为备用
							try:
								g.open_price = get_option_price(pos.symbol)
								print(f"使用市场价格作为开仓价: {g.open_price}")
							except:
								g.open_price = 0.05  # 使用默认价格
						
						# 根据已有持仓计算g.closed_ratio
						if g.remaining_hands < hand:
							g.closed_ratio = round(1 - (g.remaining_hands / hand), 2)
							print(f"根据剩余手数计算已平仓比例: {g.closed_ratio}")
						else:
							g.closed_ratio = 0
						
						print(f"已同步持仓: 代码={g.curr_hold}, 数量={g.remaining_hands}手, 成本价={g.open_price}, 已平仓比例={g.closed_ratio}")
						break
			except Exception as e:
				print(f"处理单个持仓时出错: {e}")
				continue
		
		# 如果没有找到期权持仓，确保状态干净
		if not found_option_position:
			print("未找到期权持仓，重置持仓状态")
			g.hold = 0
			g.curr_hold = None
			g.remaining_hands = 0
			g.open_price = 0
			g.closed_ratio = 0
	except Exception as e:
		print(f"同步持仓状态异常: {e}")
	
	return ContextInfo.get_trading_dates('SHO','','20990101',count=2, period='1d')

def handlebar(ContextInfo):
	"""
	处理市场数据更新的主函数
	"""
	timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
	bar_date = timetag_to_datetime(timetag, '%Y%m%d%H%M%S')
	
	try:
		# 声明使用全局变量
		global market_condition
		
		# 删除不存在的方法调用
		# code = ContextInfo.get_code()  # 这行有问题
		# current_time = ContextInfo.get_current_time()  # 这行有问题
		
		# 直接使用g.code代替
		code = g.code
		# 使用bar_date作为时间信息
		current_time = bar_date
		
		if not ContextInfo.is_last_bar():
			return
			
		price_1d=ContextInfo.get_market_data_ex(['close','open'],[g.code],
								period='1d',count=2,subscribe=False)[g.code]
		if price_1d.index[-1]==bar_date[:8]:
			CC = price_1d.iloc[-2]['close']
			OO = price_1d.iloc[-2]['open']
		else:
			CC = price_1d.iloc[-1]['close']
			OO = price_1d.iloc[-1]['open']
		
		start, end = after_init(ContextInfo)
		
		price = ContextInfo.get_market_data_ex(['close','amount','volume','high','low'],[g.code],period='1m',
						start_time=end+'093000',
						end_time=end+'150000',
						)[g.code]
		C = CLOSE = price['close']
		price_len = price.shape[0]
		if price_len<=20:
			return
		t = price.index[-1][-6:-2]
		if t<='0930':# 9：56开始执行策略
			return
		CG,FL,MA_Line,FS,B1,B2 = cal_vba(ContextInfo, price)
		first, last_time = get_shape(CG,FL,MA_Line,FS,B1,B2)
		pct = time.strftime("%H%M%S")
		b1 = not pct >='145400' # 收盘前10分钟清仓
		if any([math.isnan(CG[i]) for i in [-1,-2,-3,-4,-5]] ):
			return
			
		# 移除常规市场数据的输出，只在调试模式下显示
		debug_mode = False  # 设为True时会显示详细市场数据
		if debug_mode:
			print(f'{g.code} {t} c:{C[-1]} 均线：{MA_Line[-1]} B1:{B1[-3:]} FS:{FS[-1]} 昨CC:{CC} 昨OO:{OO} ')
		
		last_five_minute_long = all([CG[i]==FL[i]==FS[i] for i in (-1,)])
		ContextInfo.paint('B1',B1[-1],-1,0,)
		ContextInfo.paint('B2',B2[-1],-1,0,)
		last_five_minute_short = all([CG[i]!=FL[i] and CG[i]!=FS[i] and FL[i]!=FS[i] for i in (-1,)])
		
		# 获取当前价格
		current_price = C[-1]
		
		# 获取5分钟K线数据
		price_5m = ContextInfo.get_market_data_ex(['open', 'high', 'low', 'close', 'volume'], 
												[g.code], 
												period='5m',
												count=60)[g.code]
		
		# 调试模式开关
		hl_diagnosis_mode = False  # 设为True将启用H1/L1诊断
		
		# 在正常计算红白线之前，提供手动诊断入口
		if hl_diagnosis_mode:
			print("\n" + "="*50)
			print("[###] 手动触发H1/L1诊断 [###]")
			print("="*50)
			diagnose_hl_calculation(price_5m, debug_mode=True)
			print("继续正常策略执行...")
		
		# 使用文华指标源代码计算红白线
		white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
		
		# 只使用5分钟K线计算的红白线值
		if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
			final_red_line = red_line_5m.iloc[-1]
			final_white_line = None  # 如果有红线，则不使用白线
			print(f"白线: - | 红线: {final_red_line:.4f}")
		else:
			final_red_line = None
			final_white_line = white_line_5m.iloc[-1] if not pd.isna(white_line_5m.iloc[-1]) else None
			# 修复格式化问题
			white_line_str = f"{final_white_line:.4f}" if final_white_line is not None else "-"
			print(f"白线: {white_line_str} | 红线: -")
		
		# 计算成交量加权均线
		MA_Line_5m = calculate_ma(price_5m['close'], price_5m['volume'], 55)
		
		# 自动判断市场状态
		if AUTO_MARKET_CONDITION:
			new_market_condition = detect_market_condition(price_5m, MA_Line_5m.iloc[-1])
			if new_market_condition != market_condition:
				print(f"市场状态变更: {market_condition} → {new_market_condition}")
				market_condition = new_market_condition
		
		# 输出当前市场状态
		print(f"市场状态: {market_condition} | 允许做空: {'否' if market_condition == '抄底' else '是'}")
		
		# 初始化交易信号变量
		break_white_long = False    # 突破白线做多
		break_red_short = False     # 跌破红线做空
		pullback_long_good = False  # 回调到红线做多
		pullback_short_good = False # 反弹到白线做空
		red_support_long = False    # 红线支撑做多
		
		# 计算趋势
		trend_5m = 0
		trend_result = check_trend(price_5m, MA_Line_5m.iloc[-1])
		if trend_result == "up":
			trend_5m = 1
		elif trend_result == "down":
			trend_5m = -1
		
		# 检测红白线转换
		transition_signal = detect_line_transition(
			white_line_5m.iloc[-2] if len(white_line_5m) > 1 else None,
			white_line_5m.iloc[-1],
			red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None,
			red_line_5m.iloc[-1]
		)
		
		# 转换信号数值化
		transition_signal_num = 0
		if transition_signal == "white_to_red":
			transition_signal_num = 1
		elif transition_signal == "red_to_white":
			transition_signal_num = -1
			# 红线转白线是重要做空信号，添加处理
			if final_white_line is not None and not pd.isna(final_white_line):
				if current_price < final_white_line:
					print("\n=== 关键交易信号 ===")
					print(f"检测到价格跌破红线支撑转为白线压力：价格{current_price} < 白线{final_white_line}")
					print("这是重要的空头信号！")
				else:
					print(f"虽然红线转白线，但价格{current_price} > 白线{final_white_line}，信号较弱")
		
		# 获取转换信号的交易建议
		transition_long, transition_short = False, False

		# 根据转换类型设置信号
		if transition_signal == "white_to_red":
			transition_long = True
			print("白线转红线 => 多头信号")
		elif transition_signal == "red_to_white":
			transition_short = True
			print("红线转白线 => 空头信号")
		
		# 检查是否跌破红线
		break_red, price_red_ratio = check_break_red_line(price_5m, 
							   red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None, 
							   red_line_5m.iloc[-1])
		if break_red:
			break_red_short = True
			print(f"检测到跌破红线做空信号")
		else:
			# 确保break_red为False时，break_red_short也是False
			break_red_short = False
			# 如果红线不存在，检查是否满足"反弹到白线做空"的条件
			if final_white_line is not None and not pd.isna(final_white_line) and current_price < final_white_line:
				print(f"红线不存在，但满足白线反弹做空条件，价格{current_price} < 白线{final_white_line}")
		
		# 检查是否突破白线
		if final_white_line is not None and not pd.isna(final_white_line):
			if current_price > final_white_line:
				break_white_long = True
				print(f"检测到突破白线: 价格{current_price} > 白线{final_white_line}")
		
		# 检查是否回调到红线做多 (合并回调到红线和红线支撑的逻辑)
		if final_red_line is not None and not pd.isna(final_red_line):
			# 检查是否处于转换期（前一根是白线，当前是红线）
			is_transition_period = (white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]) and
								  final_white_line is None and
								  final_red_line is not None)
			
			# 检查价格是否曾经接近或触及红线
			price_touched_red = ((not REQUIRE_TOUCH_RED_LINE) or price_5m['low'].iloc[-1] <= final_red_line)
			
			# 检查当前价格是否在红线上方
			price_above_red = current_price > final_red_line
			
			# 检查成交量是否增加
			volume_increased = check_volume_increase(price_5m['volume'])
			
			# 综合判断：趋势向上 + 不在转换期 + 价格曾接近红线 + 现在在红线上方
			if trend_5m == 1 and not is_transition_period and price_touched_red and price_above_red:
				pullback_long_good = True
				support_msg = "且成交量增加" if volume_increased else "但成交量未增加"
				touch_msg = "触及" if price_5m['low'].iloc[-1] <= final_red_line else "接近但未触及"
				print(f"检测到红线支撑做多信号: 最低价{price_5m['low'].iloc[-1]} {touch_msg}红线{final_red_line}, "
					  f"收盘价{current_price} > 红线{final_red_line}, {support_msg}")
				
				# 成交量增加会增强信号强度，但不作为必要条件
				if volume_increased:
					print("成交量增加，强化做多信号")
			
			# 移除单独的红线支撑做多逻辑，已合并到上面
			red_support_long = False
		
		# 检查是否反弹到白线做空
		if final_white_line is not None and not pd.isna(final_white_line):
			# 修改条件判断，根据参数决定是否要求价格触及白线
			if ((not REQUIRE_TOUCH_WHITE_LINE) or price_5m['high'].iloc[-1] >= final_white_line) and current_price < final_white_line:
				pullback_short_good = True
				touch_msg = "触及" if price_5m['high'].iloc[-1] >= final_white_line else "接近但未触及"
				print(f"检测到反弹到白线做空: 最高价{price_5m['high'].iloc[-1]} {touch_msg}白线{final_white_line}, 收盘价{current_price} < 白线{final_white_line}")
		
		# =============== 新的开仓条件 ===============
		# 定义核心信号变量
		effective_break_white = (break_white_long and 
							  final_white_line is not None and not pd.isna(final_white_line) and 
							  current_price > final_white_line * (1 + EFFECTIVE_BREAK_THRESHOLD))  # 有效突破白线
		
		# 修改有效跌破红线判断逻辑，确保红线存在
		effective_break_red = (break_red_short and 
							final_red_line is not None and not pd.isna(final_red_line) and 
							current_price < final_red_line * (1 - EFFECTIVE_BREAK_THRESHOLD))  # 有效跌破红线

		# 紧跟破红线的红转白是强力空头信号，单独增加标记 - 提前初始化该变量
		red_to_white_strong_short_without_market_check = transition_signal == "red_to_white" and trend_5m == -1 and current_price < MA_Line_5m.iloc[-1]
		red_to_white_strong_short = red_to_white_strong_short_without_market_check and market_condition != "抄底"

		# 输出当前信号状态，帮助调试
		print("\n● 信号状态:")
		active_signals = []
		if effective_break_white: active_signals.append("有效突破白线")
		if effective_break_red: active_signals.append("有效跌破红线")
		if pullback_short_good: active_signals.append("反弹到白线做空")
		if pullback_long_good: active_signals.append("红线支撑做多")
		if transition_long: active_signals.append("白转红做多")
		if transition_short: active_signals.append("红转白做空")
		if red_to_white_strong_short: active_signals.append("强力红转白做空")
		
		if active_signals:
			print("  " + " | ".join(active_signals))
		else:
			print("  无活跃信号")

		# 原有复杂条件逻辑保留
		kaiduo_normal = ((pullback_long_good or 
					   transition_long) and 
					   current_price > MA_Line_5m.iloc[-1] and 
					   g.buy_long < open_long_num and 
					   b1 and 
					   trend_5m == 1 and
					   time_filter2)
		
		# 添加日线均值过滤条件
		if DAY_AVG_FILTER:
			day_avg = (price_1d.iloc[0]['close']+price_1d.iloc[0]['open'])/2
			day_avg_condition = C[-1] > day_avg  # 做多条件：价格高于日均值
			# 根据严格模式决定是否强制过滤
			if DAY_AVG_STRICT:
				kaiduo_normal = kaiduo_normal and day_avg_condition
			else:
				# 仅在日志中提示，不强制过滤
				if not day_avg_condition:
					print("● 提示: 价格低于日均值，但仍允许做多信号")

		kaikong_normal = ((
			# break_red_short or
			pullback_short_good or
			transition_short
		) and trend_5m == -1 and b1 and market_condition != "抄底" and time_filter2)
		
		# 添加日线均值过滤条件(对称设置)
		if DAY_AVG_FILTER:
			day_avg = (price_1d.iloc[0]['close']+price_1d.iloc[0]['open'])/2
			day_avg_condition = C[-1] < day_avg  # 做空条件：价格低于日均值
			# 根据严格模式决定是否强制过滤
			if DAY_AVG_STRICT:
				kaikong_normal = kaikong_normal and day_avg_condition
			else:
				# 仅在日志中提示，不强制过滤
				if not day_avg_condition:
					print("● 提示: 价格高于日均值，但仍允许做空信号")

		# 新增简化条件：有效突破/跌破情况下的快速开仓条件
		kaiduo_fast = (effective_break_white and time_filter_break) and g.buy_long < open_long_num and b1 and time_filter2
		kaikong_fast = (
			((effective_break_red and time_filter_break) or (transition_signal == "red_to_white" and time_filter_break))
			and trend_5m == -1 and b1 and time_filter2 and market_condition != "抄底"
		)
		
		# 添加日线均值过滤条件到快速开仓信号
		if DAY_AVG_FILTER:
			day_avg = (price_1d.iloc[0]['close']+price_1d.iloc[0]['open'])/2
			kaiduo_fast = kaiduo_fast and C[-1] > day_avg
			kaikong_fast = kaikong_fast and C[-1] < day_avg
			
			# 在打印日志中添加日线均值信息
			print(f"\n=== 日线均值过滤 ===")
			print(f"当前价格: {C[-1]:.4f}")
			print(f"日线均值: {day_avg:.4f}")
			print(f"关系: {'价格 > 日均值' if C[-1] > day_avg else '价格 < 日均值'}")
			print(f"适合方向: {'做多' if C[-1] > day_avg else '做空'}")

		# 最终开仓条件
		kaiduo = kaiduo_normal or kaiduo_fast

		# 显示红转白强力信号的分析
		if red_to_white_strong_short_without_market_check:
			if market_condition == "抄底":
				print("● 警告: 检测到强力空头信号，但当前市场状态为抄底，此信号已被禁止")
			else:
				print("● 高优先级: 红线转白线 + 下降趋势 + 价格低于均线 = 强力空头信号")

		if red_to_white_strong_short:
			print("\n=== 高优先级空头信号 ===")
			print("红线转白线 + 下降趋势 + 价格低于均线：强烈做空信号")
		
		# 修改空头条件，确保当红线存在时，正确处理红转白信号
		if final_red_line is None and final_white_line is not None:
			# 如果没有红线但有白线，检查是否可能是红转白的情况
			kaikong = ((pullback_short_good or transition_short or red_to_white_strong_short) and 
					  current_price < MA_Line_5m.iloc[-1] and 
					  g.buy_short < open_short_num and 
					  b1 and
					  trend_5m == -1 and
					  market_condition != "抄底")
		else:
			# 有红线或者两条线都没有的情况
			kaikong = (kaikong_normal or kaikong_fast or red_to_white_strong_short)

		# 为了更清晰地显示决策过程，输出关键条件状态（精简版）
		key_short_signals = []
		if transition_signal == "red_to_white": key_short_signals.append("红转白")
		if red_to_white_strong_short: key_short_signals.append("强力红转白")
		if pullback_short_good: key_short_signals.append("反弹白线") 
		if break_red_short: key_short_signals.append("跌破红线")
		
		if key_short_signals:
			print(f"● 做空信号: {' + '.join(key_short_signals)}")

		# 强制检查交易时机：如果是关键的红转白信号，应该高度重视
		if transition_signal == "red_to_white" and current_price < MA_Line_5m.iloc[-1]:
			if final_white_line is not None and current_price < final_white_line:
				print("● 交易机会: K线跌破红线支撑，支撑转压力，价格在白线下方")
				# 确保在满足核心条件的情况下能够生成交易信号
				if trend_5m == -1 and g.buy_short < open_short_num and b1 and market_condition != "抄底":
					kaikong = True
					print("  ? 满足所有核心条件，准备开空！")

		# 如果是快速开仓信号，记录日志
		if kaiduo_fast and not kaiduo_normal:
			print("● 强信号: 有效突破白线，准备开多")
			
		if kaikong_fast and not kaikong_normal:
			print("● 强信号: 有效跌破红线，准备开空")
		
		# 为明确的跌破红线情况单独设置条件
		if break_red_short and current_price < MA_Line_5m.iloc[-1] and trend_5m == -1:
			print("● 信号: 明确的跌破红线做空")
			# 可以考虑放宽一些辅助条件
			if market_condition != "抄底":
				kaikong = True
				print("  ? 市场状态允许做空")
			else:
				print("  ? 市场状态限制：当前抄底环境，禁止做空")
		
		# 是否可以开仓
		can_open_position = True  # 默认可以开仓
		
		# 增加价格与红白线距离的过滤条件
		white_line_value = final_white_line if not pd.isna(final_white_line) else None
		red_line_value = final_red_line if not pd.isna(final_red_line) else None

		red_line_distance_ok, white_line_distance_ok = check_and_print_line_distance(
			current_price, 
			 white_line_value,
			 red_line_value
		)
		print(f"使用的白线值: {white_line_value}, 红线值: {red_line_value}")
		
		# 根据距离检查结果修改开仓条件
		if (pullback_long_good or transition_long) and not red_line_distance_ok:
			print("● 过滤: 因价格与红线距离过大，取消做多信号")
			kaiduo = False
			
		if (pullback_short_good or transition_short) and not white_line_distance_ok:
			print("● 过滤: 因价格与白线距离过大，取消做空信号")
			kaikong = False
		
		# 打印开仓条件简化版
		print(f"\n● 开仓决策:")
		print(f"  做多: {'是' if kaiduo else '否'} | 做空: {'是' if kaikong else '否'}")
		
		# 添加市场状态对最终做空决策的明确提示
		if not kaikong and market_condition == "抄底" and (break_red_short or pullback_short_good or transition_short or transition_signal == "red_to_white"):
			print("  ? 市场状态为抄底，潜在做空信号被禁止")
		
		# 保留原有代码的后续执行部分
		if g.hold == 0 and b1 and g.buy_long < open_long_num and kaiduo:
			buy_long = True
		else:
			buy_long = False
		if g.hold == 0 and b1 and g.buy_short < open_short_num and kaikong:
			buy_short = True
		else:
			buy_short = False

		if buy_long and time.time()-g.trace_time_long>sleep_time*60 and t not in g.opened_t and g.hold !=1:
			if g.hold == -1:
				passorder(51, 1101, account, g.put_one, 12, 0, hand, '',1,'策略平空'+g.remark,ContextInfo)
			call_one, put_one = get_option_real_one(ContextInfo)
			g.call_one = call_one
			g.buy_long+=1
			passorder(50, 1101, account, call_one, 12,0, hand,'',1,'期权策略开多'+g.remark,ContextInfo)
			g.curr_hold = call_one
			g.hold = 1
			g.trace_time_long = time.time()
			g.hold_price = 0
			g.open_price = 0
			g.opened_t.append(t)
			g.hold_code = call_one
			g.remaining_hands = hand  # 设置初始剩余手数
			# 重置止盈标记
			g.profit_taken_1 = False
			g.profit_taken_2 = False
			g.profit_taken_3 = False
			g.profit_take_orders = []  # 清空止盈订单跟踪列表
			g.last_profit_take_time = 0  # 重置最近一次止盈时间
			print(f'{call_one} 开多 {transition_signal}, 手数:{hand}')

		if buy_short and time.time()-g.trace_time_short>sleep_time*60 and t not in g.opened_t and g.hold !=-1:
			if g.hold == 1:
				passorder(51, 1101, account, g.call_one, 12, 0, hand, '',1,'策略平多'+g.remark,ContextInfo)
			call_one, put_one = get_option_real_one(ContextInfo)
			g.put_one = put_one
			passorder(50, 1101, account, put_one, 12,0, hand,'',1,'期权策略开空'+g.remark,ContextInfo)
			print(f'{g.put_one} 开空 {transition_signal}, 手数:{hand}')
			g.curr_hold = put_one
			g.buy_short+=1
			g.opened_t.append(t)
			g.hold_price = 9999999
			g.open_price = 0
			g.hold = -1
			g.hold_code = put_one
			g.trace_time_short = time.time()
			g.remaining_hands = hand  # 设置初始剩余手数
			# 重置止盈标记
			g.profit_taken_1_short = False
			g.profit_taken_2_short = False
			g.profit_taken_3_short = False
			g.profit_take_orders = []  # 清空止盈订单跟踪列表
			g.last_profit_take_time = 0  # 重置最近一次止盈时间

		# =============== 持仓管理部分 ===============
		if g.hold != 0 and g.open_price > 0:
			try:
				full = ContextInfo.get_full_tick([g.curr_hold])[g.curr_hold]
				if 'lastPrice' in full and full['lastPrice'] > 0:
					c = full['lastPrice']
				else:
					print(f"警告: 无法获取合约{g.curr_hold}的有效价格，使用默认值0")
					c = 0
			except Exception as e:
				print(f"获取期权价格异常: {e}，使用默认值0")
				c = 0
				
			# 确保在使用hold_ratio前先定义它
			if c > 0 and g.open_price > 0:
				hold_ratio = round((c/g.open_price-1)*100, 2)
				
				if g.hold > 0:
					g.hold_price = max(c, g.hold_price)
					print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][多头][{g.curr_hold}] 成本价:{g.open_price:.4f} 最新价:{c:.4f} 盈亏比例:{hold_ratio:.2f}% 已平仓比例:{g.closed_ratio:.2f}')
				elif g.hold < 0:
					g.hold_price = max(c, g.hold_price)
					print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][空头][{g.curr_hold}] 成本价:{g.open_price:.4f} 最新价:{c:.4f} 盈亏比例:{hold_ratio:.2f}% 已平仓比例:{g.closed_ratio:.2f}')
				
				# 期权卖方止盈止损逻辑：期权价格下跌是盈利，上涨是亏损
				if g.hold == 1:  # 多头持仓（卖出认购期权）
					g.hold_price = max(g.hold_price, c)
					
					# 分批止损：期权价格上涨超过不同阈值时分批平仓
					if g.closed_ratio == 0 and hold_ratio >= option_buyer_stop_loss_1 and hold_ratio < option_buyer_stop_loss_2:
						# 第一次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1][多头] 期权价格上涨{hold_ratio:.2f}%，触发第一次止损条件{option_buyer_stop_loss_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, first_hand, '',1,'平多止损1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1委托][多头] {g.call_one} 平多止损1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio >= option_buyer_stop_loss_2 and hold_ratio < option_buyer_stop_loss_3:
						# 第二次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2][多头] 期权价格上涨{hold_ratio:.2f}%，触发第二次止损条件{option_seller_stop_loss_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, second_hand, '',1,'平多止损2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2委托][多头] {g.call_one} 平多止损2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio >= option_seller_stop_loss_3:
						# 第三次止损，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3][多头] 期权价格上涨{hold_ratio:.2f}%，触发第三次止损条件{option_seller_stop_loss_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.call_one, 12, 0, remain_hand, '',1,'平多止损3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3委托][多头] {g.call_one} 平多止损3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.closed_ratio = 1.0
						g.remaining_hands = 0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][多头] 所有仓位已平仓')
					
					# 分批止盈：期权价格下跌超过不同阈值时分批平仓
					elif g.closed_ratio == 0 and hold_ratio <= option_seller_profit_1 and hold_ratio > option_seller_profit_2:
						# 第一次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第一次止盈条件{-option_seller_profit_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, first_hand, '',1,'平多止盈1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1委托][多头] {g.call_one} 平多止盈1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio <= option_seller_profit_2 and hold_ratio > option_seller_profit_3:
						# 第二次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第二次止盈条件{-option_seller_profit_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, second_hand, '',1,'平多止盈2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2委托][多头] {g.call_one} 平多止盈2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio <= option_seller_profit_3:
						# 第三次止盈，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第三次止盈条件{-option_seller_profit_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.call_one, 12, 0, remain_hand, '',1,'平多止盈3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3委托][多头] {g.call_one} 平多止盈3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.closed_ratio = 1.0
						g.remaining_hands = 0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][多头] 所有仓位已平仓')
					
				if g.hold == -1:  # 空头持仓（卖出认沽期权）
					g.hold_price = max(g.hold_price, c)
					
					# 分批止损：期权价格上涨超过不同阈值时分批平仓
					if g.closed_ratio == 0 and hold_ratio >= option_seller_stop_loss_1 and hold_ratio < option_seller_stop_loss_2:
						# 第一次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1][空头] 期权价格上涨{hold_ratio:.2f}%，触发第一次止损条件{option_seller_stop_loss_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, first_hand, '',1,'平空止损1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1委托][空头] {g.put_one} 平空止损1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio >= option_seller_stop_loss_2 and hold_ratio < option_seller_stop_loss_3:
						# 第二次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2][空头] 期权价格上涨{hold_ratio:.2f}%，触发第二次止损条件{option_seller_stop_loss_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, second_hand, '',1,'平空止损2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2委托][空头] {g.put_one} 平空止损2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio >= option_seller_stop_loss_3:
						# 第三次止损，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3][空头] 期权价格上涨{hold_ratio:.2f}%，触发第三次止损条件{option_seller_stop_loss_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.put_one, 12, 0, remain_hand, '',1,'平空止损3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3委托][空头] {g.put_one} 平空止损3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.remaining_hands = 0
						g.closed_ratio = 1.0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][空头] 所有仓位已平仓')
					
					# 分批止盈：期权价格下跌超过不同阈值时分批平仓
					elif g.closed_ratio == 0 and hold_ratio <= option_seller_profit_1 and hold_ratio > option_seller_profit_2:
						# 第一次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第一次止盈条件{-option_seller_profit_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, first_hand, '',1,'平多止盈1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1委托][多头] {g.call_one} 平多止盈1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio <= option_seller_profit_2 and hold_ratio > option_seller_profit_3:
						# 第二次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第二次止盈条件{-option_seller_profit_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, second_hand, '',1,'平多止盈2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2委托][多头] {g.call_one} 平多止盈2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio <= option_seller_profit_3:
						# 第三次止盈，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第三次止盈条件{-option_seller_profit_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.call_one, 12, 0, remain_hand, '',1,'平多止盈3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3委托][多头] {g.call_one} 平多止盈3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.closed_ratio = 1.0
						g.remaining_hands = 0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][多头] 所有仓位已平仓')
					
				if g.hold == -1:  # 空头持仓（卖出认沽期权）
					g.hold_price = max(g.hold_price, c)
					
					# 分批止损：期权价格上涨超过不同阈值时分批平仓
					if g.closed_ratio == 0 and hold_ratio >= option_seller_stop_loss_1 and hold_ratio < option_seller_stop_loss_2:
						# 第一次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1][空头] 期权价格上涨{hold_ratio:.2f}%，触发第一次止损条件{option_seller_stop_loss_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, first_hand, '',1,'平空止损1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1委托][空头] {g.put_one} 平空止损1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio >= option_seller_stop_loss_2 and hold_ratio < option_seller_stop_loss_3:
						# 第二次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2][空头] 期权价格上涨{hold_ratio:.2f}%，触发第二次止损条件{option_seller_stop_loss_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, second_hand, '',1,'平空止损2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2委托][空头] {g.put_one} 平空止损2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio >= option_seller_stop_loss_3:
						# 第三次止损，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3][空头] 期权价格上涨{hold_ratio:.2f}%，触发第三次止损条件{option_seller_stop_loss_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.put_one, 12, 0, remain_hand, '',1,'平空止损3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3委托][空头] {g.put_one} 平空止损3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.remaining_hands = 0
						g.closed_ratio = 1.0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][空头] 所有仓位已平仓')
					
					# 分批止盈：期权价格下跌超过不同阈值时分批平仓
					elif g.closed_ratio == 0 and hold_ratio <= option_seller_profit_1 and hold_ratio > option_seller_profit_2:
						# 第一次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第一次止盈条件{-option_seller_profit_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, first_hand, '',1,'平多止盈1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1委托][多头] {g.call_one} 平多止盈1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio <= option_seller_profit_2 and hold_ratio > option_seller_profit_3:
						# 第二次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第二次止盈条件{-option_seller_profit_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, second_hand, '',1,'平多止盈2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2委托][多头] {g.call_one} 平多止盈2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio <= option_seller_profit_3:
						# 第三次止盈，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第三次止盈条件{-option_seller_profit_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.call_one, 12, 0, remain_hand, '',1,'平多止盈3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3委托][多头] {g.call_one} 平多止盈3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.closed_ratio = 1.0
						g.remaining_hands = 0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][多头] 所有仓位已平仓')
					
				if g.hold == -1:  # 空头持仓（卖出认沽期权）
					g.hold_price = max(g.hold_price, c)
					
					# 分批止损：期权价格上涨超过不同阈值时分批平仓
					if g.closed_ratio == 0 and hold_ratio >= option_seller_stop_loss_1 and hold_ratio < option_seller_stop_loss_2:
						# 第一次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1][空头] 期权价格上涨{hold_ratio:.2f}%，触发第一次止损条件{option_seller_stop_loss_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, first_hand, '',1,'平空止损1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1委托][空头] {g.put_one} 平空止损1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio >= option_seller_stop_loss_2 and hold_ratio < option_seller_stop_loss_3:
						# 第二次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2][空头] 期权价格上涨{hold_ratio:.2f}%，触发第二次止损条件{option_seller_stop_loss_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, second_hand, '',1,'平空止损2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2委托][空头] {g.put_one} 平空止损2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio >= option_seller_stop_loss_3:
						# 第三次止损，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3][空头] 期权价格上涨{hold_ratio:.2f}%，触发第三次止损条件{option_seller_stop_loss_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.put_one, 12, 0, remain_hand, '',1,'平空止损3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3委托][空头] {g.put_one} 平空止损3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.remaining_hands = 0
						g.closed_ratio = 1.0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][空头] 所有仓位已平仓')
					
					# 分批止盈：期权价格下跌超过不同阈值时分批平仓
					elif g.closed_ratio == 0 and hold_ratio <= option_seller_profit_1 and hold_ratio > option_seller_profit_2:
						# 第一次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第一次止盈条件{-option_seller_profit_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, first_hand, '',1,'平多止盈1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1委托][多头] {g.call_one} 平多止盈1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio <= option_seller_profit_2 and hold_ratio > option_seller_profit_3:
						# 第二次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第二次止盈条件{-option_seller_profit_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, second_hand, '',1,'平多止盈2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2委托][多头] {g.call_one} 平多止盈2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio <= option_seller_profit_3:
						# 第三次止盈，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第三次止盈条件{-option_seller_profit_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.call_one, 12, 0, remain_hand, '',1,'平多止盈3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3委托][多头] {g.call_one} 平多止盈3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.closed_ratio = 1.0
						g.remaining_hands = 0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][多头] 所有仓位已平仓')
					
				if g.hold == -1:  # 空头持仓（卖出认沽期权）
					g.hold_price = max(g.hold_price, c)
					
					# 分批止损：期权价格上涨超过不同阈值时分批平仓
					if g.closed_ratio == 0 and hold_ratio >= option_seller_stop_loss_1 and hold_ratio < option_seller_stop_loss_2:
						# 第一次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1][空头] 期权价格上涨{hold_ratio:.2f}%，触发第一次止损条件{option_seller_stop_loss_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, first_hand, '',1,'平空止损1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1委托][空头] {g.put_one} 平空止损1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio >= option_seller_stop_loss_2 and hold_ratio < option_seller_stop_loss_3:
						# 第二次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2][空头] 期权价格上涨{hold_ratio:.2f}%，触发第二次止损条件{option_seller_stop_loss_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, second_hand, '',1,'平空止损2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2委托][空头] {g.put_one} 平空止损2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio >= option_seller_stop_loss_3:
						# 第三次止损，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3][空头] 期权价格上涨{hold_ratio:.2f}%，触发第三次止损条件{option_seller_stop_loss_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.put_one, 12, 0, remain_hand, '',1,'平空止损3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3委托][空头] {g.put_one} 平空止损3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.remaining_hands = 0
						g.closed_ratio = 1.0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][空头] 所有仓位已平仓')
					
					# 分批止盈：期权价格下跌超过不同阈值时分批平仓
					elif g.closed_ratio == 0 and hold_ratio <= option_seller_profit_1 and hold_ratio > option_seller_profit_2:
						# 第一次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第一次止盈条件{-option_seller_profit_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, first_hand, '',1,'平多止盈1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1委托][多头] {g.call_one} 平多止盈1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio <= option_seller_profit_2 and hold_ratio > option_seller_profit_3:
						# 第二次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第二次止盈条件{-option_seller_profit_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, second_hand, '',1,'平多止盈2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2委托][多头] {g.call_one} 平多止盈2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio <= option_seller_profit_3:
						# 第三次止盈，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第三次止盈条件{-option_seller_profit_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.call_one, 12, 0, remain_hand, '',1,'平多止盈3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3委托][多头] {g.call_one} 平多止盈3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.closed_ratio = 1.0
						g.remaining_hands = 0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][多头] 所有仓位已平仓')
					
				if g.hold == -1:  # 空头持仓（卖出认沽期权）
					g.hold_price = max(g.hold_price, c)
					
					# 分批止损：期权价格上涨超过不同阈值时分批平仓
					if g.closed_ratio == 0 and hold_ratio >= option_seller_stop_loss_1 and hold_ratio < option_seller_stop_loss_2:
						# 第一次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1][空头] 期权价格上涨{hold_ratio:.2f}%，触发第一次止损条件{option_seller_stop_loss_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, first_hand, '',1,'平空止损1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1委托][空头] {g.put_one} 平空止损1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio >= option_seller_stop_loss_2 and hold_ratio < option_seller_stop_loss_3:
						# 第二次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2][空头] 期权价格上涨{hold_ratio:.2f}%，触发第二次止损条件{option_seller_stop_loss_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, second_hand, '',1,'平空止损2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2委托][空头] {g.put_one} 平空止损2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio >= option_seller_stop_loss_3:
						# 第三次止损，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3][空头] 期权价格上涨{hold_ratio:.2f}%，触发第三次止损条件{option_seller_stop_loss_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.put_one, 12, 0, remain_hand, '',1,'平空止损3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3委托][空头] {g.put_one} 平空止损3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.remaining_hands = 0
						g.closed_ratio = 1.0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][空头] 所有仓位已平仓')
					
					# 分批止盈：期权价格下跌超过不同阈值时分批平仓
					elif g.closed_ratio == 0 and hold_ratio <= option_seller_profit_1 and hold_ratio > option_seller_profit_2:
						# 第一次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第一次止盈条件{-option_seller_profit_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, first_hand, '',1,'平多止盈1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1委托][多头] {g.call_one} 平多止盈1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio <= option_seller_profit_2 and hold_ratio > option_seller_profit_3:
						# 第二次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第二次止盈条件{-option_seller_profit_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, second_hand, '',1,'平多止盈2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2委托][多头] {g.call_one} 平多止盈2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio <= option_seller_profit_3:
						# 第三次止盈，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第三次止盈条件{-option_seller_profit_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.call_one, 12, 0, remain_hand, '',1,'平多止盈3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3委托][多头] {g.call_one} 平多止盈3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.closed_ratio = 1.0
						g.remaining_hands = 0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][多头] 所有仓位已平仓')
					
				if g.hold == -1:  # 空头持仓（卖出认沽期权）
					g.hold_price = max(g.hold_price, c)
					
					# 分批止损：期权价格上涨超过不同阈值时分批平仓
					if g.closed_ratio == 0 and hold_ratio >= option_seller_stop_loss_1 and hold_ratio < option_seller_stop_loss_2:
						# 第一次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1][空头] 期权价格上涨{hold_ratio:.2f}%，触发第一次止损条件{option_seller_stop_loss_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, first_hand, '',1,'平空止损1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1委托][空头] {g.put_one} 平空止损1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio >= option_seller_stop_loss_2 and hold_ratio < option_seller_stop_loss_3:
						# 第二次止损，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2][空头] 期权价格上涨{hold_ratio:.2f}%，触发第二次止损条件{option_seller_stop_loss_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.put_one, 12, 0, second_hand, '',1,'平空止损2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2委托][空头] {g.put_one} 平空止损2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio >= option_seller_stop_loss_3:
						# 第三次止损，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3][空头] 期权价格上涨{hold_ratio:.2f}%，触发第三次止损条件{option_seller_stop_loss_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.put_one, 12, 0, remain_hand, '',1,'平空止损3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3委托][空头] {g.put_one} 平空止损3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.remaining_hands = 0
						g.closed_ratio = 1.0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][空头] 所有仓位已平仓')
					
					# 分批止盈：期权价格下跌超过不同阈值时分批平仓
					elif g.closed_ratio == 0 and hold_ratio <= option_seller_profit_1 and hold_ratio > option_seller_profit_2:
						# 第一次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第一次止盈条件{-option_seller_profit_1}%，平掉1/3仓位')
						first_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, first_hand, '',1,'平多止盈1'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1委托][多头] {g.call_one} 平多止盈1 {hold_ratio:.2f}% 数量:{first_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.33
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.33 and hold_ratio <= option_seller_profit_2 and hold_ratio > option_seller_profit_3:
						# 第二次止盈，平1/3仓位
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第二次止盈条件{-option_seller_profit_2}%，平掉1/3仓位')
						second_hand = math.ceil(hand/3)
						order_id = passorder(51, 1101, account, g.call_one, 12, 0, second_hand, '',1,'平多止盈2'+g.remark,ContextInfo)
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2委托][多头] {g.call_one} 平多止盈2 {hold_ratio:.2f}% 数量:{second_hand}')
						
						# 记录订单和更新状态
						g.profit_take_orders.append(order_id)
						g.order_submit_time[order_id] = time.time()
						g.closed_ratio = 0.67
						g.remaining_hands = round(hand * (1 - g.closed_ratio))
					
					elif g.closed_ratio <= 0.67 and hold_ratio <= option_seller_profit_3:
						# 第三次止盈，平剩余仓位
						remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
						if remain_hand > 0:
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第三次止盈条件{-option_seller_profit_3}%，平掉剩余{remain_hand}仓位')
							order_id = passorder(51, 1101, account, g.call_one, 12, 0, remain_hand, '',1,'平多止盈3'+g.remark,ContextInfo)
							print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3委托][多头] {g.call_one} 平多止盈3 {hold_ratio:.2f}% 数量:{remain_hand}')
							
							# 记录订单和更新状态
							g.profit_take_orders.append(order_id)
							g.order_submit_time[order_id] = time.time()
						
						g.closed_ratio = 1.0
						g.remaining_hands = 0
						g.hold = 0
						print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓完成][多头] 所有仓位已平仓')
					
				if '期权策略开空'+g.remark in passOrderInfo.strategyName:
					g.buy_short+=1
					print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][订单错误][空头] {g.code} 开空次数+1: {g.buy_short}")
				
				if '期权策略开多'+g.remark in passOrderInfo.strategyName:
					g.buy_long+=1
					print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][订单错误][多头] {g.code} 开多次数+1: {g.buy_long}")
				
				# 处理止盈订单错误
				if "止盈" in passOrderInfo.strategyName or "止损" in passOrderInfo.strategyName:
					print(f"止盈订单错误: {passOrderInfo.strategyName}, 错误信息: {msg}, 订单ID: {passOrderInfo.orderID}")
					# 从跟踪列表中移除
					if passOrderInfo.orderID in g.profit_take_orders:
						g.profit_take_orders.remove(passOrderInfo.orderID)
						print(f"已从跟踪列表移除错误止盈订单: {passOrderInfo.orderID}")
					
					# 从订单时间跟踪字典中移除
					if passOrderInfo.orderID in g.order_submit_time:
						del g.order_submit_time[passOrderInfo.orderID]

def deal_callback(event):
    """
    成交回调函数, 处理交易执行
    """
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][成交回调] 收到成交回调: {event}")
    
    try:
        # 确保事件包含必要数据
        if not all(key in event for key in ['security', 'offset']):
            print("成交回调数据不完整")
            return {'status': 'error', 'msg': "成交回调数据不完整"}
        
        # 检查是否是我们的策略订单
        if not (event['security'] == g.call_one or event['security'] == g.put_one or event['security'] == g.curr_hold):
            print(f"非策略管理的合约成交，忽略: {event['security']}")
            return {'status': 'skip', 'msg': "非策略管理的合约"}
        
        # 开仓处理
        if event['offset'] == 'open':
            # 更新成交价格
            if 'price' in event and event['price'] > 0:
                g.open_price = event['price']
                g.hold_price = event['price']
                print(f"开仓成交，价格更新为: {g.open_price}")
            
            # 更新持仓状态
            if event['side'] == 'buy':
                g.hold = 1  # 多头持仓
                g.curr_hold = event['security']
                g.call_one = event['security']
                print(f"开多成交: {g.curr_hold}")
            elif event['side'] == 'sell':
                g.hold = -1  # 空头持仓
                g.curr_hold = event['security']
                g.put_one = event['security']
                print(f"开空成交: {g.curr_hold}")
            
            # 更新持仓手数
            g.remaining_hands = event['volume'] if 'volume' in event else hand
            print(f"开仓成交，剩余手数: {g.remaining_hands}")
            
            # 重置平仓比例
            g.closed_ratio = 0
            print(f"开仓成交，重置已平仓比例为0")
        
        # 平仓处理
        elif event['offset'] == 'close':
            # 确保持仓存在
            if g.hold == 0:
                print("警告: 收到平仓回报，但当前无持仓状态")
                # 尝试根据成交合约恢复持仓方向
                if event['security'] == g.call_one:
                    g.hold = 1
                    g.curr_hold = g.call_one
                elif event['security'] == g.put_one:
                    g.hold = -1
                    g.curr_hold = g.put_one
            
            # 更新剩余手数
            close_volume = event['volume'] if 'volume' in event else 0
            g.remaining_hands -= close_volume
            
            # 更新已平仓比例
            if g.remaining_hands <= 0:
                g.closed_ratio = 1.0  # 全部平仓
            else:
                # 根据剩余手数计算已平仓比例
                g.closed_ratio = round(1 - (g.remaining_hands / hand), 2)
            
            print(f"平仓成交，减少手数: {close_volume}, 剩余手数: {g.remaining_hands}, 已平仓比例: {g.closed_ratio}")
            
            # 如果全部平仓，更新状态
            if g.remaining_hands <= 0:
                g.hold = 0
                g.open_price = 0
                g.hold_price = 0
                g.remaining_hands = 0
                g.closed_ratio = 1.0
                print("全部平仓，重置持仓状态")
        
        # 检查状态一致性
        check_and_fix_state()
        
        # 记录当前持仓状态
        print(f"当前持仓状态: hold={g.hold}, "
              f"curr_hold={g.curr_hold}, "
              f"remaining_hands={g.remaining_hands}, "
              f"closed_ratio={g.closed_ratio}, "
              f"open_price={g.open_price}")
        
        return {'status': 'success', 'msg': "成交回调处理完成"}
    
    except Exception as e:
        print(f"成交回调处理异常: {e}")
        import traceback
        traceback.print_exc()
        # 尝试恢复状态一致性
        try:
            check_and_fix_state()
        except Exception as recovery_error:
            print(f"状态恢复失败: {recovery_error}")
        
        return {'status': 'error', 'msg': f"成交回调处理异常: {str(e)}"}

def REF(S, N=1):          #对序列整体下移动N,返回序列(shift后会产生NAN)    
    return pd.Series(S).shift(N).values  
def SMA(S, N, M=1):       #中国式的SMA,至少需要120周期才精确 (雪球180周期)    alpha=1/(1+com)    
    return pd.Series(S).ewm(alpha=M/N,adjust=False).mean().values           #com=N-M/M
def SUM(S, N):            #对序列求N天累计和，返回序列    N=0对序列所有依次求和         
    return pd.Series(S).rolling(N).sum().values if N>0 else pd.Series(S).cumsum().values  
def HHV(S,N):             #HHV(C, 5) 最近5天收盘最高价        
    return pd.Series(S).rolling(N).max().values     

def LLV(S,N):             #LLV(C, 5) 最近5天收盘最低价     
    return pd.Series(S).rolling(N).min().values    
    
def HHVBARS(S,N):         #求N周期内S最高值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]),raw=True).values 

def LLVBARS(S,N):         #求N周期内S最低值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]),raw=True).values    
  
def MA(S,N):              #求序列的N日简单移动平均值，返回序列                    
    return pd.Series(S).rolling(N).mean().values  
def EMA(S,N):             #指数移动平均,为了精度 S>4*N  EMA至少需要120周期     alpha=2/(span+1)    
    return pd.Series(S).ewm(span=N, adjust=False).mean().values     

def EMA(source, N:int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    """
    # if not isinstance(source, np.ndarray):
    #     source = np.array(source)
    M = 2
    if N<M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N-1
    M = 2
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M*x+d*temp)/(N+1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
	index = S.index
	result_list = []
	slist = list(S)

	for i in range(len(S.index)):
		l = slist[max(0, i+1-34):i+1]
		result_list.append(min(l))
	return pd.Series(result_list, index=index)
	
	
def PyHHV(S, N):
	index = S.index
	result_list = []
	slist = list(S)

	for i in range(len(S.index)):
		l = slist[max(0, i+1-34):i+1]
		result_list.append(max(l))
	return pd.Series(result_list, index=index)

def cal_vba(ContextInfo, price):
	C = CLOSE = price['close']
	HIGH = price['close']
	LOW = price['close']
	AMT = price['amount']
	VOL = price['volume']
	MA_Line = SUM(C*VOL,0)/SUM(VOL, 0)
	CG=MA(C,21)
	FL=HHV(CG,3)
	FS=CG-(FL-CG)
	
	VA6=(2*CLOSE+HIGH+LOW)/4
	VA8=LLV(LOW,34)
	VARB=HHV(HIGH,34)
	VARC=EMA((VA6-VA8)/(VARB-VA8)*100,13)
	VARD=EMA(0.667*REF(VARC,1)+0.333*VARC,2)
	生命线:EMA(VARD,10)

	VAR1=HHV(HIGH,9)-LLV(LOW,9)
	VAR2=HHV(HIGH,9)-CLOSE
	VAR3=CLOSE-LLV(LOW,9)
	VAR4=((VAR2)/(VAR1))*(100)-70
	VAR5=((CLOSE-LLV(LOW,60))/(HHV(HIGH,60)-LLV(LOW,60)))*(100)


	VAR6=((2)*(CLOSE)+HIGH+LOW)/(4)
	index = VAR6.index
	VAR6 = pd.Series([v for v in VAR6],index=index)
	VAR7=SMA(((VAR3)/(VAR1))*(100),3,1)
	VAR8=PyLLV(CLOSE,min(34, len(LOW)))
	VAR9=SMA(VAR7,3,1)-SMA(VAR4,9,1)
	VAR10 = pd.Series([v-100 if v>100 else 0 for v in VAR9])
	VAR11=PyHHV(CLOSE,min(34, len(HIGH)))
	vv = ((VAR6-VAR8)/(VAR11-VAR8))*(100)
	vv=vv.fillna(0)
	vv=vv.replace([np.inf, -np.inf], np.nan).fillna(0)
	B1=EMA(vv,8)
	B2=EMA(B1,5)
	print(VAR6[-1],VAR11[-1],VAR8[-1])
	return CG,FL,MA_Line,FS,B1,B2


def get_option_real_one(ContextInfo):
	call_one = put_one = None
	now = time.strftime("%Y%m%d")
	
	# 获取当前市场数据进行波动性分析
	price_5m = ContextInfo.get_market_data_ex(['open', 'high', 'low', 'close', 'volume'], 
	                                          [g.undl_code], 
	                                          period='5m',
	                                          count=VOLATILITY_WINDOW+10)[g.undl_code]
	
	# 计算市场波动率
	volatility, volatility_level = calculate_market_volatility(price_5m)
	
	# 获取趋势方向
	trend_result = check_trend(price_5m, calculate_ma(price_5m['close'], price_5m['volume'], 55).iloc[-1])
	trend_direction = 1 if trend_result == "up" else (-1 if trend_result == "down" else 0)
	
	# 确定合约类型和档位
	auto_option_type, auto_option_level = determine_option_parameters(volatility_level, trend_direction)
	
	# 在自动模式中使用计算的参数，否则使用用户设置的参数
	option_type = auto_option_type if AUTO_CONTRACT_SELECT else OPTION_TYPE
	option_level = auto_option_level if AUTO_CONTRACT_SELECT else OPTION_LEVEL
	
	call_list = get_current_month_option(ContextInfo, g.undl_code, now,'CALL')
	
	if not call_list:
		print("未找到符合条件的认购期权合约")
		return None, None

	undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
	call_dict = {call:ContextInfo.get_option_detail_data(call)['OptExercisePrice'] for call in call_list}
	
	# 根据期权类型选择不同的筛选方法
	if option_type == "ITM":  # 实值期权
		call_option_list = {code for code in call_dict if call_dict[code] < undl_price}
		put_option_type = ">"  # 认沽实值期权的行权价大于标的价格
	elif option_type == "OTM":  # 虚值期权
		call_option_list = {code for code in call_dict if call_dict[code] > undl_price}
		put_option_type = "<"  # 认沽虚值期权的行权价小于标的价格
	elif option_type == "ATM":  # 平值期权
		# 找到最接近标的价格的合约
		call_option_list = set(call_list)
		put_option_type = "ATM"
	else:  # 默认使用实值期权
		call_option_list = {code for code in call_dict if call_dict[code] < undl_price}
		put_option_type = ">"
	
	print(f"选定的期权类型: {option_type}, 档位: {option_level}")
	
	# 选择指定档位的合约
	if call_option_list:
		if option_type == "ITM":
			# 实值期权按行权价从大到小排序(接近实值的排前面)
			sorted_calls = sorted(call_option_list, key=lambda code: call_dict[code], reverse=True)
		elif option_type == "OTM":
			# 虚值期权按行权价从小到大排序(接近实值的排前面)
			sorted_calls = sorted(call_option_list, key=lambda code: call_dict[code], reverse=False)
		elif option_type == "ATM":
			# 平值期权按照行权价与标的价格的差值绝对值排序
			sorted_calls = sorted(call_option_list, key=lambda code: abs(call_dict[code] - undl_price))
		
		# 根据档位选择合约
		level_idx = min(option_level - 1, len(sorted_calls) - 1)
		if level_idx >= 0 and level_idx < len(sorted_calls):
			call_one = sorted_calls[level_idx]
			print(f"选定认购期权，档位 {option_level}，行权价 {call_dict[call_one]}")
		else:
			print(f"无法找到符合要求的第{option_level}档认购期权")
	
	# 获取认沽期权列表
	put_list = get_current_month_option(ContextInfo, g.undl_code, now, 'PUT')
	if not put_list:
		print("未找到符合条件的认沽期权合约")
		return call_one, None
	
	put_dict = {put: ContextInfo.get_option_detail_data(put)['OptExercisePrice'] for put in put_list}
	
	# 根据期权类型选择认沽期权
	if put_option_type == ">":  # 实值认沽
		put_option_list = {code for code in put_dict if put_dict[code] > undl_price}
		sort_reverse = False  # 从小到大排序(接近实值的排前面)
	elif put_option_type == "<":  # 虚值认沽
		put_option_list = {code for code in put_dict if put_dict[code] < undl_price}
		sort_reverse = True  # 从大到小排序(接近实值的排前面)
	elif put_option_type == "ATM":  # 平值认沽
		put_option_list = set(put_list)
		sort_reverse = None  # 特殊处理
	else:
		put_option_list = {code for code in put_dict if put_dict[code] > undl_price}
		sort_reverse = False
	
	# 选择指定档位的认沽期权
	if put_option_list:
		if put_option_type == "ATM":
			# 平值期权按照行权价与标的价格的差值绝对值排序
			sorted_puts = sorted(put_option_list, key=lambda code: abs(put_dict[code] - undl_price))
		else:
			sorted_puts = sorted(put_option_list, key=lambda code: put_dict[code], reverse=sort_reverse)
		
		# 根据档位选择合约
		level_idx = min(option_level - 1, len(sorted_puts) - 1)
		if level_idx >= 0 and level_idx < len(sorted_puts):
			put_one = sorted_puts[level_idx]
			print(f"选定认沽期权，档位 {option_level}，行权价 {put_dict[put_one]}")
		else:
			print(f"无法找到符合要求的第{option_level}档认沽期权")
	else:
		print(f"未找到符合 {option_type} 类型的认沽期权")
	
	# 可以在这里添加Delta筛选逻辑，如果合约的Delta数据可用
	# 如果有需要，可以添加额外的Delta筛选代码
	
	return call_one, put_one


def get_current_month_option(ContextInfo, object, dedate, opttype=""):
	#dedate 日期 %Y%m%d 
	#获取截止到ddate这天还未到行权日的当月期权合约
	isavailavle = True
	result = []
	opt_by_month = {}
	undlMarket = "";
	undlCode = "";
	marketcodeList = object.split('.');
	if(len(marketcodeList) !=2):
		return [];
	undlCode = marketcodeList[0]
	undlMarket = marketcodeList[1];
	market = ""
	if(undlMarket == "SH"):
		if undlCode == "000016" or undlCode == "000300" or undlCode == "000852" or undlCode == "000905":
			market = 'IF'
		else:
			market = "SHO"
	elif(undlMarket == "SZ"):
		market = "SZO";
	if(opttype.upper() == "C"):
		opttype = "CALL"
	elif(opttype.upper() == "P"):
		opttype = "PUT"
	optList = []
	if market == 'SHO':
		optList += get_stock_list_in_sector('上证期权')
		
	elif market == 'SZO':
		optList += get_stock_list_in_sector('深证期权')
		
	elif market == 'IF':
		optList += get_stock_list_in_sector('中金所')
	for opt in optList:
		if(opt.find(market) < 0):
			continue
		inst = ContextInfo.get_option_detail_data(opt)
		if('optType' not in inst):
			continue
		endDate = inst['EndDelivDate']
		if( isavailavle and  str(endDate) <= dedate):
			continue
		if(opttype.upper()  != "" and  opttype.upper() != inst["optType"]):
			continue
		if 1: # option is trade,guosen demand
			createDate = inst['OpenDate'];
			openDate = inst['OpenDate'];
			if(createDate >= 1):
				openDate = min(openDate,createDate);
			if(openDate < 20150101 or str(openDate) > dedate):
				continue
		if(inst['ProductID'].find(undlCode) > 0 or inst['OptUndlCode'] == undlCode):
			result.append(opt)
			#print('append')
			#print(opt, inst)
			month = str(endDate)[:6]
			if month not in opt_by_month:
				opt_by_month[month] = [opt]
			else:
				opt_by_month[month].append(opt)
	opt_list = sorted(opt_by_month,reverse=False)
	print(opt_by_month.keys())
	
	# 计算指定年月的第四个星期三(期权到期日)
	def get_fourth_wednesday(year_month_str):
		year = int(year_month_str[:4])
		month = int(year_month_str[4:6])
		
		# 找出该月第一天
		first_day = datetime(year, month, 1)
		
		# 计算第一个星期三的日期
		days_until_wednesday = (2 - first_day.weekday()) % 7
		first_wednesday = first_day + timedelta(days=days_until_wednesday)
		
		# 计算第四个星期三
		fourth_wednesday = first_wednesday + timedelta(days=21)
		
		# 如果超出当月，回退一周
		if fourth_wednesday.month != month:
			fourth_wednesday -= timedelta(days=7)
		
		return fourth_wednesday

	# 在判断逻辑中使用正确的到期日计算
	if len(opt_list) >= 2:  # 确保至少有当月和下月两个合约月份
		expiry_date = get_fourth_wednesday(opt_list[0])
		today = datetime.now()
		days_to_expiry = (expiry_date - today).days
		
		if days_to_expiry < OPTION_ROLL_DAYS:  # 当月合约距离到期不足OPTION_ROLL_DAYS天
			print(f"当月合约{opt_list[0]}距离到期仅剩{days_to_expiry}天，切换到下月合约{opt_list[1]}")
			return opt_by_month[opt_list[1]]  # 返回下月合约
		else:
			return opt_by_month[opt_list[0]]  # 返回当月合约
	elif len(opt_list) == 1:
		expiry_date = get_fourth_wednesday(opt_list[0])
		today = datetime.now()
		days_to_expiry = (expiry_date - today).days
		
		if days_to_expiry < OPTION_ROLL_DAYS:
			print(f"当月合约{opt_list[0]}距离到期仅剩{days_to_expiry}天，但没有找到下月合约")
			print(object, '没有找到下月期权', opt_list[0], opt_by_month)
			return []  # 没有下月合约可供切换
		else:
			return opt_by_month[opt_list[0]]  # 返回当月合约
	else:
		print(object, '未找到任何期权合约')
		return []

def check_break_red_line(price_data, red_line_prev, red_line_curr):
    """
    检查是否跌破红线
    
    参数:
    price_data: 价格数据DataFrame，必须包含'close'列
    red_line_prev: 前一根K线的红线值
    red_line_curr: 当前K线的红线值
    
    返回:
    break_detected: 是否检测到跌破红线
    price_red_ratio: 价格与红线的比例
    """
    try:
        # 严格检查红线值是否存在
        if red_line_prev is None or pd.isna(red_line_prev) or red_line_curr is None or pd.isna(red_line_curr):
            print("红线值不存在，无法判断是否跌破红线")
            return False, 0
        
        # 获取当前收盘价
        current_close = price_data['close'].iloc[-1]
        
        # 检查红线值是否合理 - 与当前价格相差不应太大(超过50%)
        avg_price = price_data['close'].mean()
        red_line_valid_prev = red_line_prev > 0 and abs(red_line_prev - avg_price) / avg_price <= 0.5
        red_line_valid_curr = red_line_curr > 0 and abs(red_line_curr - avg_price) / avg_price <= 0.5
        
        # 如果红线值不合理，就不产生信号
        if not red_line_valid_prev or not red_line_valid_curr:
            print("红线值不合理，无法判断是否跌破红线")
            return False, 0
        
        # 计算价格与红线的百分比距离
        price_red_ratio = (current_close - red_line_curr) / red_line_curr * 100 if red_line_curr > 0 else 0
        
        # 判断是否跌破红线 - 修改判断条件，确保当前价格真正低于红线
        prev_close = price_data['close'].iloc[-2] if len(price_data) > 1 else current_close
        
        # 关键修改: 必须满足当前收盘价低于当前红线值，前一根收盘价高于前一根红线值
        break_detected = (prev_close > red_line_prev) and (current_close < red_line_curr)
        
        # 额外检查: 如果当前价格实际上高于红线，则不能视为跌破，无论前一根K线如何
        if current_close >= red_line_curr:
            break_detected = False
            
        # 只有在检测到跌破红线时才输出信息
        if break_detected:
            print("\n=== 操作信号: 检测到跌破红线 ===")
            print(f"当前价格: {current_close}, 支撑位: {red_line_curr}")
            print("建议操作: 考虑止损或减仓")
        
        return break_detected, price_red_ratio
        
    except Exception as e:
        print(f"检查跌破红线出错: {e}")
        return False, 0

def check_trend(price_data, ma_value, lookback=5):
    """
    检查价格趋势 - 优化版
    
    参数:
    price_data: 价格数据
    ma_value: 均线值
    lookback: 回溯周期
    
    返回:
    "up": 上升趋势
    "none": 无明显趋势
    "down": 下降趋势
    """
    # 计算短期趋势
    short_trend = price_data['close'].iloc[-1] - price_data['close'].iloc[-3]
    
    # 计算价格相对均线位置
    price_vs_ma = price_data['close'].iloc[-1] - ma_value if ma_value is not None and not pd.isna(ma_value) else 0
    
    # 判断趋势
    if short_trend > 0 and price_vs_ma > 0:
        return "up"  # 上升趋势
    elif short_trend < 0 and price_vs_ma < 0:
        return "down"  # 下降趋势
    
    # 如果短期趋势和均线位置不一致，以短期趋势为准
    if abs(short_trend) > abs(price_vs_ma) * 0.01:  # 短期趋势明显
        return "up" if short_trend > 0 else "down"
        
    return "none"  # 无明显趋势

def detect_line_transition(prev_white, curr_white, prev_red, curr_red):
    """
    检测红白线的转换 - 简化版
    参数:
    prev_white: 前一K线的白线值
    curr_white: 当前K线的白线值
    prev_red: 前一K线的红线值
    curr_red: 当前K线的红线值
    
    返回:
    "red_to_white": 红线转白线（空头信号）
    "white_to_red": 白线转红线（多头信号）
    "none": 无转换
    """
    try:
        # 检查值的有效性
        prev_white_valid = prev_white is not None and not pd.isna(prev_white) and prev_white > 0
        curr_white_valid = curr_white is not None and not pd.isna(curr_white) and curr_white > 0
        prev_red_valid = prev_red is not None and not pd.isna(prev_red) and prev_red > 0
        curr_red_valid = curr_red is not None and not pd.isna(curr_red) and curr_red > 0
        
        # 简化日志输出，完全修复格式化问题
        print("\n=== 红白线状态 ===")
        # 为每个值分别处理格式化
        prev_white_str = f"{prev_white:.4f}" if prev_white_valid else "nan"
        curr_white_str = f"{curr_white:.4f}" if curr_white_valid else "nan"
        prev_red_str = f"{prev_red:.4f}" if prev_red_valid else "nan"
        curr_red_str = f"{curr_red:.4f}" if curr_red_valid else "nan"
        
        print(f"白线：前值={prev_white_str}, 当前={curr_white_str}")
        print(f"红线：前值={prev_red_str}, 当前={curr_red_str}")
        
        # 检查是否从红线转为白线 (红线消失，白线出现)
        red_to_white = prev_red_valid and not curr_red_valid and curr_white_valid
        
        # 检查是否从白线转为红线 (白线消失，红线出现)
        white_to_red = prev_white_valid and not curr_white_valid and curr_red_valid
        
        # 只有检测到转换时才输出详细信息
        if red_to_white:
            print("\n【检测到红转白】强烈做空信号")
            return "red_to_white"
        
        if white_to_red:
            print("\n【检测到白转红】潜在做多信号")
            return "white_to_red"
            
        # 简化无转换的输出
        if prev_white_valid and curr_white_valid:
            print("白线持续存在")
        if prev_red_valid and curr_red_valid:
            print("红线持续存在")
        
        return "none"
    except Exception as e:
        print(f"检测红白线转换出错: {e}")
        return "none"

def should_trade_with_transition(red_line, white_line, price, transition_type=None):
    """
    判断是否应该基于红白线交叉进行交易
    
    参数:
    red_line: 当前红线值
    white_line: 当前白线值
    price: 当前价格
    transition_type: 交叉类型 "red_to_white" or "white_to_red" or None（自动检测）
    
    返回:
    should_trade: 是否应该交易
    transition_type: 检测到的交叉类型
    """
    # 安全检查：确保红线和白线值有效
    if red_line is None or pd.isna(red_line) or white_line is None or pd.isna(white_line):
        print("红线或白线值不存在，无法判断交叉交易信号")
        return False, transition_type if transition_type else "unknown"
    
    # 计算红白线距离占比
    line_distance_percent = abs(red_line - white_line) / ((red_line + white_line) / 2) * 100 if red_line > 0 and white_line > 0 else 0
    
    # 计算价格和线的关系
    price_above_white = price > white_line
    price_above_red = price > red_line
    
    # 如果没有指定交叉类型，自动判断
    if transition_type is None:
        if red_line > white_line:
            transition_type = "red_to_white"
        else:
            transition_type = "white_to_red"
    
    print(f"当前交叉类型: {transition_type}")
    print(f"红线: {red_line}, 白线: {white_line}, 价格: {price}")
    print(f"线间距离百分比: {line_distance_percent:.2f}%")
    
    # 根据不同交叉类型进行判断
    if transition_type == "red_to_white":  # 红线在上白线在下
        if line_distance_percent < 3:  # 红白线距离过小
            return False, transition_type
            
        # 主要条件: 价格位于白线之上 (多头)
        if price_above_white:
            print("价格在白线上方，考虑做多")
            return True, transition_type
        else:
            return False, transition_type
            
    elif transition_type == "white_to_red":  # 白线在上红线在下
        if line_distance_percent < 3:  # 红白线距离过小
            return False, transition_type
            
        # 主要条件: 价格位于红线之下 (空头)
        if not price_above_red:
            print("价格在红线下方，考虑做空")
            return True, transition_type
        else:
            return False, transition_type
    
    return False, transition_type

def check_volume_increase(volume_data, threshold_base=1.2):
    """
    检查成交量是否增加
    
    参数:
    volume_data: 成交量数据
    threshold_base: 基础阈值倍数
    
    返回:
    True: 成交量增加
    False: 成交量未增加
    """
    try:
        # 确保数据足够
        if len(volume_data) < 2:
            return False
            
        # 计算当前成交量与前一根K线成交量的比值
        volume_ratio = volume_data.iloc[-1] / volume_data.iloc[-2]
        
        # 返回是否超过阈值
        return volume_ratio > threshold_base
    except Exception as e:
        print(f"检查成交量增加出错: {e}")
        return False

def calculate_ma(close, volume, period):
    """
    计算成交量加权均线
    
    参数:
    close: 收盘价序列
    volume: 成交量序列
    period: 周期
    
    返回:
    ma: 成交量加权均线
    """
    weighted_price = close * volume
    ma = weighted_price.rolling(window=period).sum() / volume.rolling(window=period).sum()
    return ma

def calculate_red_white_lines_exact(price_data, debug_mode=False):
    """
    严格按照文华指标源代码计算红白线
    
    参数:
    price_data: 包含open, high, low, close的DataFrame
    debug_mode: 是否打印详细的调试信息
    
    返回:
    white_line: 白线序列
    red_line: 红线序列
    """
    try:
        # 确保数据足够
        if len(price_data) < 10:
            return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data))
        
        # 获取必要的数据列
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        
        # 计算HX和LX
        # HX:=HHV(HIGH,2);
        hx = high.rolling(window=2).max()
        
        # LX:=LLV(LOW,2);
        lx = low.rolling(window=2).min()
        
        # 初始化结果数组
        h1 = pd.Series(0, index=price_data.index)
        l1 = pd.Series(0, index=price_data.index)
        
        # 计算H1和L1
        for i in range(5, len(price_data)):
            # H1:=IFELSE(HX<REF(HX,1)&&HX<REF(HX,2)&&HX<REF(HX,4)&&LX<REF(LX,1)&&LX<REF(LX,3)&&LX<REF(LX,5)&&OPEN>CLOSE&&(HHV(OPEN,0)-CLOSE)>0,REF(HX,4),0);
            if (i >= 5 and 
                hx.iloc[i] < hx.iloc[i-1] and 
                hx.iloc[i] < hx.iloc[i-2] and 
                i >= 4 and hx.iloc[i] < hx.iloc[i-4] and 
                lx.iloc[i] < lx.iloc[i-1] and 
                i >= 3 and lx.iloc[i] < lx.iloc[i-3] and 
                i >= 5 and lx.iloc[i] < lx.iloc[i-5] and 
                open_price.iloc[i] > close.iloc[i] and 
                (open_price.iloc[:i+1].max() - close.iloc[i]) > 0):
                
                if i >= 4:
                    h1.iloc[i] = hx.iloc[i-4]
                else:
                    h1.iloc[i] = 0
            else:
                h1.iloc[i] = 0
            
            # L1:=IFELSE(LX>REF(LX,1)&&LX>REF(LX,3)&&LX>REF(LX,5)&&HX>REF(HX,1)&&HX>REF(HX,2)&&HX>REF(HX,4)&&OPEN<CLOSE&&(CLOSE-LLV(OPEN,0))>0,REF(LX,4),0);
            if (i >= 5 and 
                lx.iloc[i] > lx.iloc[i-1] and 
                i >= 3 and lx.iloc[i] > lx.iloc[i-3] and 
                i >= 5 and lx.iloc[i] > lx.iloc[i-5] and 
                hx.iloc[i] > hx.iloc[i-1] and 
                hx.iloc[i] > hx.iloc[i-2] and 
                i >= 4 and hx.iloc[i] > hx.iloc[i-4] and 
                open_price.iloc[i] < close.iloc[i] and 
                (close.iloc[i] - open_price.iloc[:i+1].min()) > 0):
                
                if i >= 4:
                    l1.iloc[i] = lx.iloc[i-4]
                else:
                    l1.iloc[i] = 0
            else:
                l1.iloc[i] = 0
        
        # H2:=VALUEWHEN(H1>0,H1);
        h2 = pd.Series(None, index=price_data.index)
        last_valid_h1 = None
        for i in range(len(price_data)):
            if h1.iloc[i] > 0:
                last_valid_h1 = h1.iloc[i]
            h2.iloc[i] = last_valid_h1
        
        # L2:=VALUEWHEN(L1>0,L1);
        l2 = pd.Series(None, index=price_data.index)
        last_valid_l1 = None
        for i in range(len(price_data)):
            if l1.iloc[i] > 0:
                last_valid_l1 = l1.iloc[i]
            l2.iloc[i] = last_valid_l1
        
        # K1:=IFELSE(CLOSE>H2,-3,IFELSE(CLOSE<L2,1,0));
        k1 = pd.Series(0, index=price_data.index)
        for i in range(len(price_data)):
            if h2.iloc[i] is not None and close.iloc[i] > h2.iloc[i]:
                k1.iloc[i] = -3
            elif l2.iloc[i] is not None and close.iloc[i] < l2.iloc[i]:
                k1.iloc[i] = 1
            else:
                k1.iloc[i] = 0
        
        # K2:=VALUEWHEN(K1<>0,K1);
        k2 = pd.Series(0, index=price_data.index)
        last_valid_k1 = 0
        for i in range(len(price_data)):
            if k1.iloc[i] != 0:
                last_valid_k1 = k1.iloc[i]
            k2.iloc[i] = last_valid_k1
        
        # G:=IFELSE(K2=1,H2,L2);
        g = pd.Series(None, index=price_data.index)
        for i in range(len(price_data)):
            if k2.iloc[i] == 1:
                g.iloc[i] = h2.iloc[i]
            else:
                g.iloc[i] = l2.iloc[i]
        
        # 计算白线和红线 - 严格按照原始指标规则
        white_line = pd.Series(None, index=price_data.index)
        red_line = pd.Series(None, index=price_data.index)
        
        for i in range(len(price_data)):
            if k2.iloc[i] == 1:  # 白线
                white_line.iloc[i] = g.iloc[i]
                red_line.iloc[i] = None
            elif k2.iloc[i] == -3:  # 红线
                red_line.iloc[i] = g.iloc[i]
                white_line.iloc[i] = None
            else:  # K2=0，不显示任何线
                white_line.iloc[i] = None
                red_line.iloc[i] = None
        
        # 打印诊断信息
        last_idx = len(price_data) - 1
        if debug_mode:
            print(f"\n=== 红白线计算详情 ===")
            print(f"K2[-1] = {k2.iloc[last_idx]}, G[-1] = {g.iloc[last_idx]}")
            
            # 检查red_line和white_line最后一个值是否为None
            if red_line.iloc[last_idx] is not None and not pd.isna(red_line.iloc[last_idx]):
                print(f"红线[-1] = {red_line.iloc[last_idx]:.4f}")
            else:
                print("红线[-1] = None")
                
            if white_line.iloc[last_idx] is not None and not pd.isna(white_line.iloc[last_idx]):
                print(f"白线[-1] = {white_line.iloc[last_idx]:.4f}")
            else:
                print("白线[-1] = None")
            
            # 打印详细的诊断信息，说明为什么红线或白线可能不存在
            if k2.iloc[last_idx] == 1:
                print("K2=1，显示白线，不显示红线")
            elif k2.iloc[last_idx] == -3:
                print("K2=-3，显示红线，不显示白线")
            else:
                print(f"K2={k2.iloc[last_idx]}，不显示红线也不显示白线")
                print("注意：根据原始指标设计，只有K2=-3时才显示红线，只有K2=1时才显示白线，K2=0时不显示任何线")
        else:
            print(f"● 红白线计算: K2={k2.iloc[last_idx]} | {'显示白线' if k2.iloc[last_idx] == 1 else '显示红线' if k2.iloc[last_idx] == -3 else '无线'}")
            
        # 打印H1, L1的最后几个值
        h1_last_values = list(h1.iloc[-5:])
        l1_last_values = list(l1.iloc[-5:])
        if debug_mode:
            print(f"H1最后5个值: {h1_last_values}")
            print(f"L1最后5个值: {l1_last_values}")
        
        # 检查是否需要触发H1/L1诊断
        h1_all_zeros = all(v == 0 for v in h1_last_values)
        l1_all_zeros = all(v == 0 for v in l1_last_values)
        
        # 如果H1或L1最近5个值全部为0，触发诊断
        if h1_all_zeros or l1_all_zeros:
            if debug_mode:
                print("\n检测到H1/L1计算异常，启动深度诊断...")
                if h1_all_zeros:
                    print("[WARN] H1最近5个值全为0，这可能导致红线无法正确显示")
                if l1_all_zeros:
                    print("[WARN] L1最近5个值全为0，这可能导致白线无法正确显示")
            else:
                print("● 注意: H1/L1存在计算异常，进行诊断")
                
            # 调用深度诊断函数，非debug模式下不显示详细信息
            diagnose_hl_calculation(price_data, debug_mode=debug_mode)
        
        # 检查计算的红白线值是否合理
        if red_line.iloc[last_idx] is not None and not pd.isna(red_line.iloc[last_idx]):
            if abs(red_line.iloc[last_idx] - close.iloc[last_idx]) / close.iloc[last_idx] > 0.5:
                print(f"● 警告: 红线值 {red_line.iloc[last_idx]:.4f} 与收盘价 {close.iloc[last_idx]:.4f} 相差过大!")
        
        if white_line.iloc[last_idx] is not None and not pd.isna(white_line.iloc[last_idx]):
            if abs(white_line.iloc[last_idx] - close.iloc[last_idx]) / close.iloc[last_idx] > 0.5:
                print(f"● 警告: 白线值 {white_line.iloc[last_idx]:.4f} 与收盘价 {close.iloc[last_idx]:.4f} 相差过大!")
        
        return white_line, red_line
    
    except Exception as e:
        print(f"计算红白线出错: {e}")
        import traceback
        traceback.print_exc()
        return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data))

def check_and_print_line_distance(current_price, white_line, red_line):
    """
    检查当前价格与红白线的距离，并进行输出
    
    参数:
    current_price: 当前价格
    white_line: 白线价格
    red_line: 红线价格
    
    返回:
    red_line_distance_ok: 与红线距离是否合适
    white_line_distance_ok: 与白线距离是否合适
    """
    red_line_distance_ok = True
    white_line_distance_ok = True
    
    # 定义距离警告阈值级别
    CRITICAL_DISTANCE = 0.08  # 8%
    WARNING_DISTANCE = 0.05   # 5%
    NOTICE_DISTANCE = 0.03    # 3%
    
    # 白线距离检查
    if white_line is not None and not pd.isna(white_line):
        white_distance = abs(current_price - white_line) / white_line
        white_distance_percent = white_distance * 100
        
        if white_distance > WHITE_LINE_DISTANCE_THRESHOLD:
            print(f"● 距离检查: 价格与白线距离{white_distance_percent:.2f}% > {WHITE_LINE_DISTANCE_THRESHOLD*100:.2f}%阈值")
            white_line_distance_ok = False
    else:
        print("● 距离检查: 白线值不存在，跳过距离检查")
    
    # 红线距离检查
    if red_line is not None and not pd.isna(red_line):
        red_distance = abs(current_price - red_line) / red_line
        red_distance_percent = red_distance * 100
        
        if red_distance > RED_LINE_DISTANCE_THRESHOLD:
            print(f"● 距离检查: 价格与红线距离{red_distance_percent:.2f}% > {RED_LINE_DISTANCE_THRESHOLD*100:.2f}%阈值")
            red_line_distance_ok = False
    else:
        print("● 距离检查: 红线值不存在，跳过距离检查")
    
    return red_line_distance_ok, white_line_distance_ok

# 添加订单超时检查函数
def check_order_timeout(ContextInfo):
    """检查并处理超时未成交的订单"""
    current_time = time.time()
    orders = get_trade_detail_data(account, 'STOCK', 'STOCK_OPTION')
    
    for o in orders:
        # 检查订单是否为待成交状态
        if o.m_nOrderStatus in [50, 55]:  # 委托可撤状态
            order_id = o.m_strOrderSysID
            
            # 如果是我们跟踪的订单
            if order_id in g.order_submit_time:
                submit_time = g.order_submit_time[order_id]
                
                # 检查是否超时
                if current_time - submit_time > ORDER_TIMEOUT:
                    print(f"订单超时: {order_id}, 已等待{current_time - submit_time}秒, 准备撤单")
                    
                    # 撤销订单
                    cancel(order_id, account, 'stock', ContextInfo)
                    
                    # 从跟踪列表中移除
                    if order_id in g.profit_take_orders:
                        g.profit_take_orders.remove(order_id)
                    
                    # 从提交时间字典中移除
                    del g.order_submit_time[order_id]
                    
                    print(f"已撤销超时订单: {order_id}")
                    
                    # 如果是开仓订单，可能需要重新评估开仓条件
                    # 如果是平仓订单，可能需要重新尝试平仓

def calculate_market_volatility(price_data, window=VOLATILITY_WINDOW):
    """
    计算市场波动率
    
    参数:
    price_data: 价格数据，需要包含'high'和'low'列
    window: 计算窗口大小
    
    返回:
    volatility: 波动率百分比
    volatility_level: 波动级别 ("low", "medium", "high")
    """
    try:
        # 确保数据足够
        if len(price_data) < window:
            print(f"● 警告: 数据不足，使用默认波动率")
            return 1.0, "medium"
        
        # 计算波动率 - 使用真实波动幅度(ATR)的变种
        recent_data = price_data.iloc[-window:]
        
        # 计算每日波动率(百分比)
        daily_volatility = []
        for i in range(1, len(recent_data)):
            high = recent_data['high'].iloc[i]
            low = recent_data['low'].iloc[i]
            if high > 0 and low > 0:  # 避免除以零或负数
                daily_range_pct = (high - low) / low * 100
                daily_volatility.append(daily_range_pct)
        
        if not daily_volatility:
            return 1.0, "medium"
        
        # 平均日波动率
        avg_volatility = sum(daily_volatility) / len(daily_volatility)
        
        # 波动率分级
        if avg_volatility < VOLATILITY_LOW:
            volatility_level = "low"
        elif avg_volatility > VOLATILITY_HIGH:
            volatility_level = "high"
        else:
            volatility_level = "medium"
            
        print(f"● 市场波动率: {avg_volatility:.2f}% | 级别: {volatility_level}")
        
        return avg_volatility, volatility_level
        
    except Exception as e:
        print(f"● 警告: 计算波动率出错: {e}")
        return 1.0, "medium"  # 默认中等波动率

def determine_option_parameters(volatility_level, trend_direction):
    """
    根据波动率和趋势方向确定合适的期权参数
    
    参数:
    volatility_level: 波动率级别 ("low", "medium", "high")
    trend_direction: 趋势方向 (1=上升, -1=下降, 0=无明显趋势)
    
    返回:
    option_type: 期权类型 ("ITM", "ATM", "OTM")
    option_level: 期权档位 (1, 2, 3)
    """
    # 默认使用用户设置的参数
    if not AUTO_CONTRACT_SELECT:
        return OPTION_TYPE, OPTION_LEVEL
    
    # 根据波动率和趋势确定合约类型和档位
    if volatility_level == "high":
        # 高波动市场，使用实值期权降低风险
        option_type = "ITM"
        # 在强趋势下可以选择稍微靠近平值的合约
        option_level = 1 if abs(trend_direction) <= 0.5 else 2
    elif volatility_level == "low":
        # 低波动市场，可以使用虚值期权提高杠杆
        # 但在无明显趋势时使用平值期权更为安全
        if abs(trend_direction) < 0.3:
            option_type = "ATM"
            option_level = 1
        else:
            # 明确趋势时可以使用虚值期权
            option_type = "OTM"
            option_level = 1
    else:  # medium volatility
        # 中等波动率市场，根据趋势的强弱选择
        if abs(trend_direction) > 0.7:
            # 强趋势可以考虑虚值期权
            option_type = "OTM"
            option_level = 1
        elif abs(trend_direction) > 0.3:
            # 中等趋势使用平值期权
            option_type = "ATM"
            option_level = 1
        else:
            # 弱趋势使用实值期权
            option_type = "ITM" 
            option_level = 1
    
    print(f"● 合约选择: {option_type}-{option_level} (波动率:{volatility_level}|趋势:{trend_direction:.2f})")
    
    return option_type, option_level

# 在全局作用域中定义历史转换检测函数
def check_historical_transition(white_line, red_line, lookback=10):
    """
    检查历史数据中是否有红白线转换
    
    参数:
    white_line: 白线序列
    red_line: 红线序列
    lookback: 向前检查的K线数量
    
    返回:
    (transition_type, bars_ago): 转换类型和发生在多少根K线之前
    transition_type: "red_to_white", "white_to_red", "none"
    """
    try:
        # 确保数据足够
        if white_line is None or red_line is None:
            print("● 红白线数据不足，无法检测历史转换")
            return "none", 0
            
        # 转换为Series以便使用pandas方法
        if not isinstance(white_line, pd.Series):
            white_line = pd.Series(white_line)
        if not isinstance(red_line, pd.Series):
            red_line = pd.Series(red_line)
            
        # 确保数据长度足够
        min_length = min(len(white_line), len(red_line))
        if min_length < 3:
            print(f"数据长度不足，当前长度: {min_length}，需要至少3根K线")
            return "none", 0
            
        # 限制lookback不超过可用数据长度
        lookback = min(lookback, min_length - 1)
        
        # 记录找到的转换
        transitions = []
        
        # 从近期数据开始向前检查
        for i in range(1, lookback):
            if i >= min_length:
                break
                
            prev_white = white_line.iloc[-i-1] if i+1 < len(white_line) else None
            curr_white = white_line.iloc[-i] if i < len(white_line) else None
            prev_red = red_line.iloc[-i-1] if i+1 < len(red_line) else None
            curr_red = red_line.iloc[-i] if i < len(red_line) else None
            
            prev_white_valid = prev_white is not None and not pd.isna(prev_white)
            curr_white_valid = curr_white is not None and not pd.isna(curr_white)
            prev_red_valid = prev_red is not None and not pd.isna(prev_red)
            curr_red_valid = curr_red is not None and not pd.isna(curr_red)
            
            # 检查红转白
            if prev_red_valid and not prev_white_valid and curr_white_valid and not curr_red_valid:
                transitions.append(("red_to_white", i))
                print(f"检测到历史红转白：发生在{i}根K线之前")
                print(f"红线值从 {prev_red} 变为 None，白线值从 None 变为 {curr_white}")
                
            # 检查白转红
            if prev_white_valid and not prev_red_valid and curr_red_valid and not curr_white_valid:
                transitions.append(("white_to_red", i))
                print(f"检测到历史白转红：发生在{i}根K线之前")
                print(f"白线值从 {prev_white} 变为 None，红线值从 None 变为 {curr_red}")
        
        # 返回最近的一次转换
        if transitions:
            # 按发生时间排序（从近到远）
            transitions.sort(key=lambda x: x[1])
            print(f"● 最近转换: {transitions[0][0]} ({transitions[0][1]}根K线前)")
            return transitions[0]
        else:
            print("● 未检测到历史红白线转换")
            return "none", 0
            
    except Exception as e:
        print(f"检查历史转换出错: {e}")
        import traceback
        traceback.print_exc()
        return "none", 0

# 添加新的诊断函数
def diagnose_hl_calculation(price_data, debug_mode=False):
    """
    专门诊断H1和L1为什么会出现全0情况
    
    参数:
    price_data: 包含open, high, low, close的DataFrame
    debug_mode: 是否打印详细的调试信息
    
    返回:
    diagnosis_result: 包含诊断结果的字典
    """
    if debug_mode:
        print("\n" + "="*50)
        print("[###] H1和L1计算诊断 [###]")
        print("="*50)
    
    diagnosis_result = {
        "h1_conditions_met": 0,
        "l1_conditions_met": 0,
        "total_bars": 0,
        "data_length_sufficient": False,
        "has_valid_h1": False,
        "has_valid_l1": False,
        "h1_condition_failures": {},
        "l1_condition_failures": {}
    }
    
    try:
        # 检查数据长度是否足够
        if len(price_data) < 10:
            print("[ERROR] 数据长度不足 (需要至少10根K线)")
            diagnosis_result["data_length_sufficient"] = False
            return diagnosis_result
        else:
            diagnosis_result["data_length_sufficient"] = True
            diagnosis_result["total_bars"] = len(price_data)
            print(f"[OK] 数据长度充足: {len(price_data)}根K线")
        
        # 获取必要的数据列
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        
        # 检查数据是否包含NaN或无效值
        if high.isna().any() or low.isna().any() or open_price.isna().any() or close.isna().any():
            print("[WARN] 数据包含NaN值，可能影响计算")
            
            # 显示每列NaN的数量
            print(f"  高价(high)中的NaN数量: {high.isna().sum()}")
            print(f"  低价(low)中的NaN数量: {low.isna().sum()}")
            print(f"  开盘价(open)中的NaN数量: {open_price.isna().sum()}")
            print(f"  收盘价(close)中的NaN数量: {close.isna().sum()}")
        
        # 计算HX和LX
        print("\n【计算HX和LX】")
        hx = high.rolling(window=2).max()
        lx = low.rolling(window=2).min()
        
        print(f"HX最后5个值: {list(hx.iloc[-5:])}")
        print(f"LX最后5个值: {list(lx.iloc[-5:])}")
        
        if hx.isna().any() or lx.isna().any():
            print("[WARN] HX或LX中存在NaN值")
            print(f"  HX中的NaN数量: {hx.isna().sum()}")
            print(f"  LX中的NaN数量: {lx.isna().sum()}")
        
        # 初始化结果数组和条件失败计数器
        h1 = pd.Series(0, index=price_data.index)
        l1 = pd.Series(0, index=price_data.index)
        
        h1_failures = {
            "hx_lt_ref1": 0,
            "hx_lt_ref2": 0,
            "hx_lt_ref4": 0,
            "lx_lt_ref1": 0,
            "lx_lt_ref3": 0,
            "lx_lt_ref5": 0,
            "open_gt_close": 0,
            "hhv_open_minus_close_gt_0": 0,
            "index_too_small": 0
        }
        
        l1_failures = {
            "lx_gt_ref1": 0,
            "lx_gt_ref3": 0,
            "lx_gt_ref5": 0,
            "hx_gt_ref1": 0,
            "hx_gt_ref2": 0,
            "hx_gt_ref4": 0,
            "open_lt_close": 0,
            "close_minus_llv_open_gt_0": 0,
            "index_too_small": 0
        }
        
        print("\n【逐条件检验H1和L1】")
        # 从第6个K线开始检查（确保有足够的历史数据）
        valid_h1_count = 0
        valid_l1_count = 0
        
        # 如果不是调试模式，不打印详细检查过程
        if not debug_mode:
            print("● 计算红白线...")
        else:
            print("\n【逐条件检验H1和L1】")
        for i in range(5, len(price_data)):
            # 用于调试的详细输出
            if debug_mode and i >= len(price_data) - 5:  # 仅显示最后5根K线的详细信息
                print(f"\n检查K线 #{i} (索引从0开始)")
                print(f"  时间: {price_data.index[i]}")
                print(f"  价格: O={open_price.iloc[i]:.4f} H={high.iloc[i]:.4f} L={low.iloc[i]:.4f} C={close.iloc[i]:.4f}")
                print(f"  HX={hx.iloc[i]:.4f} LX={lx.iloc[i]:.4f}")
            
            # 检查H1条件
            h1_condition_detail = {}
            
            # 单独检查每个条件
            cond1 = hx.iloc[i] < hx.iloc[i-1]
            cond2 = hx.iloc[i] < hx.iloc[i-2]
            cond3 = i >= 4 and hx.iloc[i] < hx.iloc[i-4]
            cond4 = lx.iloc[i] < lx.iloc[i-1]
            cond5 = i >= 3 and lx.iloc[i] < lx.iloc[i-3]
            cond6 = i >= 5 and lx.iloc[i] < lx.iloc[i-5]
            cond7 = open_price.iloc[i] > close.iloc[i]
            cond8 = (open_price.iloc[:i+1].max() - close.iloc[i]) > 0
            
            # 记录失败的条件
            if not cond1: h1_failures["hx_lt_ref1"] += 1
            if not cond2: h1_failures["hx_lt_ref2"] += 1
            if not cond3: 
                if i < 4:
                    h1_failures["index_too_small"] += 1
                else:
                    h1_failures["hx_lt_ref4"] += 1
            if not cond4: h1_failures["lx_lt_ref1"] += 1
            if not cond5:
                if i < 3:
                    h1_failures["index_too_small"] += 1
                else:
                    h1_failures["lx_lt_ref3"] += 1
            if not cond6:
                if i < 5:
                    h1_failures["index_too_small"] += 1
                else:
                    h1_failures["lx_lt_ref5"] += 1
            if not cond7: h1_failures["open_gt_close"] += 1
            if not cond8: h1_failures["hhv_open_minus_close_gt_0"] += 1
            
            # 所有H1条件是否满足
            h1_all_conditions = (cond1 and cond2 and cond3 and cond4 and cond5 and cond6 and cond7 and cond8)
            
            if h1_all_conditions:
                if i >= 4:
                    h1.iloc[i] = hx.iloc[i-4]
                    valid_h1_count += 1
                    if debug_mode and i >= len(price_data) - 5:
                        print(f"  [OK] H1条件全部满足: H1 = {hx.iloc[i-4]:.4f}")
                else:
                    if debug_mode and i >= len(price_data) - 5:
                        print(f"  [WARN] H1条件满足但索引太小 (需要i>=4): H1 = 0")
            else:
                if debug_mode and i >= len(price_data) - 5:
                    print(f"  [ERROR] H1条件不满足: H1 = 0")
                    print(f"    条件状态: {'[+]' if cond1 else '[-]'} HX<REF(HX,1) | {'[+]' if cond2 else '[-]'} HX<REF(HX,2) | {'[+]' if cond3 else '[-]'} HX<REF(HX,4)")
                    print(f"             {'[+]' if cond4 else '[-]'} LX<REF(LX,1) | {'[+]' if cond5 else '[-]'} LX<REF(LX,3) | {'[+]' if cond6 else '[-]'} LX<REF(LX,5)")
                    print(f"             {'[+]' if cond7 else '[-]'} OPEN>CLOSE | {'[+]' if cond8 else '[-]'} (HHV(OPEN)-CLOSE)>0")
            
            # 检查L1条件
            cond1 = lx.iloc[i] > lx.iloc[i-1]
            cond2 = i >= 3 and lx.iloc[i] > lx.iloc[i-3]
            cond3 = i >= 5 and lx.iloc[i] > lx.iloc[i-5]
            cond4 = hx.iloc[i] > hx.iloc[i-1]
            cond5 = hx.iloc[i] > hx.iloc[i-2]
            cond6 = i >= 4 and hx.iloc[i] > hx.iloc[i-4]
            cond7 = open_price.iloc[i] < close.iloc[i]
            cond8 = (close.iloc[i] - open_price.iloc[:i+1].min()) > 0
            
            # 记录失败的条件
            if not cond1: l1_failures["lx_gt_ref1"] += 1
            if not cond2:
                if i < 3:
                    l1_failures["index_too_small"] += 1
                else:
                    l1_failures["lx_gt_ref3"] += 1
            if not cond3:
                if i < 5:
                    l1_failures["index_too_small"] += 1
                else:
                    l1_failures["lx_gt_ref5"] += 1
            if not cond4: l1_failures["hx_gt_ref1"] += 1
            if not cond5: l1_failures["hx_gt_ref2"] += 1
            if not cond6:
                if i < 4:
                    l1_failures["index_too_small"] += 1
                else:
                    l1_failures["hx_gt_ref4"] += 1
            if not cond7: l1_failures["open_lt_close"] += 1
            if not cond8: l1_failures["close_minus_llv_open_gt_0"] += 1
            
            # 所有L1条件是否满足
            l1_all_conditions = (cond1 and cond2 and cond3 and cond4 and cond5 and cond6 and cond7 and cond8)
            
            if l1_all_conditions:
                if i >= 4:
                    l1.iloc[i] = lx.iloc[i-4]
                    valid_l1_count += 1
                    if debug_mode and i >= len(price_data) - 5:
                        print(f"  [OK] L1条件全部满足: L1 = {lx.iloc[i-4]:.4f}")
                else:
                    if debug_mode and i >= len(price_data) - 5:
                        print(f"  [WARN] L1条件满足但索引太小 (需要i>=4): L1 = 0")
            else:
                if debug_mode and i >= len(price_data) - 5:
                    print(f"  [ERROR] L1条件不满足: L1 = 0")
                    print(f"    条件状态: {'[+]' if cond1 else '[-]'} LX>REF(LX,1) | {'[+]' if cond2 else '[-]'} LX>REF(LX,3) | {'[+]' if cond3 else '[-]'} LX>REF(LX,5)")
                    print(f"             {'[+]' if cond4 else '[-]'} HX>REF(HX,1) | {'[+]' if cond5 else '[-]'} HX>REF(HX,2) | {'[+]' if cond6 else '[-]'} HX>REF(HX,4)")
                    print(f"             {'[+]' if cond7 else '[-]'} OPEN<CLOSE | {'[+]' if cond8 else '[-]'} (CLOSE-LLV(OPEN))>0")
        
        # 打印H1、L1的统计信息
        if debug_mode:
            print("\n【H1和L1分析结果】")
            print(f"数据总长度: {len(price_data)}根K线")
            print(f"H1有效值数量: {valid_h1_count} ({valid_h1_count/len(price_data)*100:.2f}%)")
            print(f"L1有效值数量: {valid_l1_count} ({valid_l1_count/len(price_data)*100:.2f}%)")
        else:
            print(f"● 红白线统计: H1有效值={valid_h1_count}/{len(price_data)} ({valid_h1_count/len(price_data)*100:.2f}%) | L1有效值={valid_l1_count}/{len(price_data)} ({valid_l1_count/len(price_data)*100:.2f}%)")
        
        diagnosis_result["h1_conditions_met"] = valid_h1_count
        diagnosis_result["l1_conditions_met"] = valid_l1_count
        diagnosis_result["has_valid_h1"] = valid_h1_count > 0
        diagnosis_result["has_valid_l1"] = valid_l1_count > 0
        
        # 分析H1失败的主要原因 - 仅在debug模式下显示
        if debug_mode:
            print("\n【H1计算失败原因分析】")
            h1_failures_sorted = sorted(h1_failures.items(), key=lambda x: x[1], reverse=True)
            diagnosis_result["h1_condition_failures"] = dict(h1_failures_sorted)
            
            for condition, count in h1_failures_sorted:
                percent = count/(len(price_data)-5)*100 if len(price_data) > 5 else 0
                if count > 0:
                    print(f"  {condition}: {count}次 ({percent:.2f}%)")
                    
                    # 给出中文解释
                    if condition == "hx_lt_ref1":
                        print("     解释: HX < REF(HX,1) 条件不满足，即最近2天的最高价没有下降")
                    elif condition == "hx_lt_ref2":
                        print("     解释: HX < REF(HX,2) 条件不满足，即最近的最高价没有低于2天前的最高价")
                    elif condition == "hx_lt_ref4":
                        print("     解释: HX < REF(HX,4) 条件不满足，即最近的最高价没有低于4天前的最高价")
                    elif condition == "lx_lt_ref1":
                        print("     解释: LX < REF(LX,1) 条件不满足，即最近2天的最低价没有下降")
                    elif condition == "lx_lt_ref3":
                        print("     解释: LX < REF(LX,3) 条件不满足，即最近的最低价没有低于3天前的最低价")
                    elif condition == "lx_lt_ref5":
                        print("     解释: LX < REF(LX,5) 条件不满足，即最近的最低价没有低于5天前的最低价")
                    elif condition == "open_gt_close":
                        print("     解释: OPEN > CLOSE 条件不满足，即K线没有收阴（收盘价高于开盘价）")
                    elif condition == "hhv_open_minus_close_gt_0":
                        print("     解释: (HHV(OPEN)-CLOSE) > 0 条件不满足，即收盘价不低于历史最高开盘价")
                    elif condition == "index_too_small":
                        print("     解释: 数据索引太小，无法获取足够的历史数据进行计算")
            
            # 分析L1失败的主要原因
            print("\n【L1计算失败原因分析】")
            l1_failures_sorted = sorted(l1_failures.items(), key=lambda x: x[1], reverse=True)
            diagnosis_result["l1_condition_failures"] = dict(l1_failures_sorted)
            
            for condition, count in l1_failures_sorted:
                percent = count/(len(price_data)-5)*100 if len(price_data) > 5 else 0
                if count > 0:
                    print(f"  {condition}: {count}次 ({percent:.2f}%)")
                    
                    # 给出中文解释
                    if condition == "lx_gt_ref1":
                        print("     解释: LX > REF(LX,1) 条件不满足，即最近2天的最低价没有上升")
                    elif condition == "lx_gt_ref3":
                        print("     解释: LX > REF(LX,3) 条件不满足，即最近的最低价没有高于3天前的最低价")
                    elif condition == "lx_gt_ref5":
                        print("     解释: LX > REF(LX,5) 条件不满足，即最近的最低价没有高于5天前的最低价")
                    elif condition == "hx_gt_ref1":
                        print("     解释: HX > REF(HX,1) 条件不满足，即最近2天的最高价没有上升")
                    elif condition == "hx_gt_ref2":
                        print("     解释: HX > REF(HX,2) 条件不满足，即最近的最高价没有高于2天前的最高价")
                    elif condition == "hx_gt_ref4":
                        print("     解释: HX > REF(HX,4) 条件不满足，即最近的最高价没有高于4天前的最高价")
                    elif condition == "open_lt_close":
                        print("     解释: OPEN < CLOSE 条件不满足，即K线没有收阳（收盘价低于开盘价）")
                    elif condition == "close_minus_llv_open_gt_0":
                        print("     解释: (CLOSE-LLV(OPEN)) > 0 条件不满足，即收盘价不高于历史最低开盘价")
                    elif condition == "index_too_small":
                        print("     解释: 数据索引太小，无法获取足够的历史数据进行计算")
        
        # 总结分析并给出建议
        if debug_mode:
            print("\n【诊断总结】")
        
        if valid_h1_count == 0 and valid_l1_count == 0:
            if debug_mode:
                print("[WARN] H1和L1都没有有效值，红白线无法正常计算")
            else:
                print("● 警告: H1和L1都没有有效值，红白线计算受限")
            
            # 获取主要原因
            h1_main_reason = sorted(h1_failures.items(), key=lambda x: x[1], reverse=True)[0][0] if h1_failures else None
            l1_main_reason = sorted(l1_failures.items(), key=lambda x: x[1], reverse=True)[0][0] if l1_failures else None
            
            if debug_mode:
                if h1_main_reason:
                    print(f"H1主要失败原因: {h1_main_reason}")
                if l1_main_reason:
                    print(f"L1主要失败原因: {l1_main_reason}")
                
                print("建议检查市场趋势和价格走势，当前可能处于转折点或盘整区间")
            else:
                print(f"● 市场分析: 可能处于转折点或盘整区间")
                
        elif valid_h1_count == 0:
            if debug_mode:
                print("[WARN] H1没有有效值，红线无法正常显示")
                print("建议可能处于上升趋势中，高点持续创新高")
            else:
                print("● 警告: H1没有有效值，红线计算受限 | 可能处于上升趋势")
        
        elif valid_l1_count == 0:
            if debug_mode:
                print("[WARN] L1没有有效值，白线无法正常显示")
                print("建议可能处于下降趋势中，低点持续创新低")
            else:
                print("● 警告: L1没有有效值，白线计算受限 | 可能处于下降趋势")
                
        else:
            if debug_mode:
                print("[OK] H1和L1都有有效值，红白线计算正常")
            else:
                print("● 红白线计算正常")
            
        # 检查HX、LX数据的NaN情况
        nan_in_hx = hx.isna().sum()
        nan_in_lx = lx.isna().sum()
        if nan_in_hx > 0 or nan_in_lx > 0:
            if debug_mode:
                print(f"[WARN] HX或LX中存在NaN值")
                print(f"  HX中的NaN数量: {nan_in_hx}")
                print(f"  LX中的NaN数量: {nan_in_lx}")
            else:
                print(f"● 警告: 数据存在缺失 (HX: {nan_in_hx}个空值, LX: {nan_in_lx}个空值)")
                
        if debug_mode:
            print("="*50)
        
        return diagnosis_result
        
    except Exception as e:
        print(f"诊断过程出错: {e}")
        import traceback
        traceback.print_exc()
        diagnosis_result["error"] = str(e)
        return diagnosis_result

def detect_market_condition(price_data, ma_value):
    """
    自动判断市场状态
    
    参数:
    price_data: 价格数据DataFrame，包含open, high, low, close
    ma_value: 均线值
    
    返回:
    market_condition: "normal", "volatile", "flat", "抄底"
    """
    try:
        # 确保数据充足
        if len(price_data) < 20:
            return "normal"  # 数据不足时默认为normal
        
        # 1. 计算近期波动率 (最高价与最低价的差值占均价的百分比)
        recent_data = price_data.iloc[-10:]  # 使用最近10根K线
        price_range = (recent_data['high'].max() - recent_data['low'].min()) / recent_data['close'].mean() * 100
        
        # 2. 判断趋势强度 (收盘价与均线的关系)
        close = price_data['close'].iloc[-1]
        trend_strength = abs(close - ma_value) / ma_value * 100
        
        # 3. 计算价格位置 (当前价格在近期价格范围中的位置)
        recent_min = recent_data['low'].min()
        recent_max = recent_data['high'].max()
        price_position = (close - recent_min) / (recent_max - recent_min) if (recent_max - recent_min) > 0 else 0.5
        
        # 4. 判断连续下跌 (用于抄底判断)
        down_days = 0
        for i in range(min(5, len(price_data) - 1)):
            if price_data['close'].iloc[-(i+1)] < price_data['close'].iloc[-(i+2)]:
                down_days += 1
        
        # 综合判断
        if price_range > 3:  # 波动率大于3%
            return "volatile"
        elif trend_strength < 0.5 and price_range < 1.5:  # 趋势强度弱且波动率小
            return "flat"
        elif down_days >= 3 and price_position < 0.2:  # 连续下跌且价格接近近期低点
            return "抄底"
        else:
            return "normal"
            
    except Exception as e:
        print(f"市场状态判断出错: {e}")
        return "normal"  # 出错时默认为normal

def check_and_fix_state():
	"""检查并修复全局状态变量的一致性"""
	try:
		print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][状态修复] 开始检查并修复状态变量...")
		
		# 检查g.hold与g.curr_hold一致性
		if g.hold != 0 and g.curr_hold is None:
			print(f"警告: 检测到状态不一致 - g.hold={g.hold}但g.curr_hold为None")
			if g.hold == 1 and hasattr(g, 'call_one') and g.call_one is not None:
				print(f"尝试恢复多头持仓代码为: {g.call_one}")
				g.curr_hold = g.call_one
			elif g.hold == -1 and hasattr(g, 'put_one') and g.put_one is not None:
				print(f"尝试恢复空头持仓代码为: {g.put_one}")
				g.curr_hold = g.put_one
			else:
				print("无法恢复持仓代码，重置持仓状态")
				g.hold = 0
				g.remaining_hands = 0
				g.closed_ratio = 1.0
		
		# 检查g.remaining_hands与g.hold一致性
		if g.remaining_hands <= 0 and g.hold != 0:
			print(f"警告: 检测到状态不一致 - g.remaining_hands={g.remaining_hands}但g.hold={g.hold}")
			g.hold = 0
			g.closed_ratio = 1.0
			print("已修复: g.hold=0, g.closed_ratio=1.0")
			
		# 检查g.closed_ratio的有效性
		if g.closed_ratio < 0 or g.closed_ratio > 1:
			print(f"警告: 检测到无效的closed_ratio值: {g.closed_ratio}，重置为有效值")
			g.closed_ratio = min(max(g.closed_ratio, 0), 1)
			print(f"已修复: g.closed_ratio={g.closed_ratio}")
		
		# 检查g.remaining_hands与g.closed_ratio的一致性
		if g.hold != 0 and g.remaining_hands > 0:
			expected_closed_ratio = round(1 - (g.remaining_hands / hand), 2)
			if abs(g.closed_ratio - expected_closed_ratio) > 0.01:  # 允许0.01的误差
				print(f"警告: 检测到g.remaining_hands与g.closed_ratio不一致")
				print(f"remaining_hands={g.remaining_hands}, hand={hand}, 计算得closed_ratio应为{expected_closed_ratio}，实际为{g.closed_ratio}")
				g.closed_ratio = expected_closed_ratio
				print(f"已修复: g.closed_ratio={g.closed_ratio}")
				
		# 检查开仓价格有效性
		if g.hold != 0 and g.open_price <= 0 and g.curr_hold:
			print(f"警告: 检测到开仓价格无效({g.open_price})，尝试获取当前价格作为替代")
			try:
				market_price = get_option_price(g.curr_hold)
				if market_price > 0:
					g.open_price = market_price
					print(f"已设置开仓价格为当前市场价: {g.open_price}")
				else:
					g.open_price = 0.05  # 使用默认价格
					print(f"无法获取市场价格，使用默认价格: {g.open_price}")
			except:
				g.open_price = 0.05  # 使用默认价格
				print(f"获取市场价格异常，使用默认价格: {g.open_price}")
		
		print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][状态修复] 状态变量检查修复完成")
	except Exception as e:
		print(f"状态变量检查修复异常: {e}")

# 在每个周期结束检查持仓状态一致性
def check_position_consistency():
	"""
	检查持仓状态一致性并与实际账户同步
	"""
	try:
		print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 执行持仓状态一致性检查...")
		
		# 第一步：尝试获取实际账户持仓
		positions = get_positions(account)
		
		# 找到与策略相关的期权持仓
		found_option_position = False
		for pos in positions:
			if hasattr(pos, 'symbol') and pos.symbol:
				# 检查是否是期权持仓 (较长代码)
				if len(pos.symbol.split('.')[0]) >= 8:
					# 确认是当前跟踪的合约或者是策略可能的合约
					if (g.curr_hold and pos.symbol == g.curr_hold) or \
					   (g.call_one and pos.symbol == g.call_one) or \
					   (g.put_one and pos.symbol == g.put_one):
						found_option_position = True
						print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 账户中找到期权持仓: {pos.symbol}, 数量: {pos.current if hasattr(pos, 'current') else '未知'}")
						
						# 更新全局状态与账户一致
						# 确定持仓方向
						position_side = 1 if hasattr(pos, 'side') and pos.side > 0 else -1
						
						# 更新策略状态
						g.hold = position_side
						g.curr_hold = pos.symbol
						g.remaining_hands = pos.current if hasattr(pos, 'current') else hand
						
						# 更新相应的call_one或put_one
						if g.hold == 1:
							g.call_one = pos.symbol
						else:
							g.put_one = pos.symbol
						
						# 更新开仓价格 (如果可用)
						if hasattr(pos, 'cost') and pos.cost > 0:
							g.open_price = pos.cost
						
						# 根据剩余手数计算已平仓比例
						if g.remaining_hands < hand:
							g.closed_ratio = round(1 - (g.remaining_hands / hand), 2)
						else:
							g.closed_ratio = 0
						
						print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 已同步持仓状态: 方向={g.hold}, 代码={g.curr_hold}, 手数={g.remaining_hands}, 价格={g.open_price}, 已平仓比例={g.closed_ratio}")
						break
		
		# 如果账户中没有相关期权持仓，但策略认为有持仓，则清理策略状态
		if not found_option_position and g.hold != 0:
			print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 警告: 账户中未找到期权持仓，但策略状态显示有持仓，清理策略状态")
			g.hold = 0
			g.remaining_hands = 0
			g.open_price = 0
			g.closed_ratio = 1.0
			
			print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 持仓状态已重置: hold=0, remaining_hands=0, closed_ratio=1.0")
		
		# 检查g.hold与g.curr_hold一致性
		if g.hold != 0 and g.curr_hold is None:
			print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 警告: 检测到状态不一致 - g.hold={g.hold}但g.curr_hold为None，尝试修复")
			if g.hold == 1 and hasattr(g, 'call_one') and g.call_one:
				g.curr_hold = g.call_one
				print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 已修复: 设置g.curr_hold={g.curr_hold}")
			elif g.hold == -1 and hasattr(g, 'put_one') and g.put_one:
				g.curr_hold = g.put_one
				print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 已修复: 设置g.curr_hold={g.curr_hold}")
			else:
				print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 无法修复g.curr_hold，重置持仓状态")
				g.hold = 0
				g.remaining_hands = 0
				g.closed_ratio = 1.0
		
		# 检查开仓价格有效性
		if g.hold != 0 and g.open_price <= 0 and g.curr_hold:
			print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 警告: 检测到开仓价格无效({g.open_price})，尝试获取当前价格作为替代")
			try:
				market_price = get_option_price(g.curr_hold)
				if market_price > 0:
					g.open_price = market_price
					print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 已设置开仓价格为当前市场价: {g.open_price}")
				else:
					g.open_price = 0.05  # 使用默认价格
					print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 无法获取市场价格，使用默认价格: {g.open_price}")
			except:
				g.open_price = 0.05  # 使用默认价格
				print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 获取市场价格异常，使用默认价格: {g.open_price}")
		
		print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 持仓状态一致性检查完成")
	except Exception as e:
		print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][持仓检查] 持仓状态一致性检查异常: {e}")

# 增强获取期权价格的函数，确保可以获取实时价格
def get_option_price(option_code):
	"""
	获取期权当前市场价格
	如果无法获取价格，返回默认值0.05
	"""
	try:
		# 尝试多种方法获取期权价格
		# 方法1: 尝试获取最新行情
		try:
			import datetime
			full_code = option_code
			data = get_market_data_ex(['close'], [full_code], period='1m', count=1, subscribe=False)
			if full_code in data and not data[full_code].empty:
				price = data[full_code]['close'].iloc[-1]
				if price > 0:
					return price
		except Exception as e1:
			print(f"获取期权价格方法1失败: {e1}")
		
		# 方法2: 尝试使用get_full_tick
		try:
			tick = get_full_tick([option_code])
			if option_code in tick and 'lastPrice' in tick[option_code] and tick[option_code]['lastPrice'] > 0:
				return tick[option_code]['lastPrice']
		except Exception as e2:
			print(f"获取期权价格方法2失败: {e2}")
		
		# 如果都失败，返回默认值
		print(f"无法获取{option_code}的实时价格，使用默认值0.05")
		return 0.05
	except Exception as e:
		print(f"获取期权价格失败: {e}")
		return 0.05

# 增加持仓获取辅助函数
def get_positions(account_id):
	"""
	获取账户持仓信息
	返回持仓列表或空列表
	"""
	try:
		# 方法1: 标准API方法
		try:
			from xtquant.xttrader import XtQuantTrader
			trader = XtQuantTrader(account_id)
			positions = trader.query_stock_positions()
			if positions:
				print(f"通过XtQuantTrader获取到{len(positions)}个持仓")
				return positions
		except Exception as e1:
			print(f"XtQuantTrader获取持仓失败: {e1}")
		
		# 方法2: 使用交易上下文
		try:
			from xtquant import xtconsole
			ctx = xtconsole.get_trading_context()
			positions = ctx.get_positions(account_id)
			if positions:
				print(f"通过交易上下文获取到{len(positions)}个持仓")
				return positions
		except Exception as e2:
			print(f"通过交易上下文获取持仓失败: {e2}")
		
		# 方法3: 如果全局变量里有持仓信息，创建一个模拟持仓
		if 'g' in globals() and hasattr(g, 'hold') and g.hold != 0 and hasattr(g, 'curr_hold') and g.curr_hold:
			from collections import namedtuple
			Position = namedtuple('Position', ['symbol', 'current', 'side', 'cost'])
			
			pos = Position(
				symbol=g.curr_hold,
				current=g.remaining_hands if hasattr(g, 'remaining_hands') else hand,
				side=g.hold,
				cost=g.open_price if hasattr(g, 'open_price') and g.open_price > 0 else 0.05
			)
			print(f"通过全局变量创建模拟持仓: {pos}")
			return [pos]
		
		print("无法获取持仓信息")
		return []
	except Exception as e:
		print(f"获取持仓信息异常: {e}")
		return []

# 在handlebar函数中添加以下代码 (在现有handlebar函数末尾):
# 1. 在每次tick添加持仓状态输出
print(f"当前持仓状态: hold={g.hold}, 持仓代码={g.curr_hold}, 开仓价={g.open_price}, 剩余手数={g.remaining_hands}")

# 2. 每次handlebar调用时执行持仓一致性检查
check_position_consistency()

def order_callback(ContextInfo, orderInfo):
	"""
	订单回调函数，处理订单状态变化
	"""
	print(orderInfo.m_strRemark, orderInfo.m_nOrderStatus, orderInfo.m_nOffsetFlag, orderInfo.m_dTradedPrice)
	
	# 处理止盈止损订单
	if "止盈" in orderInfo.m_strRemark or "止损" in orderInfo.m_strRemark:
		# 订单完成或废单时，从跟踪列表中移除
		if orderInfo.m_nOrderStatus in [56, 57]:
			if orderInfo.m_strOrderSysID in g.profit_take_orders:
				g.profit_take_orders.remove(orderInfo.m_strOrderSysID)
				print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 止盈/止损订单状态更新: {orderInfo.m_strOrderSysID}, 状态: {orderInfo.m_nOrderStatus}, 已从跟踪列表移除")
		# 当订单完成或废单时，清理订单提交时间记录
		if orderInfo.m_strOrderSysID in g.order_submit_time:
			del g.order_submit_time[orderInfo.m_strOrderSysID]
			print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 订单状态更新，从时间跟踪列表移除: {orderInfo.m_strOrderSysID}, 状态: {orderInfo.m_nOrderStatus}")
		return
	
	if orderInfo.m_strRemark not in ['期权策略开多'+g.remark,'期权策略开空'+g.remark]:
		return
	
	marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(orderInfo.m_strExchangeID, orderInfo.m_strExchangeID)
	k = orderInfo.m_strInstrumentID+'.'+marekt
	if k not in [g.call_one, g.put_one]:
		return
	
	if orderInfo.m_nOrderStatus==56 and orderInfo.m_strRemark.startswith('期权策略开多'+g.remark):
		g.buy_long+=1
		print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {g.code} 开多次数+1 {g.buy_long}")
		# 重置止盈标记
		g.profit_taken_1 = False
		g.profit_taken_2 = False
		g.profit_taken_3 = False
	
	if orderInfo.m_nOrderStatus==56 and orderInfo.m_strRemark.startswith('期权策略开空'+g.remark):
		g.buy_short+=1
		print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {g.code} 开空次数+1 {g.buy_short}")
		# 重置止盈标记
		g.profit_taken_1_short = False
		g.profit_taken_2_short = False
		g.profit_taken_3_short = False

	if orderInfo.m_nOrderStatus==57 and orderInfo.m_nOffsetFlag == 48:
		g.hold = 0
		g.remaining_hands = 0
		print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] order_callback set g.hold=0, 订单失败")


def orderError_callback(ContextInfo, passOrderInfo, msg):
	"""
	订单错误回调函数，处理订单错误和失败
	"""
	try:
		print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][订单错误] 收到订单错误: {passOrderInfo.strategyName}, 错误信息: {msg}")
		
		# 处理开仓失败情况
		if '期权策略'+g.remark in passOrderInfo.strategyName:
			g.hold = 0
			g.remaining_hands = 0
			print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][订单错误] 持仓状态设置为0")
		
		if '期权策略开空'+g.remark in passOrderInfo.strategyName:
			g.buy_short+=1
			print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][订单错误][空头] {g.code} 开空次数+1: {g.buy_short}")
		
		if '期权策略开多'+g.remark in passOrderInfo.strategyName:
			g.buy_long+=1
			print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][订单错误][多头] {g.code} 开多次数+1: {g.buy_long}")
		
		# 处理止盈止损订单错误
		if "止盈" in passOrderInfo.strategyName or "止损" in passOrderInfo.strategyName:
			print(f"止盈/止损订单错误: {passOrderInfo.strategyName}, 错误信息: {msg}, 订单ID: {passOrderInfo.orderID}")
			# 从跟踪列表中移除
			if passOrderInfo.orderID in g.profit_take_orders:
				g.profit_take_orders.remove(passOrderInfo.orderID)
				print(f"已从跟踪列表移除错误订单: {passOrderInfo.orderID}")
			
			# 从订单时间跟踪字典中移除
			if passOrderInfo.orderID in g.order_submit_time:
				del g.order_submit_time[passOrderInfo.orderID]
	except Exception as e:
		print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][订单错误处理异常] {e}")
		import traceback
		traceback.print_exc()


def deal_callback(event):
    """
    成交回调函数, 处理交易执行
    """
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][成交回调] 收到成交回调: {event}")
    
    try:
        # 确保事件包含必要数据
        if not all(key in event for key in ['security', 'offset']):
            print("成交回调数据不完整")
            return {'status': 'error', 'msg': "成交回调数据不完整"}
        
        # 检查是否是我们的策略订单
        if not (event['security'] == g.call_one or event['security'] == g.put_one or event['security'] == g.curr_hold):
            print(f"非策略管理的合约成交，忽略: {event['security']}")
            return {'status': 'skip', 'msg': "非策略管理的合约"}
        
        # 开仓处理
        if event['offset'] == 'open':
            # 更新成交价格
            if 'price' in event and event['price'] > 0:
                g.open_price = event['price']
                g.hold_price = event['price']
                print(f"开仓成交，价格更新为: {g.open_price}")
            
            # 更新持仓状态
            if event['side'] == 'buy':
                g.hold = 1  # 多头持仓
                g.curr_hold = event['security']
                g.call_one = event['security']
                print(f"开多成交: {g.curr_hold}")
            elif event['side'] == 'sell':
                g.hold = -1  # 空头持仓
                g.curr_hold = event['security']
                g.put_one = event['security']
                print(f"开空成交: {g.curr_hold}")
            
            # 更新持仓手数
            g.remaining_hands = event['volume'] if 'volume' in event else hand
            print(f"开仓成交，剩余手数: {g.remaining_hands}")
            
            # 重置平仓比例
            g.closed_ratio = 0
            print(f"开仓成交，重置已平仓比例为0")
        
        # 平仓处理
        elif event['offset'] == 'close':
            # 确保持仓存在
            if g.hold == 0:
                print("警告: 收到平仓回报，但当前无持仓状态")
                # 尝试根据成交合约恢复持仓方向
                if event['security'] == g.call_one:
                    g.hold = 1
                    g.curr_hold = g.call_one
                elif event['security'] == g.put_one:
                    g.hold = -1
                    g.curr_hold = g.put_one
            
            # 更新剩余手数
            close_volume = event['volume'] if 'volume' in event else 0
            g.remaining_hands -= close_volume
            
            # 更新已平仓比例
            if g.remaining_hands <= 0:
                g.closed_ratio = 1.0  # 全部平仓
            else:
                # 根据剩余手数计算已平仓比例
                g.closed_ratio = round(1 - (g.remaining_hands / hand), 2)
            
            print(f"平仓成交，减少手数: {close_volume}, 剩余手数: {g.remaining_hands}, 已平仓比例: {g.closed_ratio}")
            
            # 如果全部平仓，更新状态
            if g.remaining_hands <= 0:
                g.hold = 0
                g.open_price = 0
                g.hold_price = 0
                g.remaining_hands = 0
                g.closed_ratio = 1.0
                print("全部平仓，重置持仓状态")
        
        # 检查状态一致性
        check_and_fix_state()
        
        # 记录当前持仓状态
        print(f"当前持仓状态: hold={g.hold}, "
              f"curr_hold={g.curr_hold}, "
              f"remaining_hands={g.remaining_hands}, "
              f"closed_ratio={g.closed_ratio}, "
              f"open_price={g.open_price}")
        
        return {'status': 'success', 'msg': "成交回调处理完成"}
    
    except Exception as e:
        print(f"成交回调处理异常: {e}")
        import traceback
        traceback.print_exc()
        # 尝试恢复状态一致性
        try:
            check_and_fix_state()
        except Exception as recovery_error:
            print(f"状态恢复失败: {recovery_error}")
        
        return {'status': 'error', 'msg': f"成交回调处理异常: {str(e)}"}
