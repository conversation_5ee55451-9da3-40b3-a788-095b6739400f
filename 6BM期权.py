#encoding:gbk


open_long_num =2
open_short_num = 2
hand = 1  #交易张数
moving_tick = 0.020  # 移动止损点位  #期权的波动单位为0.0001
fs_line = 0.010 #  FS即时价位—分时均线价位<0.1
sleep_time = 5 #分钟 1分钟内不连续开同方向仓位
stoploss_ratio = -25 # % 止损比例% 盈亏比例低于该值时卖出
stopprofit_ratio = 25 #% 盈利比例% 盈亏比例高于该值时卖出

# ================= 新增条件5参数 =================
# 红白线策略参数（来自期权策略买方.py）
# 红线：EMA(EMA(HLC3, 13), 8)
# 白线：EMA(HLC3, 13)
# 开多条件：
# 1. 白线转红线：白线从红线下方穿过红线
# 2. 突破白线：价格突破白线
# 3. 红线支撑：价格在红线上方受到支撑
# 开空条件：
# 1. 红线转白线：红线从白线下方穿过白线
# 2. 跌破红线：价格跌破红线
# 3. 白线压制：价格在白线下方受到压制
# ===============================================

# ================= 新增优化参数 =================
# 期权选择参数
optimal_days_to_expiry_min = 7  # 最佳到期日区间（最小值）- 降低到7天
optimal_days_to_expiry_max = 60  # 最佳到期日区间（最大值）- 增加到60天
use_improved_option_selection = True  # 是否使用优化的期权选择
consider_next_month = True  # 是否考虑次月期权
consider_quarterly = True  # 是否考虑季月期权
detailed_option_logs = True  # 是否输出详细的期权选择日志

# 市场环境过滤参数
use_market_filter = True  # 是否使用市场环境过滤
vix_threshold = 25  # VIX指数阈值，高于此值视为高波动
market_trend_days = 20  # 判断大盘趋势的周期

# 多腿策略参数
use_multi_leg_strategy = True  # 是否使用多腿策略
spread_delta_threshold = 0.2  # 价差策略的Delta阈值

# 价差策略期权选择参数 - 调整档位参数
call_buy_otm_level = 2  # 牛市价差买入期权的实值档位（平值下方几档）
call_sell_otm_level = 2  # 牛市价差卖出期权的虚值档位（平值上方几档）
put_buy_otm_level = 2  # 熊市价差买入期权的实值档位（平值上方几档）
put_sell_otm_level = 2  # 熊市价差卖出期权的虚值档位（平值下方几档）
auto_adjust_levels = True  # 是否根据波动率自动调整档位
use_smart_option_selection = True  # 是否使用智能期权选择（基于隐含波动率、Delta和流动性）
fallback_to_any_available = True  # 如果找不到合适期权，则回退到任何可用期权

# 价差策略止盈止损参数
spread_stoploss_ratio = -15  # 价差策略止损比例（百分比）
spread_stopprofit_ratio = 15  # 价差策略止盈比例（百分比）
spread_max_loss_amount = 5000  # 价差策略最大亏损金额（元）
spread_risk_threshold = 0.8  # 价差风险阈值，当价差达到理论最大值的80%时平仓
short_option_max_profit = 80  # 卖出期权获利达到80%时平仓
max_adverse_move = 3  # 标的价格不利移动超过3%时考虑平仓
# ===============================================

import math
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from scipy.stats import norm


class G():
	pass


g = G()
g.buy_long = 0
g.buy_short = 0
g.hold = 0
g.hold_price = 0
g.open_price = 0
g.trace_time_long = 0
g.trace_time_short = 0
g.opened_t = []
g.hold_code = ''
# 价差策略相关变量
g.is_spread_strategy = False  # 是否使用了价差策略
g.spread_buy_code = ''  # 价差策略买入的期权代码
g.spread_sell_code = ''  # 价差策略卖出的期权代码
g.spread_buy_price = 0  # 价差策略买入期权的价格
g.spread_sell_price = 0  # 价差策略卖出期权的价格
g.spread_initial_value = 0  # 价差初始值
g.spread_direction = 0  # 价差方向：1=牛市价差，-1=熊市价差
g.spread_max_profit = 0  # 价差策略理论最大盈利
g.spread_max_loss = 0  # 价差策略理论最大亏损
g.undl_price_when_open = 0  # 开仓时的标的价格

# 自动策略选择参数
auto_strategy_selection = True  # 是否启用自动策略选择
# 自动策略选择阈值
vix_high_threshold = 25  # VIX高于此值时考虑使用价差策略
vix_low_threshold = 15   # VIX低于此值时倾向于使用单腿策略
market_trend_strength_threshold = 0.7  # 趋势强度阈值，超过此值时使用单腿策略

def init(ContextInfo):
	g.remark = ContextInfo.request_id[-10:]
	
	g.call_one = None
	g.put_one = None
	ContextInfo.set_account(account)
	g.undl_code = g.code = g.stock = ContextInfo.stockcode+'.'+ContextInfo.market
	g.curr_hold = None
	
	# 初始化调试变量
	g.debug_mode = True
	g.last_order_time = 0
	g.order_count = 0
	
	# 初始化策略类型
	g.current_strategy = "single_leg" if not use_multi_leg_strategy else "spread"


def after_init(ContextInfo):
	download_history_data(g.code,'1d','********','********')
	return ContextInfo.get_trading_dates('SHO','','********',count=2, period='1d')
	

def handlebar(ContextInfo):
	timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
	bar_date = timetag_to_datetime(timetag, '%Y%m%d%H%M%S')
	
		
	if not ContextInfo.is_last_bar():
		return
	price_1d=ContextInfo.get_market_data_ex(['close','open'],[g.code],
							period='1d',count=2,subscribe=False)[g.code]
	if price_1d.index[-1]==bar_date[:8]:
		CC = price_1d.iloc[-2]['close']
		OO = price_1d.iloc[-2]['open']
	else:
		CC = price_1d.iloc[-1]['close']
		OO = price_1d.iloc[-1]['open']
	
	start, end = after_init(ContextInfo)
	
	price = ContextInfo.get_market_data_ex(['close','amount','volume','high','low'],[g.code],period='1m',
					start_time=end+'093000',
					end_time=end+'150000',
					)[g.code]
	C = CLOSE = price['close']
	price_len = price.shape[0]
	if price_len<=20:
		return
	t = price.index[-1][-6:-2]
	if t<='0930':# 9：56开始执行策略
		return
		
	# 市场环境过滤
	if use_market_filter:
		market_good = check_market_environment(ContextInfo)
		if not market_good:
			print("市场环境不佳，暂停交易")
			return
	
	# 选择最佳策略类型
	best_strategy, strategy_reason = select_best_strategy(ContextInfo)
	if auto_strategy_selection:
		# 根据自动选择结果设置策略类型
		g.current_strategy = best_strategy
		use_spread = (best_strategy == "spread")
		print(f"策略选择: {best_strategy} (使用{'价差' if use_spread else '单腿'}策略), 原因: {strategy_reason}")
	else:
		# 使用配置中设置的策略类型
		g.current_strategy = "spread" if use_multi_leg_strategy else "single_leg"
		use_spread = use_multi_leg_strategy
			
	CG,FL,MA_Line,FS,B1,B2 = cal_vba(ContextInfo, price)
	first, last_time = get_shape(CG,FL,MA_Line,FS,B1,B2)
	pct = time.strftime("%H%M%S")
	b1 = not pct >='145400' # 收盘前10分钟清仓
	if any([math.isnan(CG[i]) for i in [-1,-2,-3,-4,-5]] ):
		return
	print(f'{g.code} {t} c:{C[-1]} 均线：{MA_Line[-1]} B1:{B1[-3:]} FS:{FS[-1]} 昨CC:{CC} 昨OO:{OO} ')
	
	last_five_minute_long = all([CG[i]==FL[i]==FS[i] for i in (-1,)])
	ContextInfo.paint('B1',B1[-1],-1,0,)
	ContextInfo.paint('B2',B2[-1],-1,0,)
	last_five_minute_short = all([CG[i]!=FL[i] and CG[i]!=FS[i] and FL[i]!=FS[i] for i in (-1,)])
	# 条件1 买认购
	# 1.现价线和FS线（单红色线）在分时均线上方 且 B1-B2>0
	# 2.首次出现三色线，且在分时均线上方持续时间<15分钟，再度变成单红色线
	# 3.FS即时价位—分时均线价位<5
	
	#现价线和FS线（单红色线）在分时均线上方 且 B1-B2>0
	#fs金叉ma
	cross_up =FS[-1]>MA_Line[-1] and FS[-2]<=MA_Line[-2] and B1[-1]-B2[-1]>0
	#开多 
	tj_long = first==1 and last_time<=15 and last_five_minute_long and C[-1]>MA_Line[-1] and FS[-1] > MA_Line[-1] and B1[-1]-B2[-1]>0 and FS[-1]-MA_Line[-1]<fs_line
	# 开多
	
	# 条件3 # 买多时，绿色（B1<B2）不能超过15分钟
	b12 = list(B1<B2)
	b12kong = list(B1>B2)
	#b12.reverse()
	#print(b12)
	#tj3_long = B1[-1]>20 and B1[-2]<=20 and not all(b12kong[-10:-1]) and MA_Line[-1]-FS[-1]>fs_line
	tj3_long = B1[-1]>80 and B1[-2]<=80 and not all(b12[-10:-1]) and C[-1]-MA_Line[-1]>=fs_line
	kaiduo =  (tj_long or tj3_long) and C[-1]>(CC+OO)/2
	#kaiduo =  tj3_long

	first, last_time = get_shape_kong(CG,FL,MA_Line,FS,B1,B2)

	#fs死叉
	cross_down =FS[-1]<MA_Line[-1] and FS[-2]>=MA_Line[-2] and B1[-1]-B2[-1]<0
	# 条件1 买认购
	# 1.现价线和FS线（单红色线）在分时均线上方 且 B1-B2>0
	# 2.首次出现三色线，且在分时均线上方持续时间<15分钟，再度变成单红色线
	# 3.FS即时价位—分时均线价位<5
	tj_short = first==1 and last_time<=15 and last_five_minute_short and C[-1]<MA_Line[-1] and max(FS[-1],CG[-1],FL[-1]) < MA_Line[-1]  and B1[-1]-B2[-1]<0 and MA_Line[-1] - FS[-1]<fs_line

	#开空 条件1 ，吗，买认沽
	# 1.现价线和FS CG FL（三色线）在分时均线下方 且B1-B2<0
	# 2.首次出现单条红色线，且在分时均线下方持续时间<15分钟，再度变成三色线
	# 3.分时均线价位—FS即时价位<5
	# 条件3 # 买空时，红色（B1>B2）不能超过15分钟
	#tj3_short = B1[-2]<80 and B1[-3]>=80 and not all(b12[-10:-1]) and FS[-1]>MA_Line[-1] and C[-1]-MA_Line[-1]>fs_line
	tj3_short = B1[-1]>20 and B1[-2]<=20 and not all(b12kong[-10:-1]) and MA_Line[-1]-C[-1]>=fs_line
	kaikong = (tj3_short or tj_short) and C[-1]<(CC+OO)/2
	#kaikong = tj3_short
	
	# 新增条件5：从期权策略买方.py中引入的开多和开空条件
	try:
		# 计算红白线指标
		white_line_5m, red_line_5m = calculate_red_white_lines_exact(price)
		final_white_line = white_line_5m.iloc[-1] if not pd.isna(white_line_5m.iloc[-1]) else None
		final_red_line = red_line_5m.iloc[-1] if not pd.isna(red_line_5m.iloc[-1]) else None
		current_price = C[-1]
		
		# 计算均线
		MA_Line_5m = calculate_ma(price['close'], price['volume'], 20)
		
		# 判断趋势
		trend_5m = check_trend(price, MA_Line_5m.iloc[-1])
		
		# 检测红白线转换信号
		transition_signal = detect_line_transition(
			white_line_5m.iloc[-2] if len(white_line_5m) > 1 else None,
			white_line_5m.iloc[-1],
			red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None,
			red_line_5m.iloc[-1]
		)
		
		# 打印红白线值
		print(f"红白线值 - 红线:{final_red_line if final_red_line is None else round(final_red_line, 4)}, 白线:{final_white_line if final_white_line is None else round(final_white_line, 4)}, 价格:{round(current_price, 4)}, 均线:{round(MA_Line_5m.iloc[-1], 4) if not pd.isna(MA_Line_5m.iloc[-1]) else None}")
		
		# 定义信号变量
		transition_long = transition_signal == "white_to_red"
		transition_short = transition_signal == "red_to_white"
		pullback_long_good = False
		pullback_short_good = False
		break_red_short = False
		break_white_long = False
		
		# 检查是否突破白线
		if final_white_line is not None and not pd.isna(final_white_line):
			if current_price > final_white_line:
				break_white_long = True
		
		# 检查是否回调到红线做多
		if final_red_line is not None and not pd.isna(final_red_line):
			if current_price > final_red_line:
				pullback_long_good = True
		
		# 检查是否反弹到白线做空
		if final_white_line is not None and not pd.isna(final_white_line):
			if current_price < final_white_line:
				pullback_short_good = True
		
		# 检查是否跌破红线
		if final_red_line is not None and not pd.isna(final_red_line):
			if current_price < final_red_line:
				break_red_short = True
		
		# 定义新的开多条件
		kaiduo_new = ((pullback_long_good or transition_long or break_white_long) and 
					current_price > MA_Line_5m.iloc[-1] and 
					g.buy_long < open_long_num and 
					b1 and 
					trend_5m == 1)
		
		# 定义新的开空条件
		kaikong_new = ((pullback_short_good or transition_short or break_red_short) and 
					current_price < MA_Line_5m.iloc[-1] and 
					g.buy_short < open_short_num and 
					b1 and 
					trend_5m == -1)
		
		# 合并条件
		kaiduo = kaiduo or kaiduo_new
		kaikong = kaikong or kaikong_new
		
		print(f"条件5 - 新增红白线策略: 开多={kaiduo_new}, 开空={kaikong_new}")
	except Exception as e:
		print(f"条件5计算出错: {e}")
		# 如果条件5计算出错，不影响原有条件

	if g.hold== 0 and b1 and g.buy_long<open_long_num and kaiduo:
		buy_long = True
	else:
		buy_long = False
	if g.hold ==0 and b1 and g.buy_short<open_short_num and kaikong:
		buy_short = True
	else:
		buy_short = False

	if buy_long and time.time()-g.trace_time_long>sleep_time*60 and t not in g.opened_t and g.hold !=1:
		if g.hold == -1:
			debugpassorder(51, 1101, account, g.put_one, 12, 0, hand, '',1,'策略平空'+g.remark,ContextInfo)
		
		# 使用优化的期权选择函数
		if use_improved_option_selection:
			call_one, put_one = get_option_real_one_improved(ContextInfo)
		else:
			call_one, put_one = get_option_real_one(ContextInfo)
			
		# 根据当前策略决定是否使用多腿
		use_multi_leg = g.current_strategy == "spread" and should_use_spread_strategy(ContextInfo, "CALL")
		
		# 多腿策略判断
		if use_multi_leg:
			# 方式1: 自动选择档位
			# success = execute_spread_strategy(ContextInfo, "CALL", t)
			
			# 方式2: 手动指定档位 (买入实值2档，卖出虚值1档)
			# success = execute_spread_strategy(ContextInfo, "CALL", t, buy_level=2, sell_level=1)
			
			# 方式3: 使用select_best_options自动选择最佳合约
			# buy_call, buy_strike, sell_call, sell_strike = select_best_options(ContextInfo, "CALL")
			# 然后手动交易这些合约...
			
			# 默认使用自动选择档位
			success = execute_spread_strategy(ContextInfo, "CALL", t)
			if success:
				print("成功执行牛市价差策略")
				return
			else:
				print("牛市价差策略执行失败，回退到单腿策略")
			
		g.call_one = call_one
		g.buy_long+=1
		passorder(50, 1101, account, call_one, 12,0, hand,'',1,'期权策略开多'+g.remark,ContextInfo)
		g.curr_hold = call_one
		g.hold = 1
		g.trace_time_long = time.time()
		g.hold_price = 0
		g.open_price = 0
		g.opened_t.append(t)
		g.hold_code = call_one
		# 更新打印信息，包含新增条件的信息
		signal_info = ""
		try:
			if transition_long:
				signal_info += "白转红 "
			if break_white_long:
				signal_info += "突破白线 "
			if pullback_long_good:
				signal_info += "红线支撑 "
		except:
			signal_info = ""
		print(f'{call_one} 开多 原条件:{tj_long , tj3_long} {signal_info}')

	if buy_short and time.time()-g.trace_time_short>sleep_time*60 and t not in g.opened_t and g.hold !=-1:
		if g.hold == 1:
			passorder(51, 1101, account, g.call_one, 12, 0, hand, '',1,'策略平多'+g.remark,ContextInfo)
		
		# 使用优化的期权选择函数
		if use_improved_option_selection:
			call_one, put_one = get_option_real_one_improved(ContextInfo)
		else:
			call_one, put_one = get_option_real_one(ContextInfo)
		
		# 根据当前策略决定是否使用多腿
		use_multi_leg = g.current_strategy == "spread" and should_use_spread_strategy(ContextInfo, "PUT")
		
		# 多腿策略判断
		if use_multi_leg:
			# 方式1: 自动选择档位
			# success = execute_spread_strategy(ContextInfo, "PUT", t)
			
			# 方式2: 手动指定档位 (买入实值2档，卖出虚值1档)
			# success = execute_spread_strategy(ContextInfo, "PUT", t, buy_level=2, sell_level=1)
			
			# 方式3: 使用select_best_options自动选择最佳合约
			# buy_put, buy_strike, sell_put, sell_strike = select_best_options(ContextInfo, "PUT")
			# 然后手动交易这些合约...
			
			# 默认使用自动选择档位
			success = execute_spread_strategy(ContextInfo, "PUT", t)
			if success:
				print("成功执行熊市价差策略")
				return
			else:
				print("熊市价差策略执行失败，回退到单腿策略")
			
		g.put_one = put_one
		passorder(50, 1101, account, put_one, 12,0, hand,'',1,'期权策略开空'+g.remark,ContextInfo)
		# 更新打印信息，包含新增条件的信息
		signal_info = ""
		try:
			if transition_short:
				signal_info += "红转白 "
			if break_red_short:
				signal_info += "跌破红线 "
			if pullback_short_good:
				signal_info += "白线压制 "
		except:
			signal_info = ""
		print(f'{g.put_one} 开空 原条件:{tj_short or tj3_short} {signal_info}')
		g.curr_hold = put_one
		g.buy_short+=1
		g.opened_t.append(t)
		g.hold_price = 9999999
		g.open_price = 0
		g.hold = -1
		g.hold_code = put_one
		g.trace_time_short = time.time()

	if g.hold != 0:
		print(g.put_one, g.call_one, g.hold, g.open_price)
		
	if g.open_price >0:
		# 普通单腿策略的止盈止损逻辑
		if not g.is_spread_strategy:
			full = ContextInfo.get_full_tick([g.curr_hold])[g.curr_hold]
			c = full['lastPrice']
			hold_ratio = round((c/g.open_price-1)*100,2)
			if g.hold>0:
				g.hold_price = max(c, g.hold_price)
				print(f'多 当前持有：{g.curr_hold},方向:{g.hold} 成本价:{g.open_price} 持有最高价：{g.hold_price} 最新价:{c} 盈亏比例:{hold_ratio}%')
			elif g.hold<0:
				g.hold_price = max(c, g.hold_price)
				print(f'空当前持有 {g.curr_hold},方向:{g.hold} 成本价:{g.open_price} 持有最低价：{g.hold_price} 最新价:{c} 盈亏比例:{hold_ratio}%')
			if g.hold ==1:
				g.hold_price = max(g.hold_price, c)
				# 移动止损：当盈利超过15%时，止损线上移至成本价的95%
				if hold_ratio > 15 and g.open_price > 0:
					dynamic_stoploss = max(stoploss_ratio, -5)  # 收紧止损
				else:
					dynamic_stoploss = calculate_dynamic_stoploss(g.call_one)
					
				if hold_ratio < dynamic_stoploss or hold_ratio > stopprofit_ratio:
					passorder(51, 1101, account, g.call_one, 12, 0, hand, '', 1, '', ContextInfo)
					print(f'{g.call_one} 平多，原因：{"止盈" if hold_ratio > stopprofit_ratio else "止损"}')
					g.hold = 0
			if g.hold ==-1:
				g.hold_price = max(g.hold_price, c)
				if hold_ratio<stoploss_ratio or hold_ratio>stopprofit_ratio:
					passorder(51, 1101, account, g.put_one, 12, 0, hand, '',1,'',ContextInfo)
					g.hold =0
					print(f'{g.put_one} 平空')
		# 价差策略的止盈止损逻辑
		else:
			# 计算价差当前价值和盈亏比例
			current_spread_value, spread_pnl_ratio, sell_option_pnl_ratio, undl_price_change_ratio = calculate_spread_value(ContextInfo)
			
			# 打印价差策略信息
			print(f"价差策略 - 当前价差值: {current_spread_value:.4f}, 盈亏比例: {spread_pnl_ratio:.2f}%, 卖出期权盈亏: {sell_option_pnl_ratio:.2f}%, 标的变动: {undl_price_change_ratio:.2f}%")
			
			# 判断是否需要平仓
			should_close = False
			close_reason = ""
			
			# 1. 价差整体盈亏超过阈值
			if spread_pnl_ratio < spread_stoploss_ratio:
				should_close = True
				close_reason = f"价差止损 ({spread_pnl_ratio:.2f}%)"
			elif spread_pnl_ratio > spread_stopprofit_ratio:
				should_close = True
				close_reason = f"价差止盈 ({spread_pnl_ratio:.2f}%)"
				
			# 2. 卖出期权盈利达到阈值（卖出期权获利达到80%时平仓）
			if sell_option_pnl_ratio > short_option_max_profit:
				should_close = True
				close_reason = f"卖出期权高获利 ({sell_option_pnl_ratio:.2f}%)"
				
			# 3. 标的价格不利移动超过阈值
			if (g.spread_direction == 1 and undl_price_change_ratio < -max_adverse_move) or \
			   (g.spread_direction == -1 and undl_price_change_ratio > max_adverse_move):
				should_close = True
				close_reason = f"标的价格不利移动 ({undl_price_change_ratio:.2f}%)"
				
			# 4. 价差接近理论最大盈利（获利达到理论最大值的80%）
			if g.spread_max_profit > 0:
				profit_ratio = (current_spread_value - g.spread_initial_value) / g.spread_max_profit
				if profit_ratio > spread_risk_threshold:
					should_close = True
					close_reason = f"接近理论最大盈利 ({profit_ratio*100:.2f}%)"
			
			# 执行平仓
			if should_close:
				if g.hold == 1:  # 牛市价差
					debugpassorder(51, 1101, account, g.spread_buy_code, 12, 0, hand, '', 1, '平仓买入腿', ContextInfo)
					debugpassorder(50, 1101, account, g.spread_sell_code, 12, 0, hand, '', 1, '平仓卖出腿', ContextInfo)
					print(f"平仓牛市价差策略，原因: {close_reason}")
					g.hold = 0
					g.is_spread_strategy = False
				elif g.hold == -1:  # 熊市价差
					debugpassorder(51, 1101, account, g.spread_buy_code, 12, 0, hand, '', 1, '平仓买入腿', ContextInfo)
					debugpassorder(50, 1101, account, g.spread_sell_code, 12, 0, hand, '', 1, '平仓卖出腿', ContextInfo)
					print(f"平仓熊市价差策略，原因: {close_reason}")
					g.hold = 0
					g.is_spread_strategy = False
	
	if not b1:
		if g.hold !=0:
			orders = get_trade_detail_data(account,'STOCK','STOCK_OPTION')
			for o in orders:
				if o.m_nOrderStatus  in [50,55]: # 委托可撤时再撤单
					cancel(o.m_strOrderSysID, account, 'stock', ContextInfo)
			time.sleep(1)
			
			# 尾盘平仓，区分普通策略和价差策略
			if not g.is_spread_strategy:
				passorder(51, 1101, account, g.hold_code, 12, 0, hand, '',1,'',ContextInfo) 
				print('尾盘平仓',g.hold_code)
			else:
				if g.hold == 1:  # 牛市价差
					debugpassorder(51, 1101, account, g.spread_buy_code, 12, 0, hand, '', 1, '尾盘平仓买入腿', ContextInfo)
					debugpassorder(50, 1101, account, g.spread_sell_code, 12, 0, hand, '', 1, '尾盘平仓卖出腿', ContextInfo)
					print(f"尾盘平仓牛市价差策略 - 买入: {g.spread_buy_code}, 卖出: {g.spread_sell_code}")
				elif g.hold == -1:  # 熊市价差
					debugpassorder(51, 1101, account, g.spread_buy_code, 12, 0, hand, '', 1, '尾盘平仓买入腿', ContextInfo)
					debugpassorder(50, 1101, account, g.spread_sell_code, 12, 0, hand, '', 1, '尾盘平仓卖出腿', ContextInfo)
					print(f"尾盘平仓熊市价差策略 - 买入: {g.spread_buy_code}, 卖出: {g.spread_sell_code}")
				g.is_spread_strategy = False
			
			g.hold = 0


def get_shape(CG,FL,MA_Line,FS,B1,B2):
	# 2.首次出现三色线，且在分时均线上方持续时间<15分钟，再度变成单红色线
	count = 0
	record = []
	compare_ma = []
	for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
		if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
			continue
		record.append(cg==fl==fs)
		compare_ma.append(min(cg,fl,fs)>ma)
	pre=None
	record.reverse()
	compare_ma.reverse()
	i = 0
	if not record:
		return 0, 99
	if not record[0]:
		return 0, 99
	if not compare_ma[0]:
		return 0, 99
	# 获取大于均线的三条线
	uprecord = []
	for r, cpma in zip(record, compare_ma):
		if not cpma:
			break
		uprecord.append(r)
	# 去除重复连续
	drop_uprecord = []
	for i in range(len(uprecord)):
		if i == 0 or uprecord[i] != uprecord[i-1]:
			drop_uprecord.append(uprecord[i])
	if drop_uprecord.count(False)!=1:
		return 0, 99
	else:
		return 1, uprecord.count(False)

# 1.现价线和FS CG FL（三色线）在分时均线下方 且B1-B2<0
# 2.首次出现单条红色线，且在分时均线下方持续时间<15分钟，再度变成三色线
# 3.分时均线价位—FS即时价位<5
def get_shape_kong(CG,FL,MA_Line,FS,B1,B2):
	count = 0
	record = []
	compare_ma = []
	for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
		if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
			continue
		record.append(not cg==fl==fs)
		compare_ma.append(max(cg,fl,fs)<ma)
	# record True 三色线
	pre=None
	record.reverse()
	compare_ma.reverse()
	i = 0
	if not record:
		return 0, 99
	if not record[0]:
		return 0, 99
	if not compare_ma[0]:
		return 0, 99
	
	uprecord = []
	for r, cpma in zip(record, compare_ma):
		if not cpma:
			break
		uprecord.append(r)
	# 去除重复连续
	drop_uprecord = []
	for i in range(len(uprecord)):
		if i == 0 or uprecord[i] != uprecord[i-1]:
			drop_uprecord.append(uprecord[i])
	if drop_uprecord.count(False)!=1:
		return 0, 99
	else:
		return 1, uprecord.count(False)


def get_shape_old(CG,FL,MA_Line,FS,B1,B2):
	#判断是不是首次出现的三色线
	#CG[i]==FL[i]==FS[i]
	count = 0
	record = []
	compare_ma = []
	for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
		if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
			continue
		record.append(cg==fl==fs)
		compare_ma.append(min(cg,fl,fs)>ma)
	pre=None
	for pos, b in enumerate(record):
		if pre is None:
			pre = b
			continue
		if b and pre==False:
			count += 1
			pre = b
			continue
		if not b and pos == len(record) -1:
			count+=1
			pre = b
			continue
		pre = b
	return count, sum([1 for r, cp in zip(record, compare_ma) if not r and cp])


def get_shape_kong_old(CG,FL,MA_Line,FS,B1,B2):
	#判断是不是首次出现的三色线
	#CG[i]==FL[i]==FS[i]
	count = 0
	record = []
	compare_ma = []
	for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
		if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
			continue
		record.append(not cg==fl==fs)
		compare_ma.append(min(cg,fl,fs)<ma)
	pre=None
	for pos, b in enumerate(record):
		if pre is None:
			pre = b
			continue
		if b and pre==False:
			count += 1
			pre = b
			continue
		if not b and pos == len(record) -1:
			count+=1
			pre = b
			continue
		pre = b
	return count, sum([1 for r, cp in zip(record, compare_ma) if not r and cp])


def order_callback(ContextInfo, orderInfo):
	print(orderInfo.m_strRemark, orderInfo.m_nOrderStatus, orderInfo.m_nOffsetFlag,orderInfo.m_dTradedPrice)
	if orderInfo.m_strRemark not in  ['期权策略开多'+g.remark,'期权策略开空'+g.remark]:
		return
	marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(orderInfo.m_strExchangeID, orderInfo.m_strExchangeID)
	k = orderInfo.m_strInstrumentID+'.'+marekt
	if k not in [g.call_one, g.put_one]:
		return
	#if orderInfo.m_nOrderStatus==56 and orderInfo.m_nOffsetFlag == 48 and orderInfo.m_dTradedPrice>0:
	#	g.open_price = round(orderInfo.m_dTradedPrice,1)
	#	g.hold_price = round(orderInfo.m_dTradedPrice,1)
	if orderInfo.m_nOrderStatus==56 and orderInfo.m_strRemark.startswith('期权策略开多'+g.remark):
		g.buy_long+=1
		print(f"{g.code} 开多次数+1 {g.buy_long}")
	if orderInfo.m_nOrderStatus==56 and orderInfo.m_strRemark.startswith('期权策略开空'+g.remark):
		g.buy_short+=1
		print(f"{g.code} 开空次数+1 {g.buy_short}")

	if orderInfo.m_nOrderStatus==57 and orderInfo.m_nOffsetFlag == 48:
		g.hold =0
		print("order_callback set 0")

def orderError_callback(ContextInfo,passOrderInfo,msg):
	if '期权策略'+g.remark in passOrderInfo.strategyName:
		g.hold =0
		print("orderError_callback set 0", msg)
	if '期权策略开空'+g.remark in passOrderInfo.strategyName:
		g.buy_short+=1
		print(f"{g.code} 开空次数+1 {g.buy_short}")
	if '期权策略开多'+g.remark in passOrderInfo.strategyName:
		g.buy_long+=1
		print(f"{g.code} 开多次数+1 {g.buy_long}")



def deal_callback(ContextInfo, dealInfo):
	print(f"deal callback m_nOffsetFlag:[{dealInfo.m_nOffsetFlag}] m_strRemark:[{dealInfo.m_strRemark}], [{dealInfo.m_strInstrumentID}] [{dealInfo.m_strExchangeID}]  [{dealInfo.m_dPrice}]",)
	if dealInfo.m_strRemark not in  ['期权策略开多'+g.remark,'期权策略开空'+g.remark]:
		return
	
	marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
	k = dealInfo.m_strInstrumentID+'.'+marekt
	print(k in [g.call_one, g.put_one],g.code.find(dealInfo.m_strInstrumentID))
	if k not in [g.call_one, g.put_one]:
		return
	if dealInfo.m_nOffsetFlag == 48:
		print("deal callback", dealInfo.m_dPrice)
		g.open_price = round(dealInfo.m_dPrice, 4)
		g.hold_price = round(dealInfo.m_dPrice,4)
def REF(S, N=1):          #对序列整体下移动N,返回序列(shift后会产生NAN)    
    return pd.Series(S).shift(N).values  
def SMA(S, N, M=1):       #中国式的SMA,至少需要120周期才精确 (雪球180周期)    alpha=1/(1+com)    
    return pd.Series(S).ewm(alpha=M/N,adjust=False).mean().values           #com=N-M/M
def SUM(S, N):            #对序列求N天累计和，返回序列    N=0对序列所有依次求和         
    return pd.Series(S).rolling(N).sum().values if N>0 else pd.Series(S).cumsum().values  
def HHV(S,N):             #HHV(C, 5) 最近5天收盘最高价        
    return pd.Series(S).rolling(N).max().values     

def LLV(S,N):             #LLV(C, 5) 最近5天收盘最低价     
    return pd.Series(S).rolling(N).min().values    
    
def HHVBARS(S,N):         #求N周期内S最高值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]),raw=True).values 

def LLVBARS(S,N):         #求N周期内S最低值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]),raw=True).values    
  
def MA(S,N):              #求序列的N日简单移动平均值，返回序列                    
    return pd.Series(S).rolling(N).mean().values  
def EMA(S,N):             #指数移动平均,为了精度 S>4*N  EMA至少需要120周期     alpha=2/(span+1)    
    return pd.Series(S).ewm(span=N, adjust=False).mean().values     

def EMA(source, N:int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    """
    # if not isinstance(source, np.ndarray):
    #     source = np.array(source)
    M = 2
    if N<M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N-1
    M = 2
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M*x+d*temp)/(N+1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
	index = S.index
	result_list = []
	slist = list(S)

	for i in range(len(S.index)):
		l = slist[max(0, i+1-34):i+1]
		result_list.append(min(l))
	return pd.Series(result_list, index=index)
	
	
def PyHHV(S, N):
	index = S.index
	result_list = []
	slist = list(S)

	for i in range(len(S.index)):
		l = slist[max(0, i+1-34):i+1]
		result_list.append(max(l))
	return pd.Series(result_list, index=index)

def cal_vba(ContextInfo, price):
	C = CLOSE = price['close']
	HIGH = price['close']
	LOW = price['close']
	AMT = price['amount']
	VOL = price['volume']
	MA_Line = SUM(C*VOL,0)/SUM(VOL, 0)
	CG=MA(C,21)
	FL=HHV(CG,3)
	FS=CG-(FL-CG)
	
	VA6=(2*CLOSE+HIGH+LOW)/4
	VA8=LLV(LOW,34)
	VARB=HHV(HIGH,34)
	VARC=EMA((VA6-VA8)/(VARB-VA8)*100,13)
	VARD=EMA(0.667*REF(VARC,1)+0.333*VARC,2)
	生命线:EMA(VARD,10)

	VAR1=HHV(HIGH,9)-LLV(LOW,9)
	VAR2=HHV(HIGH,9)-CLOSE
	VAR3=CLOSE-LLV(LOW,9)
	VAR4=((VAR2)/(VAR1))*(100)-70
	VAR5=((CLOSE-LLV(LOW,60))/(HHV(HIGH,60)-LLV(LOW,60)))*(100)


	VAR6=((2)*(CLOSE)+HIGH+LOW)/(4)
	index = VAR6.index
	VAR6 = pd.Series([v for v in VAR6],index=index)
	VAR7=SMA(((VAR3)/(VAR1))*(100),3,1)
	VAR8=PyLLV(CLOSE,min(34, len(LOW)))
	VAR9=SMA(VAR7,3,1)-SMA(VAR4,9,1)
	VAR10 = pd.Series([v-100 if v>100 else 0 for v in VAR9])
	VAR11=PyHHV(CLOSE,min(34, len(HIGH)))
	vv = ((VAR6-VAR8)/(VAR11-VAR8))*(100)
	vv=vv.fillna(0)
	vv=vv.replace([np.inf, -np.inf], np.nan).fillna(0)
	B1=EMA(vv,8)
	B2=EMA(B1,5)
	print(VAR6[-1],VAR11[-1],VAR8[-1])
	return CG,FL,MA_Line,FS,B1,B2


def get_option_real_one(ContextInfo):
	call_one = put_one = None
	now = time.strftime("%Y%m%d")
	call_list = get_current_month_option(ContextInfo, g.undl_code, now,'CALL')

	# call_list = get_current_month_option(ContextInfo, g.undl_code, '20231011','PUT')
	# print(len(call_list),call_list)
	# print([ContextInfo.get_instrumentdetail(s)['InstrumentName']for s in call_list])
	undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
	#实值实值认购一档
	call_dict = {call:ContextInfo.get_option_detail_data(call)['OptExercisePrice'] for call in call_list}
	real_list = {code for code in call_dict if call_dict[code]<undl_price }
	print("购实值 所有",real_list)
	real_list = sorted(real_list,key=lambda code:call_dict[code], reverse=True) #小于标的现价的行权价最大的票
	if real_list:
		print("购实一", real_list[0])
		call_one = real_list[0]

	call_list = get_current_month_option(ContextInfo, g.undl_code, now,'PUT')
	print(call_list)
	undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
	#市值认沽一档
	call_dict = {call:ContextInfo.get_option_detail_data(call)['OptExercisePrice'] for call in call_list}
	real_list = {code for code in call_dict if call_dict[code]>undl_price }
	print("沽 实值 所有",real_list)
	real_list = sorted(real_list,key=lambda code:call_dict[code], reverse=False) #小于标的现价的行权价最大的票
	if real_list:
		print("沽实一", real_list[0])
		put_one = real_list[0]
	return call_one, put_one


def get_current_month_option(ContextInfo, object, dedate, opttype=""):
	#dedate 日期 %Y%m%d 
	#获取截止到ddate这天还未到行权日的期权合约（不限于当月）
	isavailavle = True
	result = []
	opt_by_month = {}
	undlMarket = "";
	undlCode = "";
	marketcodeList = object.split('.');
	if(len(marketcodeList) !=2):
		print(f"错误的标的代码格式: {object}")
		return [];
	undlCode = marketcodeList[0]
	undlMarket = marketcodeList[1];
	market = ""
	if(undlMarket == "SH"):
		if undlCode == "000016" or undlCode == "000300" or undlCode == "000852" or undlCode == "000905":
			market = 'IF'
		else:
			market = "SHO"
	elif(undlMarket == "SZ"):
		market = "SZO";
	if(opttype.upper() == "C"):
		opttype = "CALL"
	elif(opttype.upper() == "P"):
		opttype = "PUT"
	optList = []
	print(f"获取期权列表，标的: {object}, 日期: {dedate}, 类型: {opttype}, 市场: {market}")
	
	# 改进期权列表获取，确保能获取到期权
	try:
		if market == 'SHO':
			optList += get_stock_list_in_sector('上证期权')
			print(f"获取上证期权列表，数量: {len(optList)}")
		elif market == 'SZO':
			optList += get_stock_list_in_sector('深证期权')
			print(f"获取深证期权列表，数量: {len(optList)}")
		elif market == 'IF':
			optList += get_stock_list_in_sector('中金所')
			print(f"获取中金所期权列表，数量: {len(optList)}")
		
		# 如果没有获取到任何期权，尝试获取所有期权
		if not optList:
			print("未获取到任何期权，尝试获取所有交易所的期权")
			optList += get_stock_list_in_sector('上证期权')
			optList += get_stock_list_in_sector('深证期权')
			optList += get_stock_list_in_sector('中金所')
			print(f"获取所有期权列表，总数量: {len(optList)}")
	except Exception as e:
		print(f"获取期权列表出错: {e}")
		# 如果获取期权列表出错，返回空列表
		return []
	
	for opt in optList:
		if(opt.find(market) < 0):
			continue
		try:
			inst = ContextInfo.get_option_detail_data(opt)
			if('optType' not in inst):
				continue
			endDate = inst['EndDelivDate']
			if(isavailavle and str(endDate) <= dedate):
				continue
			if(opttype.upper() != "" and opttype.upper() != inst["optType"]):
				continue
			
			# 放宽开盘日期条件
			createDate = inst.get('OpenDate', 0)
			openDate = inst.get('OpenDate', 0)
			if(createDate >= 1):
				openDate = min(openDate, createDate)
			# 移除日期限制，确保能获取到更多期权
			# if(openDate < 20150101 or str(openDate) > dedate):
			#     continue
			
			if(inst['ProductID'].find(undlCode) > 0 or inst['OptUndlCode'] == undlCode):
				result.append(opt)
				month = str(endDate)[:6]
				if month not in opt_by_month:
					opt_by_month[month] = [opt]
				else:
					opt_by_month[month].append(opt)
		except Exception as e:
			print(f"处理期权时出错: {opt}, 错误: {e}")
			continue
	
	# 打印找到的期权月份和数量
	print(f"找到的期权月份: {list(opt_by_month.keys())}")
	for month, opts in opt_by_month.items():
		print(f"月份 {month}: {len(opts)}个期权")
	
	# 如果没有找到任何期权，返回空列表
	if not opt_by_month:
		print(f"未找到任何期权：{object}, {dedate}, {opttype}")
		return []
	
	# 返回逻辑，确保优先返回目标月份期权
	if len(dedate) == 8 and dedate.endswith('01'):
		target_month = dedate[:6]
		if target_month in opt_by_month:
			print(f"返回{target_month}月期权: {len(opt_by_month[target_month])}个")
			return opt_by_month[target_month]
		else:
			print(f"未找到{target_month}月期权")
			# 如果没有找到目标月份，返回最近的月份
			if opt_by_month:
				closest_month = min(opt_by_month.keys(), key=lambda x: abs(int(x) - int(target_month)))
				print(f"返回最近的{closest_month}月期权: {len(opt_by_month[closest_month])}个")
				return opt_by_month[closest_month]
	
	# 返回最近月份的期权
	opt_list = sorted(opt_by_month.keys())
	if opt_list:
		print(f"返回最近月份的期权: {opt_list[0]}, 数量: {len(opt_by_month[opt_list[0]])}个")
		return opt_by_month[opt_list[0]]
	else:
		print(f"{object} 未找到任何符合条件的期权")
		return []

def calculate_red_white_lines_exact(price_data, debug_mode=False):
    """
    计算红白线指标
    
    参数:
    price_data: DataFrame 包含价格数据的DataFrame，需要包含close,high,low列
    debug_mode: bool 是否输出调试信息
    
    返回:
    white_line: Series 白线数据
    red_line: Series 红线数据
    """
    try:
        # 获取必要的价格数据
        close = price_data['close']
        high = price_data['high'] if 'high' in price_data else price_data['close']
        low = price_data['low'] if 'low' in price_data else price_data['close']
        
        # 计算基础指标
        hl2 = (high + low) / 2
        hlc3 = (high + low + close) / 3
        
        # 计算红白线
        white_line = EMA(hlc3, 13)
        red_line = EMA(white_line, 8)
        
        # 转换为pandas Series
        white_line = pd.Series(white_line, index=price_data.index)
        red_line = pd.Series(red_line, index=price_data.index)
        
        if debug_mode:
            print("红白线计算成功")
            
        return white_line, red_line
    except Exception as e:
        print(f"计算红白线时出错: {e}")
        # 返回空Series
        empty = pd.Series([], index=price_data.index)
        return empty, empty

def check_trend(price_data, ma_value, lookback=5):
    """
    判断价格趋势
    
    参数:
    price_data: DataFrame 价格数据
    ma_value: float 均线值
    lookback: int 回看的K线数量
    
    返回:
    int: 1=上升趋势, -1=下降趋势, 0=盘整
    """
    try:
        # 获取收盘价
        close = price_data['close']
        
        # 计算最近几根K线的趋势
        recent_closes = close[-lookback:]
        
        # 如果大部分收盘价高于均线，认为是上升趋势
        above_ma_count = sum(recent_closes > ma_value)
        below_ma_count = sum(recent_closes < ma_value)
        
        # 计算收盘价的方向
        price_direction = 1 if close.iloc[-1] > close.iloc[-lookback] else -1
        
        # 综合判断趋势
        if above_ma_count > below_ma_count and price_direction > 0:
            return 1  # 上升趋势
        elif below_ma_count > above_ma_count and price_direction < 0:
            return -1  # 下降趋势
        else:
            return 0  # 盘整
    except Exception as e:
        print(f"判断趋势时出错: {e}")
        return 0  # 默认为盘整

def detect_line_transition(prev_white, curr_white, prev_red, curr_red):
    """
    检测红白线转换信号
    
    参数:
    prev_white: float 前一个时间点的白线值
    curr_white: float 当前时间点的白线值
    prev_red: float 前一个时间点的红线值
    curr_red: float 当前时间点的红线值
    
    返回:
    str: "white_to_red"=白线转红线, "red_to_white"=红线转白线, None=无转换
    """
    try:
        # 检查参数是否有效
        if prev_white is None or curr_white is None or prev_red is None or curr_red is None:
            return None
        
        if pd.isna(prev_white) or pd.isna(curr_white) or pd.isna(prev_red) or pd.isna(curr_red):
            return None
        
        # 白线转红线：之前白线在红线下方，现在白线在红线上方
        if prev_white < prev_red and curr_white > curr_red:
            return "white_to_red"
        
        # 红线转白线：之前红线在白线下方，现在红线在白线上方
        if prev_red < prev_white and curr_red > curr_white:
            return "red_to_white"
        
        return None
    except Exception as e:
        print(f"检测线转换时出错: {e}")
        return None

def check_break_red_line(price_data, red_line_prev, red_line_curr):
    """
    检查价格是否跌破红线
    
    参数:
    price_data: DataFrame 价格数据
    red_line_prev: float 前一个时间点的红线值
    red_line_curr: float 当前时间点的红线值
    
    返回:
    tuple: (是否跌破红线, 跌破幅度)
    """
    try:
        # 获取最近的收盘价
        close = price_data['close'].iloc[-1]
        
        # 如果红线不存在，无法判断
        if red_line_curr is None or pd.isna(red_line_curr):
            return False, 0
        
        # 计算价格与红线的比率
        price_red_ratio = (close / red_line_curr - 1) * 100
        
        # 判断是否跌破红线
        if close < red_line_curr:
            # 如果前一个红线值存在，判断是否是新跌破
            if red_line_prev is not None and not pd.isna(red_line_prev):
                prev_close = price_data['close'].iloc[-2]
                was_above = prev_close > red_line_prev
                
                if was_above:
                    return True, price_red_ratio  # 新跌破
                else:
                    return False, price_red_ratio  # 持续在红线下方
            else:
                # 无法判断是否是新跌破，但价格确实在红线下方
                return True, price_red_ratio
        else:
            # 价格在红线上方
            return False, price_red_ratio
    except Exception as e:
        print(f"检查跌破红线时出错: {e}")
        return False, 0

def calculate_ma(close, volume, period):
    """
    计算成交量加权移动平均线
    
    参数:
    close: Series 收盘价序列
    volume: Series 成交量序列
    period: int 周期
    
    返回:
    Series: 成交量加权移动平均线
    """
    try:
        # 确保输入数据有效
        if len(close) != len(volume):
            print("收盘价和成交量长度不匹配")
            return pd.Series(close)  # 返回原始收盘价作为备用
        
        # 计算成交量加权移动平均线
        weighted_price = close * volume
        ma = pd.Series(SUM(weighted_price, period) / SUM(volume, period), index=close.index)
        
        return ma
    except Exception as e:
        print(f"计算成交量加权移动平均线时出错: {e}")
        return pd.Series(close)  # 返回原始收盘价作为备用

def check_volume_increase(volume_data, threshold_base=1.2):
    """
    检查成交量是否放大
    
    参数:
    volume_data: Series 成交量数据
    threshold_base: float 成交量放大阈值基准
    
    返回:
    bool: 成交量是否放大
    """
    try:
        # 获取最近的成交量数据
        recent_volume = volume_data.iloc[-5:]
        
        # 计算平均成交量
        avg_volume = recent_volume.iloc[:-1].mean()
        
        # 获取最新成交量
        latest_volume = recent_volume.iloc[-1]
        
        # 判断成交量是否放大
        return latest_volume > avg_volume * threshold_base
    except Exception as e:
        print(f"检查成交量放大时出错: {e}")
        return False

def calculate_dynamic_stoploss(option_code, base_stoploss=-25):
    # 获取期权隐含波动率
    option_detail = ContextInfo.get_option_detail_data(option_code)
    implied_vol = option_detail.get('ImpliedVol', 0.3)  # 默认波动率30%
    
    # 根据波动率调整止损比例
    adjusted_stoploss = base_stoploss * (0.3 / max(implied_vol, 0.1))
    
    # 限制在合理范围内
    return max(min(adjusted_stoploss, -15), -35)

def get_option_real_one_improved(ContextInfo):
    """
    优化的期权选择函数，考虑到期日、隐含波动率和流动性
    - 放宽筛选条件
    - 考虑次月和季月期权
    - 添加详细日志
    """
    call_one = put_one = None
    now = time.strftime("%Y%m%d")
    
    # 获取当月、次月和季月期权
    call_list = []
    put_list = []
    
    # 获取当月期权
    current_month_calls = get_current_month_option(ContextInfo, g.undl_code, now, 'CALL')
    current_month_puts = get_current_month_option(ContextInfo, g.undl_code, now, 'PUT')
    
    call_list.extend(current_month_calls)
    put_list.extend(current_month_puts)
    
    print(f"当月期权 - 认购: {len(current_month_calls)}个, 认沽: {len(current_month_puts)}个")
    
    # 获取次月期权
    if consider_next_month:
        # 计算次月日期
        current_date = datetime.now()
        next_month = current_date.replace(day=1) + timedelta(days=32)
        next_month_str = next_month.strftime("%Y%m") + "01"  # 次月1号
        
        next_month_calls = get_current_month_option(ContextInfo, g.undl_code, next_month_str, 'CALL')
        next_month_puts = get_current_month_option(ContextInfo, g.undl_code, next_month_str, 'PUT')
        
        call_list.extend(next_month_calls)
        put_list.extend(next_month_puts)
        
        print(f"次月期权 - 认购: {len(next_month_calls)}个, 认沽: {len(next_month_puts)}个")
    
    # 获取季月期权
    if consider_quarterly:
        # 计算季月日期（当前月份+3个月）
        current_date = datetime.now()
        quarterly_month = current_date.replace(day=1) + timedelta(days=92)  # 约3个月
        quarterly_month_str = quarterly_month.strftime("%Y%m") + "01"  # 季月1号
        
        quarterly_month_calls = get_current_month_option(ContextInfo, g.undl_code, quarterly_month_str, 'CALL')
        quarterly_month_puts = get_current_month_option(ContextInfo, g.undl_code, quarterly_month_str, 'PUT')
        
        call_list.extend(quarterly_month_calls)
        put_list.extend(quarterly_month_puts)
        
        print(f"季月期权 - 认购: {len(quarterly_month_calls)}个, 认沽: {len(quarterly_month_puts)}个")
    
    if not call_list or not put_list:
        print("未找到任何期权合约，回退到原始方法")
        return get_option_real_one(ContextInfo)
    
    # 获取标的价格
    undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
    
    # 处理认购期权（看涨期权）
    filtered_calls = []
    for call in call_list:
        try:
            detail = ContextInfo.get_option_detail_data(call)
            # 计算到期日
            days_to_expiry = (datetime.strptime(str(detail['EndDelivDate']), '%Y%m%d') - datetime.now()).days
            # 获取行权价
            strike = detail['OptExercisePrice']
            
            # 筛选条件：
            # 1. 实值期权（行权价 < 标的价格）
            # 2. 到期日在理想区间内
            # 3. 有成交量（流动性）
            if (strike < undl_price and 
                optimal_days_to_expiry_min <= days_to_expiry <= optimal_days_to_expiry_max):
                
                # 获取成交量和隐含波动率
                tick_data = ContextInfo.get_full_tick([call])
                if call in tick_data:
                    volume = tick_data[call].get('volume', 0)
                    iv = detail.get('ImpliedVol', 1.0)
                    
                    if volume > 0:  # 确保有流动性
                        filtered_calls.append((call, strike, iv, days_to_expiry))
                        print(f"符合条件的认购期权: {call}, 行权价: {strike}, 隐含波动率: {iv:.2%}, 剩余天数: {days_to_expiry}")
        except Exception as e:
            print(f"处理认购期权时出错: {e}")
    
    print(f"筛选后的认购期权数量: {len(filtered_calls)}")
    
    # 选择最佳认购期权：
    # 1. 首选实值程度适中（接近平值）
    # 2. 隐含波动率较低
    # 3. 到期日适中
    if filtered_calls:
        # 按照行权价降序排序（取接近标的价格的）
        filtered_calls.sort(key=lambda x: (-x[1], x[2]))
        if filtered_calls:
            call_one = filtered_calls[0][0]
            print(f"选择的认购期权: {call_one}, 行权价: {filtered_calls[0][1]}, 剩余天数: {filtered_calls[0][3]}")
    
    # 处理认沽期权（看跌期权）
    filtered_puts = []
    for put in put_list:
        try:
            detail = ContextInfo.get_option_detail_data(put)
            days_to_expiry = (datetime.strptime(str(detail['EndDelivDate']), '%Y%m%d') - datetime.now()).days
            strike = detail['OptExercisePrice']
            
            # 筛选条件同上，但是认沽期权要求行权价 > 标的价格（实值认沽）
            if (strike > undl_price and 
                optimal_days_to_expiry_min <= days_to_expiry <= optimal_days_to_expiry_max):
                
                tick_data = ContextInfo.get_full_tick([put])
                if put in tick_data:
                    volume = tick_data[put].get('volume', 0)
                    iv = detail.get('ImpliedVol', 1.0)
                    
                    if volume > 0:
                        filtered_puts.append((put, strike, iv, days_to_expiry))
                        print(f"符合条件的认沽期权: {put}, 行权价: {strike}, 隐含波动率: {iv:.2%}, 剩余天数: {days_to_expiry}")
        except Exception as e:
            print(f"处理认沽期权时出错: {e}")
    
    print(f"筛选后的认沽期权数量: {len(filtered_puts)}")
    
    # 选择最佳认沽期权
    if filtered_puts:
        # 按照行权价升序排序（取接近标的价格的）
        filtered_puts.sort(key=lambda x: (x[1], x[2]))
        if filtered_puts:
            put_one = filtered_puts[0][0]
            print(f"选择的认沽期权: {put_one}, 行权价: {filtered_puts[0][1]}, 剩余天数: {filtered_puts[0][3]}")
    
    # 如果没有找到合适的期权，回退到原始方法
    if call_one is None or put_one is None:
        print("未找到符合条件的期权，回退到原始方法")
        return get_option_real_one(ContextInfo)
        
    print(f"优化选择 - 认购: {call_one}, 认沽: {put_one}")
    return call_one, put_one

def check_market_environment(ContextInfo):
    """
    检查市场环境，判断是否适合交易
    返回: True=市场环境良好，False=市场环境不佳
    """
    try:
        # 1. 获取大盘指数（以上证指数为例）
        index_code = "000001.SH"
        index_data = ContextInfo.get_market_data_ex(['close', 'volume'], [index_code], 
                                                  period='1d', count=market_trend_days+1, 
                                                  subscribe=False)
        
        if index_code not in index_data:
            print("无法获取大盘数据，默认市场环境良好")
            return True
            
        index_close = index_data[index_code]['close']
        
        # 2. 计算大盘趋势
        ma20 = index_close.rolling(market_trend_days).mean().iloc[-1]
        current_price = index_close.iloc[-1]
        
        # 3. 计算波动率（简化版VIX）
        returns = index_close.pct_change().dropna()
        volatility = returns.std() * np.sqrt(252) * 100  # 年化波动率
        
        # 4. 判断市场环境
        # 市场环境良好的条件：
        # - 大盘处于上升趋势（价格高于20日均线）或
        # - 波动率不高于阈值
        market_uptrend = current_price > ma20
        low_volatility = volatility <= vix_threshold
        
        market_good = market_uptrend or low_volatility
        
        # 打印市场环境信息
        trend_str = "上升" if market_uptrend else "下降"
        vol_str = "正常" if low_volatility else "过高"
        print(f"市场环境 - 大盘趋势: {trend_str}, 波动率: {volatility:.2f}% ({vol_str})")
        
        return market_good
        
    except Exception as e:
        print(f"检查市场环境时出错: {e}")
        # 出错时默认市场环境良好
        return True

def should_use_spread_strategy(ContextInfo, option_type):
    """
    判断是否应该使用价差策略
    """
    try:
        # 获取标的价格和波动率
        undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
        
        # 获取历史波动率
        hist_data = ContextInfo.get_market_data_ex(['close'], [g.undl_code], 
                                                 period='1d', count=20, 
                                                 subscribe=False)
        if g.undl_code in hist_data:
            returns = hist_data[g.undl_code]['close'].pct_change().dropna()
            hist_vol = returns.std() * np.sqrt(252)
        else:
            hist_vol = 0.3  # 默认值
        
        # 在以下情况使用价差策略：
        # 1. 历史波动率高于阈值
        # 2. 市场处于震荡状态
        high_volatility = hist_vol > 0.3  # 30%的年化波动率
        
        # 检查市场是否处于震荡
        index_code = "000001.SH"
        index_data = ContextInfo.get_market_data_ex(['close'], [index_code], 
                                                  period='1d', count=10, 
                                                  subscribe=False)
        
        if index_code in index_data:
            closes = index_data[index_code]['close']
            ma5 = closes.rolling(5).mean().iloc[-1]
            ma10 = closes.rolling(10).mean().iloc[-1]
            
            # 均线交叉或接近表示震荡
            oscillating = abs(ma5/ma10 - 1) < 0.01
        else:
            oscillating = False
        
        return high_volatility or oscillating
        
    except Exception as e:
        print(f"判断是否使用价差策略时出错: {e}")
        return False

def execute_spread_strategy(ContextInfo, option_type, t, buy_level=None, sell_level=None):
    """
    执行期权价差策略
    
    参数:
    ContextInfo: 上下文信息
    option_type: 期权类型，"CALL"或"PUT"
    t: 时间标记
    buy_level: 买入期权的档位，如果为None则自动选择
    sell_level: 卖出期权的档位，如果为None则自动选择
    
    返回:
    bool: 是否成功执行价差策略
    """
    try:
        if option_type == "CALL":
            # 看涨价差策略（牛市价差）
            print("执行看涨价差策略（牛市价差）")
            
            # 获取标的价格
            undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
            g.undl_price_when_open = undl_price
            
            # 获取期权列表
            now = time.strftime("%Y%m%d")
            
            # 改进期权获取，尝试获取更多月份
            call_list = get_current_month_option(ContextInfo, g.undl_code, now, 'CALL')
            print(f"当月认购期权数量: {len(call_list)}")
            
            # 如果当月期权不足，尝试获取次月期权
            if len(call_list) < 4:
                print("当月认购期权数量不足，尝试获取次月期权")
                # 计算次月日期
                current_date = datetime.now()
                next_month = current_date.replace(day=1) + timedelta(days=32)
                next_month_str = next_month.strftime("%Y%m") + "01"  # 次月1号
                
                next_month_calls = get_current_month_option(ContextInfo, g.undl_code, next_month_str, 'CALL')
                call_list.extend(next_month_calls)
                print(f"合并期权列表后总数: {len(call_list)}")
            
            # 筛选合适的期权对
            filtered_calls = []
            for call in call_list:
                try:
                    detail = ContextInfo.get_option_detail_data(call)
                    strike = detail['OptExercisePrice']
                    
                    # 计算到期日（放宽到期日筛选条件）
                    try:
                        days_to_expiry = (datetime.strptime(str(detail['EndDelivDate']), '%Y%m%d') - datetime.now()).days
                    except:
                        print(f"无法计算到期日，使用默认值30: {call}")
                        days_to_expiry = 30
                    
                    # 放宽筛选条件
                    if optimal_days_to_expiry_min <= days_to_expiry <= optimal_days_to_expiry_max:
                        filtered_calls.append((call, strike))
                        print(f"符合筛选条件的认购期权: {call}, 行权价: {strike}, 到期天数: {days_to_expiry}")
                except Exception as e:
                    print(f"筛选期权时出错, 期权: {call}, 错误: {e}")
                    continue
            
            print(f"筛选后的认购期权数量: {len(filtered_calls)}")
            
            # 检查是否有足够的期权
            if len(filtered_calls) < 2:
                print("筛选后的认购期权数量不足，无法执行牛市价差策略")
                return False
            
            # 按行权价排序
            filtered_calls.sort(key=lambda x: x[1])
            
            # 找到平值期权的索引
            atm_index = None
            for i, (call, strike) in enumerate(filtered_calls):
                if strike >= undl_price:  # 找到第一个行权价大于等于标的价格的期权
                    atm_index = i
                    break
            
            # 如果没有找到平值期权索引，使用应对策略
            if atm_index is None:
                if filtered_calls:  # 如果有任何期权
                    # 简单地使用中间的期权作为参考点
                    atm_index = len(filtered_calls) // 2
                    print(f"未找到平值期权，使用中间索引作为替代: {atm_index}")
                else:
                    print("错误：筛选后没有任何期权，无法执行牛市价差策略")
                    return False
            
            print(f"平值期权索引: {atm_index}, 标的价格: {undl_price}, 共有期权: {len(filtered_calls)}个")
            
            # 区分实值和虚值认购期权
            real_value_calls = [(code, strike) for code, strike in filtered_calls if strike < undl_price]
            virtual_value_calls = [(code, strike) for code, strike in filtered_calls if strike > undl_price]
            
            print(f"实值认购期权数量: {len(real_value_calls)}, 虚值认购期权数量: {len(virtual_value_calls)}")
            
            # 确保有买入和卖出期权
            buy_call = None
            sell_call = None
            buy_strike = 0
            sell_strike = 0
            
            if len(real_value_calls) > 0 and len(virtual_value_calls) > 0:
                # 确定买入和卖出的档位
                if buy_level is not None and sell_level is not None:
                    # 使用传入的参数
                    print(f"使用传入的参数选择期权档位 - 买入: {buy_level}档, 卖出: {sell_level}档")
                elif auto_adjust_levels:
                    # 自动选择档位
                    buy_level, sell_level = get_auto_adjusted_levels(ContextInfo, "CALL")
                else:
                    # 使用固定档位
                    buy_level = call_buy_otm_level
                    sell_level = call_sell_otm_level
                
                # 选择实值期权（平值下方N档）买入
                if buy_level <= len(real_value_calls):
                    # 从高到低排序实值期权
                    real_value_calls.sort(key=lambda x: x[1], reverse=True)
                    buy_index = min(buy_level - 1, len(real_value_calls) - 1)
                    buy_call = real_value_calls[buy_index][0]
                    buy_strike = real_value_calls[buy_index][1]
                    print(f"选择实{buy_level}档期权买入: {buy_call}, 行权价: {buy_strike}")
                else:
                    # 如果实值期权不足，使用最接近平值的实值期权
                    real_value_calls.sort(key=lambda x: undl_price - x[1])
                    buy_call = real_value_calls[0][0]
                    buy_strike = real_value_calls[0][1]
                    print(f"实值期权不足，选择最接近平值的实值期权买入: {buy_call}, 行权价: {buy_strike}")
                
                # 选择虚值期权（平值上方N档）卖出
                if sell_level <= len(virtual_value_calls):
                    # 从低到高排序虚值期权
                    virtual_value_calls.sort(key=lambda x: x[1])
                    sell_index = min(sell_level - 1, len(virtual_value_calls) - 1)
                    sell_call = virtual_value_calls[sell_index][0]
                    sell_strike = virtual_value_calls[sell_index][1]
                    print(f"选择虚{sell_level}档期权卖出: {sell_call}, 行权价: {sell_strike}")
                else:
                    # 如果虚值期权不足，使用最接近平值的虚值期权
                    virtual_value_calls.sort(key=lambda x: x[1] - undl_price)
                    sell_call = virtual_value_calls[0][0]
                    sell_strike = virtual_value_calls[0][1]
                    print(f"虚值期权不足，选择最接近平值的虚值期权卖出: {sell_call}, 行权价: {sell_strike}")
                
                print(f"选择的期权 - 买入: {buy_call}(K={buy_strike}), 卖出: {sell_call}(K={sell_strike})")
            # 如果没有足够的实值或虚值期权，直接选择任意两个行权价不同的期权
            elif fallback_to_any_available and len(filtered_calls) >= 2:
                print("没有足够的实值和虚值期权，尝试选择任意两个行权价不同的期权")
                # 按行权价排序
                filtered_calls.sort(key=lambda x: x[1])
                
                # 选择行权价最低和次低的两个期权
                buy_call = filtered_calls[0][0]  # 选择行权价最低的期权买入
                buy_strike = filtered_calls[0][1]
                
                sell_call = filtered_calls[1][0]  # 选择行权价次低的期权卖出
                sell_strike = filtered_calls[1][1]
                
                print(f"选择任意期权 - 买入: {buy_call}(K={buy_strike}), 卖出: {sell_call}(K={sell_strike})")
            else:
                print("无法找到合适的期权对，无法执行牛市价差策略")
                return False
            
            # 如果成功找到了期权对
            if buy_call and sell_call:
                try:
                    # 获取期权价格
                    buy_tick = ContextInfo.get_full_tick([buy_call])
                    sell_tick = ContextInfo.get_full_tick([sell_call])
                    
                    if buy_call not in buy_tick:
                        print(f"错误：无法获取买入期权行情数据 {buy_call}")
                        return False
                        
                    if sell_call not in sell_tick:
                        print(f"错误：无法获取卖出期权行情数据 {sell_call}")
                        return False
                        
                    buy_price = buy_tick[buy_call].get('lastPrice', 0)
                    sell_price = sell_tick[sell_call].get('lastPrice', 0)
                    
                    if buy_price <= 0 or sell_price <= 0:
                        print(f"错误：期权价格异常 - 买入价格: {buy_price}, 卖出价格: {sell_price}")
                        return False
                        
                    print(f"期权价格 - 买入: {buy_price}, 卖出: {sell_price}")
                    
                    # 记录价差信息
                    g.is_spread_strategy = True
                    g.spread_direction = 1  # 牛市价差
                    g.spread_buy_code = buy_call
                    g.spread_sell_code = sell_call
                    g.spread_buy_price = buy_price
                    g.spread_sell_price = sell_price
                    g.spread_initial_value = buy_price - sell_price
                    
                    # 分析价差策略风险收益特征
                    spread_analysis = analyze_spread_strategy(
                        buy_strike, sell_strike, buy_price, sell_price, True, "CALL"
                    )
                    g.spread_max_profit = spread_analysis["max_profit"] if "max_profit" in spread_analysis else 0
                    g.spread_max_loss = spread_analysis["max_loss"] if "max_loss" in spread_analysis else buy_price
                    risk_reward_ratio = spread_analysis.get("risk_reward_ratio", 0)
                    breakeven = spread_analysis.get("breakeven", 0)
                    
                    print(f"牛市价差策略分析 - 风险收益比: {risk_reward_ratio:.2f}, 盈亏平衡点: {breakeven:.2f}")
                    
                    # 执行牛市价差策略
                    g.call_one = buy_call
                    g.buy_long += 1
                    
                    # 买入期权使用开仓指令50
                    print(f"提交买入订单: {buy_call}, 价格: {buy_price}")
                    debugpassorder(50, 1101, account, buy_call, 12, 0, hand, '', 1, '期权策略开多(价差买入)'+g.remark, ContextInfo)
                    
                    # 卖出期权使用开仓指令51
                    print(f"提交卖出订单: {sell_call}, 价格: {sell_price}")
                    debugpassorder(51, 1101, account, sell_call, 12, 0, hand, '', 1, '期权策略开多(价差卖出)'+g.remark, ContextInfo)
                    
                    print(f"执行牛市价差策略 - 买入: {buy_call}(K={buy_strike}), 卖出: {sell_call}(K={sell_strike})")
                    print(f"价差初始值: {g.spread_initial_value:.4f}, 理论最大盈利: {g.spread_max_profit:.4f}, 理论最大亏损: {g.spread_max_loss:.4f}")
                    
                    g.curr_hold = buy_call
                    g.hold = 1
                    g.trace_time_long = time.time()
                    g.hold_price = 0
                    g.open_price = 0
                    g.opened_t.append(t)
                    g.hold_code = buy_call
                    
                    return True
                except Exception as e:
                    print(f"执行价差策略下单时出错: {e}")
                    import traceback
                    traceback.print_exc()
                    return False
            else:
                print(f"无法确定买入和卖出的期权，放弃执行牛市价差策略")
                return False
        elif option_type == "PUT":
            # 看跌价差策略（熊市价差）
            print("执行看跌价差策略（熊市价差）")
            
            # 获取标的价格
            undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
            g.undl_price_when_open = undl_price
            print(f"当前标的价格: {undl_price}")
            
            # 获取期权列表 - 改进期权获取方式
            now = time.strftime("%Y%m%d")
            put_list = get_current_month_option(ContextInfo, g.undl_code, now, 'PUT')
            print(f"获取到看跌期权列表，数量: {len(put_list)}")
            
            # 检查期权列表是否为空
            if not put_list:
                print("警告: 未获取到任何看跌期权，尝试获取所有月份")
                # 尝试获取多个月份的期权
                try:
                    # 获取当前日期的前几个月和后几个月
                    current_date = datetime.now()
                    for i in range(-2, 6):  # 从前2个月到后6个月
                        target_date = current_date + timedelta(days=i*30)
                        target_month_str = target_date.strftime("%Y%m") + "01"
                        month_puts = get_current_month_option(ContextInfo, g.undl_code, target_month_str, 'PUT')
                        put_list.extend(month_puts)
                        print(f"月份 {target_month_str}: 添加 {len(month_puts)} 个看跌期权")
                except Exception as e:
                    print(f"尝试获取多月份期权时出错: {e}")
                
                # 如果仍然没有找到期权，直接返回
                if not put_list:
                    print("错误: 无法获取任何看跌期权，放弃执行熊市价差策略")
                    return False
                    
            # 更宽松地筛选合适的期权对
            filtered_puts = []
            for put in put_list:
                try:
                    detail = ContextInfo.get_option_detail_data(put)
                    strike = detail['OptExercisePrice']
                    
                    # 计算到期日（容错处理）
                    try:
                        days_to_expiry = (datetime.strptime(str(detail['EndDelivDate']), '%Y%m%d') - datetime.now()).days
                    except:
                        print(f"无法计算到期日，使用默认值30: {put}")
                        days_to_expiry = 30
                    
                    # 放宽筛选条件，几乎接受任何期权
                    if days_to_expiry >= 0:  # 只要没有过期就接受
                        filtered_puts.append((put, strike))
                        print(f"符合筛选条件的认沽期权: {put}, 行权价: {strike}, 到期天数: {days_to_expiry}")
                except Exception as e:
                    print(f"筛选期权时出错, 期权: {put}, 错误: {e}")
                    continue
            
            print(f"筛选后的看跌期权数量: {len(filtered_puts)}")
            
            # 按行权价排序
            filtered_puts.sort(key=lambda x: x[1])
            
            # 找到平值期权的索引
            atm_index = None
            for i, (put, strike) in enumerate(filtered_puts):
                if strike >= undl_price:  # 找到第一个行权价大于等于标的价格的期权
                    atm_index = i
                    break
            
            # 如果没有找到平值期权索引，使用应对策略
            if atm_index is None:
                if filtered_puts:  # 如果有任何期权
                    # 简单地使用中间的期权作为参考点
                    atm_index = len(filtered_puts) // 2
                    print(f"未找到平值期权，使用中间索引作为替代: {atm_index}")
                else:
                    print("错误：筛选后没有任何期权，无法执行熊市价差策略")
                    return False
            
            print(f"平值期权索引: {atm_index}, 标的价格: {undl_price}, 共有期权: {len(filtered_puts)}个")
            
            # 直接在这里选择买入和卖出期权
            buy_put = None
            sell_put = None
            buy_strike = 0
            sell_strike = 0
            
            # 尝试按照过滤后的结果选择期权
            if len(filtered_puts) >= 2:
                # 正确区分实值和虚值认沽期权
                real_value_puts = [(code, strike) for code, strike in filtered_puts if strike > undl_price]
                virtual_value_puts = [(code, strike) for code, strike in filtered_puts if strike < undl_price]
                
                print(f"实值认沽期权数量: {len(real_value_puts)}, 虚值认沽期权数量: {len(virtual_value_puts)}")
                
                # 确保有买入和卖出期权
                if len(real_value_puts) > 0 and len(virtual_value_puts) > 0:
                    # 确定买入和卖出的档位
                    if buy_level is not None and sell_level is not None:
                        # 使用传入的参数
                        print(f"使用传入的参数选择期权档位 - 买入: {buy_level}档, 卖出: {sell_level}档")
                    elif auto_adjust_levels:
                        # 自动选择档位
                        buy_level, sell_level = get_auto_adjusted_levels(ContextInfo, "PUT")
                    else:
                        # 使用固定档位
                        buy_level = put_buy_otm_level
                        sell_level = put_sell_otm_level
                    
                    # 选择实值认沽期权（平值上方N档）买入
                    if buy_level <= len(real_value_puts):
                        # 按行权价从低到高排序实值期权
                        real_value_puts.sort(key=lambda x: x[1])
                        buy_index = min(buy_level - 1, len(real_value_puts) - 1)
                        buy_put = real_value_puts[buy_index][0]
                        buy_strike = real_value_puts[buy_index][1]
                        print(f"选择实{buy_level}档认沽期权买入: {buy_put}, 行权价: {buy_strike}")
                    else:
                        # 如果实值期权不足，使用最接近平值的实值期权
                        real_value_puts.sort(key=lambda x: x[1] - undl_price)
                        buy_put = real_value_puts[0][0]
                        buy_strike = real_value_puts[0][1]
                        print(f"实值认沽期权不足，选择最接近平值的实值期权买入: {buy_put}, 行权价: {buy_strike}")
                    
                    # 选择虚值认沽期权（平值下方N档）卖出
                    if sell_level <= len(virtual_value_puts):
                        # 按行权价从高到低排序虚值期权
                        virtual_value_puts.sort(key=lambda x: x[1], reverse=True)
                        sell_index = min(sell_level - 1, len(virtual_value_puts) - 1)
                        sell_put = virtual_value_puts[sell_index][0]
                        sell_strike = virtual_value_puts[sell_index][1]
                        print(f"选择虚{sell_level}档认沽期权卖出: {sell_put}, 行权价: {sell_strike}")
                    else:
                        # 如果虚值期权不足，使用最接近平值的虚值期权
                        virtual_value_puts.sort(key=lambda x: undl_price - x[1])
                        sell_put = virtual_value_puts[0][0]
                        sell_strike = virtual_value_puts[0][1]
                        print(f"虚值认沽期权不足，选择最接近平值的虚值期权卖出: {sell_put}, 行权价: {sell_strike}")
                    
                    print(f"选择的期权 - 买入: {buy_put}(K={buy_strike}), 卖出: {sell_put}(K={sell_strike})")
                # 如果没有足够的实值或虚值期权，直接选择任意两个行权价不同的期权
                elif fallback_to_any_available and len(filtered_puts) >= 2:
                    print("没有足够的实值和虚值期权，尝试选择任意两个行权价不同的期权")
                    # 按行权价排序
                    filtered_puts.sort(key=lambda x: x[1])
                    
                    # 选择行权价最低和最高的两个期权
                    buy_put = filtered_puts[-1][0]  # 选择行权价最高的期权买入
                    buy_strike = filtered_puts[-1][1]
                    
                    sell_put = filtered_puts[0][0]  # 选择行权价最低的期权卖出
                    sell_strike = filtered_puts[0][1]
                    
                    print(f"选择任意期权 - 买入: {buy_put}(K={buy_strike}), 卖出: {sell_put}(K={sell_strike})")
                else:
                    print("无法找到合适的期权对，无法执行熊市价差策略")
                    return False
                
                # 如果成功找到了期权对
                if buy_put and sell_put:
                    try:
                        # 获取期权价格
                        buy_tick = ContextInfo.get_full_tick([buy_put])
                        sell_tick = ContextInfo.get_full_tick([sell_put])
                        
                        if buy_put not in buy_tick:
                            print(f"错误：无法获取买入期权行情数据 {buy_put}")
                            return False
                            
                        if sell_put not in sell_tick:
                            print(f"错误：无法获取卖出期权行情数据 {sell_put}")
                            return False
                            
                        buy_price = buy_tick[buy_put].get('lastPrice', 0)
                        sell_price = sell_tick[sell_put].get('lastPrice', 0)
                        
                        if buy_price <= 0 or sell_price <= 0:
                            print(f"错误：期权价格异常 - 买入价格: {buy_price}, 卖出价格: {sell_price}")
                            return False
                            
                        print(f"期权价格 - 买入: {buy_price}, 卖出: {sell_price}")
                        
                        # 记录价差信息
                        g.is_spread_strategy = True
                        g.spread_direction = -1  # 熊市价差
                        g.spread_buy_code = buy_put
                        g.spread_sell_code = sell_put
                        g.spread_buy_price = buy_price
                        g.spread_sell_price = sell_price
                        g.spread_initial_value = buy_price - sell_price
                        
                        # 分析价差策略风险收益特征
                        spread_analysis = analyze_spread_strategy(
                            buy_strike, sell_strike, buy_price, sell_price, False, "PUT"
                        )
                        g.spread_max_profit = spread_analysis["max_profit"] if "max_profit" in spread_analysis else 0
                        g.spread_max_loss = spread_analysis["max_loss"] if "max_loss" in spread_analysis else buy_price
                        risk_reward_ratio = spread_analysis.get("risk_reward_ratio", 0)
                        breakeven = spread_analysis.get("breakeven", 0)
                        
                        print(f"熊市价差策略分析 - 风险收益比: {risk_reward_ratio:.2f}, 盈亏平衡点: {breakeven:.2f}")
                        
                        # 执行熊市价差策略
                        g.put_one = buy_put
                        g.buy_short += 1
                        
                        # 买入期权使用开仓指令50
                        print(f"提交买入订单: {buy_put}, 价格: {buy_price}")
                        debugpassorder(50, 1101, account, buy_put, 12, 0, hand, '', 1, '期权策略开空(价差买入)'+g.remark, ContextInfo)
                        
                        # 卖出期权使用开仓指令51
                        print(f"提交卖出订单: {sell_put}, 价格: {sell_price}")
                        debugpassorder(51, 1101, account, sell_put, 12, 0, hand, '', 1, '期权策略开空(价差卖出)'+g.remark, ContextInfo)
                        
                        print(f"执行熊市价差策略 - 买入: {buy_put}(K={buy_strike}), 卖出: {sell_put}(K={sell_strike})")
                        print(f"价差初始值: {g.spread_initial_value:.4f}, 理论最大盈利: {g.spread_max_profit:.4f}, 理论最大亏损: {g.spread_max_loss:.4f}")
                        
                        g.curr_hold = buy_put
                        g.hold = -1
                        g.trace_time_short = time.time()
                        g.hold_price = 9999999
                        g.open_price = 0
                        g.opened_t.append(t)
                        g.hold_code = buy_put
                        
                        return True
                    except Exception as e:
                        print(f"执行价差策略下单时出错: {e}")
                        import traceback
                        traceback.print_exc()
                        return False
            else:
                print(f"筛选后的期权数量不足: {len(filtered_puts)}个，需要至少2个期权才能执行价差策略")
                return False
        
        # 如果执行到这里，说明未能成功执行价差策略
        print("未能成功执行价差策略")
        return False
        
    except Exception as e:
        print(f"执行价差策略时出错: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 打印当前策略执行状态
        print(f"价差策略执行结果 - 成功: {g.is_spread_strategy}, 方向: {'熊市价差' if g.spread_direction == -1 else '牛市价差' if g.spread_direction == 1 else '未知'}")
        if g.is_spread_strategy:
            print(f"价差策略持仓 - 买入: {g.spread_buy_code}, 卖出: {g.spread_sell_code}")

def calculate_spread_value(ContextInfo):
    """
    计算价差策略的当前价值和盈亏比例
    
    返回:
    tuple: (当前价差值, 盈亏比例, 卖出期权盈亏比例, 标的价格变动幅度)
    """
    try:
        if not g.is_spread_strategy:
            return 0, 0, 0, 0
            
        # 获取当前期权价格
        buy_tick = ContextInfo.get_full_tick([g.spread_buy_code])[g.spread_buy_code]
        sell_tick = ContextInfo.get_full_tick([g.spread_sell_code])[g.spread_sell_code]
        current_buy_price = buy_tick['lastPrice']
        current_sell_price = sell_tick['lastPrice']
        
        # 计算当前价差值
        current_spread_value = current_buy_price - current_sell_price
        
        # 计算价差盈亏比例
        if g.spread_initial_value != 0:
            spread_pnl_ratio = (current_spread_value - g.spread_initial_value) / abs(g.spread_initial_value) * 100
        else:
            spread_pnl_ratio = 0
            
        # 计算卖出期权的盈亏比例（对于卖出期权，价格下降是盈利）
        if g.spread_sell_price != 0:
            sell_option_pnl_ratio = (g.spread_sell_price - current_sell_price) / g.spread_sell_price * 100
        else:
            sell_option_pnl_ratio = 0
            
        # 计算标的价格变动幅度
        undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
        if g.undl_price_when_open != 0:
            undl_price_change_ratio = (undl_price - g.undl_price_when_open) / g.undl_price_when_open * 100
        else:
            undl_price_change_ratio = 0
            
        return current_spread_value, spread_pnl_ratio, sell_option_pnl_ratio, undl_price_change_ratio
        
    except Exception as e:
        print(f"计算价差价值时出错: {e}")
        return 0, 0, 0, 0

def select_optimal_options(ContextInfo, filtered_options, atm_index, undl_price, option_type, is_bull_spread=True):
    """
    根据隐含波动率和Delta值选择最优期权合约
    
    参数:
    ContextInfo: 上下文信息
    filtered_options: 筛选后的期权列表，格式为[(code, strike), ...]
    atm_index: 平值期权索引
    undl_price: 标的价格
    option_type: 期权类型，"CALL"或"PUT"
    is_bull_spread: 是否为牛市价差策略
    
    返回:
    tuple: (买入期权代码, 买入行权价, 卖出期权代码, 卖出行权价)
    """
    try:
        # 确定选择范围
        if option_type == "CALL" and is_bull_spread:
            # 牛市价差：买入实值认购，卖出虚值认购
            # 对于认购期权，行权价<标的价格为实值，行权价>标的价格为虚值
            real_value_calls = [(code, strike) for code, strike in filtered_options if strike < undl_price]
            virtual_value_calls = [(code, strike) for code, strike in filtered_options if strike > undl_price]
            
            print(f"牛市价差 - 买入范围（实值）: {len(real_value_calls)}个期权")
            for i, (code, strike) in enumerate(real_value_calls):
                print(f"  买入候选[{i}]: {code}, 行权价: {strike}")
            
            print(f"牛市价差 - 卖出范围（虚值）: {len(virtual_value_calls)}个期权")
            for i, (code, strike) in enumerate(virtual_value_calls):
                print(f"  卖出候选[{i}]: {code}, 行权价: {strike}")
                
        elif option_type == "PUT" and not is_bull_spread:
            # 熊市价差：买入实值认沽，卖出虚值认沽
            # 对于认沽期权，行权价>标的价格为实值，行权价<标的价格为虚值
            real_value_puts = [(code, strike) for code, strike in filtered_options if strike > undl_price]
            virtual_value_puts = [(code, strike) for code, strike in filtered_options if strike < undl_price]
            
            print(f"熊市价差 - 买入范围（实值）: {len(real_value_puts)}个期权")
            for i, (code, strike) in enumerate(real_value_puts):
                print(f"  买入候选[{i}]: {code}, 行权价: {strike}, {'实值' if strike > undl_price else '虚值'}")
            
            print(f"熊市价差 - 卖出范围（虚值）: {len(virtual_value_puts)}个期权")
            for i, (code, strike) in enumerate(virtual_value_puts):
                print(f"  卖出候选[{i}]: {code}, 行权价: {strike}, {'实值' if strike > undl_price else '虚值'}")
        else:
            # 不支持的组合
            return None, 0, None, 0
            
        # 如果没有足够的期权可选
        if (option_type == "CALL" and (not real_value_calls or not virtual_value_calls)) or \
           (option_type == "PUT" and (not real_value_puts or not virtual_value_puts)):
            return None, 0, None, 0
        
        # 默认无风险利率
        risk_free_rate = 0.025  # 2.5%
        
        # 为每个期权计算评分
        buy_options = []
        sell_options = []
        
        if option_type == "CALL" and is_bull_spread:
            buy_candidates = real_value_calls
            sell_candidates = virtual_value_calls
        elif option_type == "PUT" and not is_bull_spread:
            buy_candidates = real_value_puts
            sell_candidates = virtual_value_puts
        
        # 计算买入期权的评分
        for code, strike in buy_candidates:
            try:
                # 获取期权详情和市场数据
                detail = ContextInfo.get_option_detail_data(code)
                tick = ContextInfo.get_full_tick([code])[code]
                
                # 提取关键信息
                iv = detail.get('ImpliedVol', 0.3)  # 默认IV为30%
                days_to_expiry = (datetime.strptime(str(detail['EndDelivDate']), '%Y%m%d') - datetime.now()).days
                volume = tick.get('volume', 0)
                bid_ask_spread = tick.get('askPrice1', 0) - tick.get('bidPrice1', 0)
                
                # 标准化到期日（越接近30天越好）
                t_score = 1 - abs(days_to_expiry - 30) / 30 if days_to_expiry <= 60 else 0
                
                # 计算希腊字母
                if option_type == "CALL":
                    greeks = calculate_option_greeks(undl_price, strike, days_to_expiry/365, risk_free_rate, iv, "call")
                else:
                    greeks = calculate_option_greeks(undl_price, strike, days_to_expiry/365, risk_free_rate, iv, "put")
                
                delta = abs(greeks['delta'])
                gamma = greeks['gamma']
                theta = -greeks['theta']  # 负theta是好的
                
                # 计算综合评分
                delta_score = delta if option_type == "CALL" else (1 - delta)  # 买入认购期权希望delta高，买入认沽期权希望delta接近0.5
                iv_score = 1 - min(iv, 1)  # 低IV更好
                liquidity_score = min(volume / 1000, 1)  # 成交量越大越好，最高1分
                
                # 实值程度评分（越接近平值越好）
                if option_type == "CALL":
                    moneyness = undl_price / strike - 1  # 实值比例
                else:
                    moneyness = strike / undl_price - 1  # 实值比例
                moneyness_score = 1 - min(abs(moneyness) / 0.05, 1)  # 实值比例越接近0越好，超过5%则为0分
                
                # 综合评分
                total_score = (
                    delta_score * 0.3 +         # Delta
                    iv_score * 0.2 +            # 隐含波动率
                    liquidity_score * 0.15 +    # 流动性
                    t_score * 0.1 +             # 到期日
                    moneyness_score * 0.25      # 实值程度
                )
                
                buy_options.append((code, strike, total_score, iv, delta, days_to_expiry))
                print(f"买入候选: {code}, 行权价: {strike}, 评分: {total_score:.2f}, IV: {iv:.2f}, Delta: {delta:.2f}")
                
            except Exception as e:
                print(f"计算买入期权评分时出错: {e}")
        
        # 计算卖出期权的评分
        for code, strike in sell_candidates:
            try:
                # 获取期权详情和市场数据
                detail = ContextInfo.get_option_detail_data(code)
                tick = ContextInfo.get_full_tick([code])[code]
                
                # 提取关键信息
                iv = detail.get('ImpliedVol', 0.3)  # 默认IV为30%
                days_to_expiry = (datetime.strptime(str(detail['EndDelivDate']), '%Y%m%d') - datetime.now()).days
                volume = tick.get('volume', 0)
                
                # 标准化到期日（越接近30天越好）
                t_score = 1 - abs(days_to_expiry - 30) / 30 if days_to_expiry <= 60 else 0
                
                # 计算希腊字母
                if option_type == "CALL":
                    greeks = calculate_option_greeks(undl_price, strike, days_to_expiry/365, risk_free_rate, iv, "call")
                else:
                    greeks = calculate_option_greeks(undl_price, strike, days_to_expiry/365, risk_free_rate, iv, "put")
                
                delta = abs(greeks['delta'])
                gamma = greeks['gamma']
                theta = -greeks['theta']  # 负theta是好的
                
                # 计算综合评分
                delta_score = 1 - delta  # 卖出期权希望delta低
                iv_score = min(iv, 1)  # 高IV更好
                liquidity_score = min(volume / 1000, 1)  # 成交量越大越好，最高1分
                
                # 虚值程度评分（越接近平值越好，但不要太接近）
                if option_type == "CALL":
                    moneyness = strike / undl_price - 1  # 虚值比例
                else:
                    moneyness = undl_price / strike - 1  # 虚值比例
                moneyness_score = 1 - min(abs(moneyness - 0.03) / 0.05, 1)  # 虚值比例越接近3%越好
                
                # 综合评分
                total_score = (
                    delta_score * 0.3 +         # Delta
                    iv_score * 0.2 +            # 隐含波动率
                    liquidity_score * 0.15 +    # 流动性
                    t_score * 0.1 +             # 到期日
                    moneyness_score * 0.25      # 虚值程度
                )
                
                sell_options.append((code, strike, total_score, iv, delta, days_to_expiry))
                print(f"卖出候选: {code}, 行权价: {strike}, 评分: {total_score:.2f}, IV: {iv:.2f}, Delta: {delta:.2f}")
                
            except Exception as e:
                print(f"计算卖出期权评分时出错: {e}")
        
        # 选择评分最高的期权
        if buy_options and sell_options:
            # 按评分排序
            buy_options.sort(key=lambda x: x[2], reverse=True)
            sell_options.sort(key=lambda x: x[2], reverse=True)
            
            # 选择最佳期权
            buy_code = buy_options[0][0]
            buy_strike = buy_options[0][1]
            sell_code = sell_options[0][0]
            sell_strike = sell_options[0][1]
            
            return buy_code, buy_strike, sell_code, sell_strike
        else:
            return None, 0, None, 0
            
    except Exception as e:
        print(f"选择最优期权时出错: {e}")
        return None, 0, None, 0

def calculate_option_greeks(S, K, T, r, sigma, option_type="call"):
    """
    使用Black-Scholes模型计算期权的理论价值和希腊字母
    
    参数:
    S: 标的价格
    K: 行权价
    T: 到期时间（年）
    r: 无风险利率
    sigma: 波动率
    option_type: 期权类型，"call"或"put"
    
    返回:
    dict: 包含期权价格和希腊字母的字典
    """
    import math
    
    try:
        # 防止除零错误
        if T <= 0:
            T = 0.01
        
        # 计算d1和d2
        d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))
        d2 = d1 - sigma * math.sqrt(T)
        
        # 计算期权价格
        if option_type.lower() == "call":
            price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
            delta = norm.cdf(d1)
            gamma = norm.pdf(d1) / (S * sigma * math.sqrt(T))
            theta = -(S * sigma * norm.pdf(d1)) / (2 * math.sqrt(T)) - r * K * math.exp(-r * T) * norm.cdf(d2)
            vega = S * math.sqrt(T) * norm.pdf(d1)
            rho = K * T * math.exp(-r * T) * norm.cdf(d2)
        else:  # put
            price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
            delta = norm.cdf(d1) - 1
            gamma = norm.pdf(d1) / (S * sigma * math.sqrt(T))
            theta = -(S * sigma * norm.pdf(d1)) / (2 * math.sqrt(T)) + r * K * math.exp(-r * T) * norm.cdf(-d2)
            vega = S * math.sqrt(T) * norm.pdf(d1)
            rho = -K * T * math.exp(-r * T) * norm.cdf(-d2)
        
        return {
            "price": price,
            "delta": delta,
            "gamma": gamma,
            "theta": theta / 365,  # 转换为每天
            "vega": vega / 100,    # 转换为每个波动率点
            "rho": rho / 100       # 转换为每个利率点
        }
    except Exception as e:
        print(f"计算期权希腊字母出错: {e}")
        return {
            "price": 0,
            "delta": 0,
            "gamma": 0,
            "theta": 0,
            "vega": 0,
            "rho": 0
        }

def analyze_spread_strategy(buy_strike, sell_strike, buy_price, sell_price, is_bull_spread=True, option_type="CALL"):
    """
    分析价差策略的风险收益特征
    
    参数:
    buy_strike: 买入期权的行权价
    sell_strike: 卖出期权的行权价
    buy_price: 买入期权的价格
    sell_price: 卖出期权的价格
    is_bull_spread: 是否为牛市价差
    option_type: 期权类型，"CALL"或"PUT"
    
    返回:
    dict: 包含价差策略分析结果的字典
    """
    try:
        # 计算初始价差
        initial_spread = buy_price - sell_price
        
        # 根据策略类型计算最大盈亏
        if option_type == "CALL" and is_bull_spread:
            # 牛市认购价差：买入低行权价，卖出高行权价
            max_profit = sell_strike - buy_strike - initial_spread
            max_loss = initial_spread
            breakeven = buy_strike + initial_spread
        elif option_type == "PUT" and not is_bull_spread:
            # 熊市认沽价差：买入高行权价，卖出低行权价
            max_profit = buy_strike - sell_strike - initial_spread
            max_loss = initial_spread
            breakeven = buy_strike - initial_spread
        else:
            # 不支持的组合
            return {
                "max_profit": 0,
                "max_loss": 0,
                "risk_reward_ratio": 0,
                "breakeven": 0
            }
        
        # 计算风险收益比
        risk_reward_ratio = max_profit / max_loss if max_loss > 0 else 0
        
        return {
            "max_profit": max_profit,
            "max_loss": max_loss,
            "risk_reward_ratio": risk_reward_ratio,
            "breakeven": breakeven
        }
    except Exception as e:
        print(f"分析价差策略出错: {e}")
        return {
            "max_profit": 0,
            "max_loss": 0,
            "risk_reward_ratio": 0,
            "breakeven": 0
        }

# 添加一个调试用的订单函数
def debugpassorder(order_type, market, acct, code, price_type, price, volume, stop_price, order_flag, note, context):
    """
    订单发送前检查并记录详细信息的函数
    
    参数:
    order_type: 订单类型，50=买入开仓，51=卖出平仓等
    market: 市场代码
    acct: 账户
    code: 合约代码
    price_type: 价格类型
    price: 价格
    volume: 数量
    stop_price: 止损价
    order_flag: 订单标志
    note: 备注
    context: 上下文
    
    返回:
    订单ID
    """
    try:
        # 打印完整订单信息
        order_type_str = "买入开仓" if order_type == 50 else "卖出开仓" if order_type == 51 else "买入平仓" if order_type == 52 else "卖出平仓" if order_type == 53 else f"其他({order_type})"
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"[{current_time}] ===== 准备发送订单 =====")
        print(f"订单类型: {order_type_str} (编码:{order_type})")
        print(f"合约: {code}")
        print(f"数量: {volume}")
        print(f"账户: {acct}")
        print(f"价格类型: {price_type}")
        print(f"价格: {price}")
        print(f"备注: {note}")
        
        # 检查合约是否有效
        try:
            tick = context.get_full_tick([code])
            if code in tick:
                print(f"合约检查: 有效, 最新价: {tick[code].get('lastPrice', '未知')}")
            else:
                print(f"警告: 合约 {code} 无法获取行情")
        except Exception as e:
            print(f"警告: 检查合约时出错 - {e}")
        
        # 检查账户是否有效
        try:
            account_info = context.get_trade_detail_data(acct, "STOCK", "STOCK_OPTION")
            print(f"账户检查: {'有效' if account_info is not None else '无效/无数据'}")
        except Exception as e:
            print(f"警告: 检查账户时出错 - {e}")
        
        # 检查是否有足够的等待时间
        current_ts = time.time()
        if current_ts - g.last_order_time < 1:  # 至少等待1秒
            print(f"警告: 订单发送过于频繁，最后一个订单发送于 {current_ts - g.last_order_time:.2f} 秒前")
        g.last_order_time = current_ts
        
        # 增加订单计数
        g.order_count += 1
        print(f"当前会话订单计数: {g.order_count}")
        
        # 发送订单
        print(f"正在发送订单...")
        order_id = passorder(order_type, market, acct, code, price_type, price, volume, stop_price, order_flag, note, context)
        print(f"订单已发送，订单ID: {order_id}")
        print(f"===== 订单发送完成 =====\n")
        return order_id
    except Exception as e:
        print(f"订单发送前检查失败: {e}")
        # 仍然尝试发送订单
        try:
            return passorder(order_type, market, acct, code, price_type, price, volume, stop_price, order_flag, note, context)
        except Exception as e2:
            print(f"订单发送失败: {e2}")
            return None

def select_best_strategy(ContextInfo):
    """
    根据市场条件自动选择最佳策略
    返回: 
    - 策略类型: "single_leg" 或 "spread"
    - 理由: 选择该策略的原因
    """
    if not auto_strategy_selection:
        # 如果未启用自动选择，根据配置参数决定
        if use_multi_leg_strategy:
            return "spread", "已在配置中设置使用价差策略"
        else:
            return "single_leg", "已在配置中设置使用单腿策略"
    
    try:
        # 1. 获取市场数据
        # 获取大盘指数
        index_code = "000001.SH"
        index_data = ContextInfo.get_market_data_ex(['close', 'high', 'low'], [index_code], 
                                                  period='1d', count=market_trend_days+1, 
                                                  subscribe=False)
        
        if index_code not in index_data:
            return "single_leg", "无法获取市场数据，默认使用单腿策略"
        
        # 计算波动率 (简易VIX)
        closes = index_data[index_code]['close']
        returns = closes.pct_change().dropna()
        volatility = returns.std() * np.sqrt(252) * 100  # 年化波动率
        
        # 计算趋势强度
        highs = index_data[index_code]['high']
        lows = index_data[index_code]['low']
        
        # 使用ADX指标思想计算趋势强度
        tr = np.maximum(highs.diff().abs(), 
                     np.maximum((highs - lows.shift()).abs(), 
                               (lows - closes.shift()).abs()))
        dx = ((highs.diff(1) - lows.diff(1)).abs() / tr).rolling(14).mean() * 100
        trend_strength = dx.iloc[-1] / 100  # 标准化到0-1之间
        
        # 获取标的期权隐含波动率平均值
        iv_avg = get_average_implied_volatility(ContextInfo, g.undl_code)
        
        # 2. 决策逻辑
        reasons = []
        
        # 高波动率环境倾向于使用价差策略
        if volatility > vix_high_threshold or iv_avg > 0.4:  # 40%隐含波动率
            strategy_score = 0.7  # 70%可能性使用价差策略
            reasons.append(f"高波动率环境 (VIX: {volatility:.1f}%, IV: {iv_avg:.1%})")
        elif volatility < vix_low_threshold and iv_avg < 0.2:  # 20%隐含波动率
            strategy_score = 0.3  # 30%可能性使用价差策略
            reasons.append(f"低波动率环境 (VIX: {volatility:.1f}%, IV: {iv_avg:.1%})")
        else:
            strategy_score = 0.5  # 中等波动率
            reasons.append(f"中等波动率环境 (VIX: {volatility:.1f}%, IV: {iv_avg:.1%})")
        
        # 强趋势环境倾向于使用单腿策略
        if trend_strength > market_trend_strength_threshold:
            strategy_score -= 0.2  # 降低使用价差策略的可能性
            reasons.append(f"强趋势市场 (趋势强度: {trend_strength:.2f})")
        else:
            strategy_score += 0.1  # 增加使用价差策略的可能性
            reasons.append(f"震荡市场 (趋势强度: {trend_strength:.2f})")
        
        # 加入随机因素防止过度拟合
        strategy_score += np.random.normal(0, 0.05)  # 加入少量随机性
        
        # 最终决策
        if strategy_score >= 0.5:
            return "spread", "、".join(reasons)
        else:
            return "single_leg", "、".join(reasons)
    
    except Exception as e:
        print(f"选择策略时出错: {e}")
        return "single_leg", f"出错，默认使用单腿策略: {e}"

def get_average_implied_volatility(ContextInfo, undl_code):
    """获取标的期权的平均隐含波动率"""
    try:
        now = time.strftime("%Y%m%d")
        call_list = get_current_month_option(ContextInfo, undl_code, now, 'CALL')
        put_list = get_current_month_option(ContextInfo, undl_code, now, 'PUT')
        
        all_ivs = []
        
        # 收集所有期权的隐含波动率
        for opt in call_list + put_list:
            try:
                detail = ContextInfo.get_option_detail_data(opt)
                iv = detail.get('ImpliedVol', None)
                if iv is not None and 0.05 < iv < 2.0:  # 过滤异常值
                    all_ivs.append(iv)
            except:
                continue
        
        # 计算平均值
        if all_ivs:
            return np.mean(all_ivs)
        else:
            return 0.3  # 默认30%
    except Exception as e:
        print(f"计算平均隐含波动率时出错: {e}")
        return 0.3  # 默认30%

# 在analyze_spread_strategy函数后面添加一个新函数

def get_auto_adjusted_levels(ContextInfo, option_type="CALL"):
    """
    根据市场环境自动确定期权选择的档位
    
    参数:
    ContextInfo: 上下文信息
    option_type: 期权类型，"CALL"或"PUT"
    
    返回:
    tuple: (买入档位, 卖出档位)
    """
    try:
        # 获取标的价格和波动率
        undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
        
        # 获取标的期权的平均隐含波动率
        iv_avg = get_average_implied_volatility(ContextInfo, g.undl_code)
        
        # 获取历史波动率
        hist_data = ContextInfo.get_market_data_ex(['close'], [g.undl_code], 
                                                 period='1d', count=20, 
                                                 subscribe=False)
        if g.undl_code in hist_data:
            returns = hist_data[g.undl_code]['close'].pct_change().dropna()
            hist_vol = returns.std() * np.sqrt(252)
        else:
            hist_vol = iv_avg  # 如果无法获取历史数据，使用隐含波动率
        
        # 根据波动率调整档位
        # 波动率越高，买入期权距离平值越远，卖出期权距离平值也相应调整
        if option_type == "CALL":
            # 牛市价差策略：买入实值认购、卖出虚值认购
            if hist_vol > 0.4:  # 高波动环境
                buy_level = 3  # 实值3档
                sell_level = 1  # 虚值1档
                print(f"高波动环境 (HV: {hist_vol:.2%}, IV: {iv_avg:.2%}) - 牛市价差设置为买入实3档，卖出虚1档")
            elif 0.2 <= hist_vol <= 0.4:  # 中等波动环境
                buy_level = 2  # 实值2档
                sell_level = 1  # 虚值1档
                print(f"中等波动环境 (HV: {hist_vol:.2%}, IV: {iv_avg:.2%}) - 牛市价差设置为买入实2档，卖出虚1档")
            else:  # 低波动环境
                buy_level = 1  # 实值1档
                sell_level = 1  # 虚值1档
                print(f"低波动环境 (HV: {hist_vol:.2%}, IV: {iv_avg:.2%}) - 牛市价差设置为买入实1档，卖出虚1档")
        else:  # PUT
            # 熊市价差策略：买入实值认沽、卖出虚值认沽
            if hist_vol > 0.4:  # 高波动环境
                buy_level = 3  # 实值3档
                sell_level = 1  # 虚值1档
                print(f"高波动环境 (HV: {hist_vol:.2%}, IV: {iv_avg:.2%}) - 熊市价差设置为买入实3档，卖出虚1档")
            elif 0.2 <= hist_vol <= 0.4:  # 中等波动环境
                buy_level = 2  # 实值2档
                sell_level = 1  # 虚值1档
                print(f"中等波动环境 (HV: {hist_vol:.2%}, IV: {iv_avg:.2%}) - 熊市价差设置为买入实2档，卖出虚1档")
            else:  # 低波动环境
                buy_level = 1  # 实值1档
                sell_level = 1  # 虚值1档
                print(f"低波动环境 (HV: {hist_vol:.2%}, IV: {iv_avg:.2%}) - 熊市价差设置为买入实1档，卖出虚1档")
        
        # 根据期权链长度调整档位
        # 确保档位不会超过可用期权数量
        if option_type == "CALL":
            call_list = get_current_month_option(ContextInfo, g.undl_code, time.strftime("%Y%m%d"), 'CALL')
            # 计算实值期权和虚值期权的数量
            real_value_count = sum(1 for call in call_list if ContextInfo.get_option_detail_data(call)['OptExercisePrice'] < undl_price)
            virtual_value_count = len(call_list) - real_value_count
            
            # 确保档位不超过可用期权数量
            buy_level = min(buy_level, real_value_count)
            sell_level = min(sell_level, virtual_value_count)
            
            print(f"期权数量限制 - 实值认购: {real_value_count}个, 虚值认购: {virtual_value_count}个")
            print(f"最终档位选择 - 买入实值认购: {buy_level}档, 卖出虚值认购: {sell_level}档")
        else:  # PUT
            put_list = get_current_month_option(ContextInfo, g.undl_code, time.strftime("%Y%m%d"), 'PUT')
            # 计算实值期权和虚值期权的数量
            real_value_count = sum(1 for put in put_list if ContextInfo.get_option_detail_data(put)['OptExercisePrice'] > undl_price)
            virtual_value_count = len(put_list) - real_value_count
            
            # 确保档位不超过可用期权数量
            buy_level = min(buy_level, real_value_count)
            sell_level = min(sell_level, virtual_value_count)
            
            print(f"期权数量限制 - 实值认沽: {real_value_count}个, 虚值认沽: {virtual_value_count}个")
            print(f"最终档位选择 - 买入实值认沽: {buy_level}档, 卖出虚值认沽: {sell_level}档")
        
        # 确保档位至少为1
        buy_level = max(1, buy_level)
        sell_level = max(1, sell_level)
        
        return buy_level, sell_level
    
    except Exception as e:
        print(f"自动计算档位时出错: {e}")
        # 如果出错，返回默认档位
        if option_type == "CALL":
            return call_buy_otm_level, call_sell_otm_level
        else:
            return put_buy_otm_level, put_sell_otm_level

def select_options_by_levels(ContextInfo, option_type, buy_level, sell_level):
    """
    根据指定的档位选择期权合约
    
    参数:
    ContextInfo: 上下文信息
    option_type: 期权类型，"CALL"或"PUT"
    buy_level: 买入期权的档位
    sell_level: 卖出期权的档位
    
    返回:
    tuple: (买入期权代码, 买入行权价, 卖出期权代码, 卖出行权价)
    """
    try:
        # 获取标的价格
        undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
        
        # 获取期权列表
        now = time.strftime("%Y%m%d")
        option_list = get_current_month_option(ContextInfo, g.undl_code, now, option_type)
        
        if not option_list:
            print(f"未找到{option_type}期权，无法按档位选择")
            return None, 0, None, 0
        
        # 根据期权类型确定实值和虚值的判断标准
        if option_type == "CALL":
            # 认购期权：行权价<标的价格为实值，行权价>标的价格为虚值
            real_value_options = []
            virtual_value_options = []
            
            for code in option_list:
                try:
                    detail = ContextInfo.get_option_detail_data(code)
                    strike = detail['OptExercisePrice']
                    
                    if strike < undl_price:
                        real_value_options.append((code, strike))
                    else:
                        virtual_value_options.append((code, strike))
                except Exception as e:
                    print(f"处理期权{code}时出错: {e}")
                    continue
            
            # 排序实值期权和虚值期权
            real_value_options.sort(key=lambda x: x[1], reverse=True)  # 从高到低排序实值期权
            virtual_value_options.sort(key=lambda x: x[1])  # 从低到高排序虚值期权
            
            print(f"认购期权: 共找到{len(real_value_options)}个实值期权，{len(virtual_value_options)}个虚值期权")
            
            # 选择实值期权
            if buy_level <= len(real_value_options):
                buy_index = buy_level - 1
                buy_code = real_value_options[buy_index][0]
                buy_strike = real_value_options[buy_index][1]
                print(f"选择实{buy_level}档认购期权买入: {buy_code}, 行权价: {buy_strike}")
            else:
                # 如果实值期权不足，使用最接近平值的实值期权
                if real_value_options:
                    real_value_options.sort(key=lambda x: undl_price - x[1])
                    buy_code = real_value_options[0][0]
                    buy_strike = real_value_options[0][1]
                    print(f"实值认购期权不足，选择最接近平值的实值期权买入: {buy_code}, 行权价: {buy_strike}")
                else:
                    print("没有实值认购期权可用")
                    return None, 0, None, 0
            
            # 选择虚值期权
            if sell_level <= len(virtual_value_options):
                sell_index = sell_level - 1
                sell_code = virtual_value_options[sell_index][0]
                sell_strike = virtual_value_options[sell_index][1]
                print(f"选择虚{sell_level}档认购期权卖出: {sell_code}, 行权价: {sell_strike}")
            else:
                # 如果虚值期权不足，使用最接近平值的虚值期权
                if virtual_value_options:
                    virtual_value_options.sort(key=lambda x: x[1] - undl_price)
                    sell_code = virtual_value_options[0][0]
                    sell_strike = virtual_value_options[0][1]
                    print(f"虚值认购期权不足，选择最接近平值的虚值期权卖出: {sell_code}, 行权价: {sell_strike}")
                else:
                    print("没有虚值认购期权可用")
                    return None, 0, None, 0
                
        else:  # PUT
            # 认沽期权：行权价>标的价格为实值，行权价<标的价格为虚值
            real_value_options = []
            virtual_value_options = []
            
            for code in option_list:
                try:
                    detail = ContextInfo.get_option_detail_data(code)
                    strike = detail['OptExercisePrice']
                    
                    if strike > undl_price:
                        real_value_options.append((code, strike))
                    else:
                        virtual_value_options.append((code, strike))
                except Exception as e:
                    print(f"处理期权{code}时出错: {e}")
                    continue
            
            # 排序实值期权和虚值期权
            real_value_options.sort(key=lambda x: x[1])  # 从低到高排序实值期权
            virtual_value_options.sort(key=lambda x: x[1], reverse=True)  # 从高到低排序虚值期权
            
            print(f"认沽期权: 共找到{len(real_value_options)}个实值期权，{len(virtual_value_options)}个虚值期权")
            
            # 选择实值期权
            if buy_level <= len(real_value_options):
                buy_index = buy_level - 1
                buy_code = real_value_options[buy_index][0]
                buy_strike = real_value_options[buy_index][1]
                print(f"选择实{buy_level}档认沽期权买入: {buy_code}, 行权价: {buy_strike}")
            else:
                # 如果实值期权不足，使用最接近平值的实值期权
                if real_value_options:
                    real_value_options.sort(key=lambda x: x[1] - undl_price)
                    buy_code = real_value_options[0][0]
                    buy_strike = real_value_options[0][1]
                    print(f"实值认沽期权不足，选择最接近平值的实值期权买入: {buy_code}, 行权价: {buy_strike}")
                else:
                    print("没有实值认沽期权可用")
                    return None, 0, None, 0
            
            # 选择虚值期权
            if sell_level <= len(virtual_value_options):
                sell_index = sell_level - 1
                sell_code = virtual_value_options[sell_index][0]
                sell_strike = virtual_value_options[sell_index][1]
                print(f"选择虚{sell_level}档认沽期权卖出: {sell_code}, 行权价: {sell_strike}")
            else:
                # 如果虚值期权不足，使用最接近平值的虚值期权
                if virtual_value_options:
                    virtual_value_options.sort(key=lambda x: undl_price - x[1])
                    sell_code = virtual_value_options[0][0]
                    sell_strike = virtual_value_options[0][1]
                    print(f"虚值认沽期权不足，选择最接近平值的虚值期权卖出: {sell_code}, 行权价: {sell_strike}")
                else:
                    print("没有虚值认沽期权可用")
                    return None, 0, None, 0
        
        return buy_code, buy_strike, sell_code, sell_strike
        
    except Exception as e:
        print(f"按档位选择期权时出错: {e}")
        return None, 0, None, 0

def select_best_options(ContextInfo, option_type="CALL"):
    """
    自动选择最佳期权合约
    
    参数:
    ContextInfo: 上下文信息
    option_type: 期权类型，"CALL"或"PUT"
    
    返回:
    tuple: (买入期权代码, 买入行权价, 卖出期权代码, 卖出行权价)
    """
    try:
        # 获取标的价格
        undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
        
        # 获取当前波动率环境
        iv_avg = get_average_implied_volatility(ContextInfo, g.undl_code)
        
        print(f"当前标的价格: {undl_price}, 隐含波动率: {iv_avg:.2%}")
        
        # 获取期权链
        now = time.strftime("%Y%m%d")
        option_list = get_current_month_option(ContextInfo, g.undl_code, now, option_type)
        
        if not option_list:
            print(f"未找到{option_type}期权合约")
            return None, 0, None, 0
        
        # 根据期权类型和波动率环境选择最佳期权
        if option_type == "CALL":
            # 分析期权链特征
            strikes = []
            for code in option_list:
                try:
                    detail = ContextInfo.get_option_detail_data(code)
                    strike = detail['OptExercisePrice']
                    strikes.append(strike)
                except:
                    continue
            
            if not strikes:
                print("无法获取期权行权价")
                return None, 0, None, 0
            
            # 计算平值期权
            atm_strike = min(strikes, key=lambda x: abs(x - undl_price))
            
            # 确定买入和卖出的最佳期权档位
            if iv_avg > 0.4:  # 高波动率环境
                buy_level, sell_level = 3, 1
            elif 0.25 <= iv_avg <= 0.4:  # 中等波动率环境
                buy_level, sell_level = 2, 1
            else:  # 低波动率环境
                buy_level, sell_level = 1, 1
                
            print(f"牛市价差 - 建议买入实{buy_level}档，卖出虚{sell_level}档")
            
        else:  # PUT
            # 分析期权链特征
            strikes = []
            for code in option_list:
                try:
                    detail = ContextInfo.get_option_detail_data(code)
                    strike = detail['OptExercisePrice']
                    strikes.append(strike)
                except:
                    continue
            
            if not strikes:
                print("无法获取期权行权价")
                return None, 0, None, 0
            
            # 计算平值期权
            atm_strike = min(strikes, key=lambda x: abs(x - undl_price))
            
            # 确定买入和卖出的最佳期权档位
            if iv_avg > 0.4:  # 高波动率环境
                buy_level, sell_level = 3, 1
            elif 0.25 <= iv_avg <= 0.4:  # 中等波动率环境
                buy_level, sell_level = 2, 1
            else:  # 低波动率环境
                buy_level, sell_level = 1, 1
                
            print(f"熊市价差 - 建议买入实{buy_level}档，卖出虚{sell_level}档")
        
        # 调用select_options_by_levels函数选择期权合约
        return select_options_by_levels(ContextInfo, option_type, buy_level, sell_level)
        
    except Exception as e:
        print(f"自动选择最佳期权合约时出错: {e}")
        return None, 0, None, 0