#encoding:gbk

"""
期货策略 - 红白线系统多空双向交易
策略逻辑：
1. 做多：突破5分钟白线或回调到红线支撑位
2. 做空：跌破5分钟红线或回调到白线压力位
3. 止盈止损：5分钟红白线止损，动态止盈
4. 时间过滤：避开收盘前和夜盘收盘前的交易
"""

"""
期货策略 - 红白线系统止损策略
版本: 1.1.0
最后更新: 2023-05-27

修改记录:
- 1.1.0: 修复持仓时间计算问题，确保新开仓保护期后能正确启用ATR止损
  1. 修复quick_stop_loss_check函数中的持仓时间计算逻辑
  2. 修改deal_callback函数，正确处理限价单和市价单的成交回报
  3. 在handlebar中提前设置开仓时间，避免等待成交回报
  4. 增加详细日志输出，便于调试持仓时间计算问题
  5. 优化init函数中的时间戳初始化逻辑
"""

# ====== 可调整的策略参数 ======
class Config:
    # 开仓参数
    OPEN_LONG_MAX = 3      # 最大开多仓位数
    OPEN_SHORT_MAX = 3     # 最大开空仓位数
    TRADE_HANDS = 1        # 每次交易手数
    SLEEP_TIME = 10        # 开仓间隔时间(分钟)
    
    # 分批止盈设置
    TAKE_PROFIT_1 = 0.16   # 第一档止盈比例% (从1.0改为0.25)
    TAKE_PROFIT_2 = 0.5    # 第二档止盈比例% (从1.5改为0.5)
    TAKE_PROFIT_3 = 0.8    # 第三档止盈比例% (从2.0改为1.0)
    PROFIT_PCT_1 = 0.3     # 第一档止盈比例
    PROFIT_PCT_2 = 0.3     # 第二档止盈比例
    PROFIT_PCT_3 = 0.4     # 第三档止盈比例
    
    # ATR止盈参数
    ATR_TAKE_PROFIT_ENABLE = True    # 是否启用ATR止盈
    ATR_TAKE_PROFIT_MULTIPLE = 0.18   # ATR止盈倍数
    ATR_TAKE_PROFIT_PCT = 1.0        # ATR止盈时平仓比例(1.0表示全平)
    
    # ATR止损参数
    ATR_STOP_LOSS_ENABLE = False     # 是否启用ATR止损（已关闭）
    
    # 成交量参数
    VOLUME_THRESHOLD_LONG = 1.2  # 做多成交量阈值
    
    # 添加与55均线距离阈值参数
    MA55_DISTANCE_THRESHOLD = 0.004  # 价格与55均线的最大偏离百分比，默认0.5%
    
    # 添加与红白线距离阈值参数
    RED_LINE_DISTANCE_THRESHOLD = 0.004  # 做多时价格与红线最大偏离百分比，默认0.55%
    WHITE_LINE_DISTANCE_THRESHOLD = 0.004  # 做空时价格与白线最大偏离百分比，默认0.55%
    
    # 尾盘清仓时间设置
    DAY_CLOSE_HOUR = 14      # 日盘收盘小时
    DAY_CLOSE_MINUTE = 56    # 日盘清仓分钟
    NIGHT_CLOSE_HOUR = 22    # 夜盘收盘小时
    NIGHT_CLOSE_MINUTE = 56  # 夜盘清仓分钟
    
    # 开盘禁止交易时间设置（分钟）
    DAY_OPEN_NO_TRADE_MINUTES = 5    # 日盘开盘后禁止交易分钟数
    NIGHT_OPEN_NO_TRADE_MINUTES = 5  # 夜盘开盘后禁止交易分钟数
    
    # 添加红白线转换强度参数
    PRICE_STRENGTH_THRESHOLD = 0.0003  # 价格强度阈值（价格高于原白线的最小百分比）
    VOLUME_STRENGTH_THRESHOLD = 1.3   # 成交量强度阈值（相对于前3根K线均值）
    CANDLE_BODY_RATIO = 0.6          # K线实体占比阈值
    
    # 开仓价格优化参数
    LONG_PRICE_DISCOUNT = 0.0003   # 做多时限价单折扣，比当前价低0.1%
    SHORT_PRICE_PREMIUM = 0.0003   # 做空时限价单溢价，比当前价高0.1%
    PRICE_ORDER_TIMEOUT = 3       # 限价单超时K线数，超时后改用市价单
    
    # 添加回调比例参数
    PULLBACK_RATIO_MIN = 0.3   # 回调/反弹比例下限
    PULLBACK_RATIO_MAX = 0.7   # 回调/反弹比例上限
    PULLBACK_LOOKBACK = 5      # 计算高低点的回溯K线数

    # 添加红白线持续时间阈值参数
    RED_LINE_DURATION_THRESHOLD = 15   # 红线超过此值需谨慎做多
    WHITE_LINE_DURATION_THRESHOLD = 15  # 白线超过此值需谨慎做空
    RED_LINE_DURATION_MAX = 25         # 红线超过此值禁止做多
    WHITE_LINE_DURATION_MAX = 25       # 白线超过此值禁止做空
    
    # 添加ATR反向开仓参数
    REVERSE_TRADE_ENABLE = False       # 是否启用ATR止损后反向开仓（已关闭）
    REVERSE_TRADE_HANDS = 1           # 反向开仓手数
    REVERSE_TAKE_PROFIT_1 = 0.12      # 反向开仓第一档止盈比例%
    REVERSE_TAKE_PROFIT_2 = 0.4       # 反向开仓第二档止盈比例%
    REVERSE_TAKE_PROFIT_3 = 0.6       # 反向开仓第三档止盈比例%
    REVERSE_PROFIT_PCT_1 = 0.3        # 反向开仓第一档止盈仓位比例
    REVERSE_PROFIT_PCT_2 = 0.3        # 反向开仓第二档止盈仓位比例
    REVERSE_PROFIT_PCT_3 = 0.4        # 反向开仓第三档止盈仓位比例
    REVERSE_STOP_LOSS_ATR = 1.5       # 反向开仓ATR止损倍数 (从1.0改为1.5)
    REVERSE_MIN_CONFIDENCE = 60       # 反向开仓最低信心分数(0-100)
    REVERSE_EVALUATION_ENABLE = False  # 是否执行反向开仓评估(True:评估后决定是否开仓, False:直接开仓)
    
    # 新增参数
    REVERSE_MIN_STOP_DISTANCE = 5     # 反向开仓后止损线与开仓价格的最小距离（以最小变动价位为单位）
    NEW_POSITION_PROTECTION_MINUTES = 7  # 新开仓保护期（分钟），在此期间内不触发ATR止损
    REVERSE_PROTECTION_MINUTES = 7   # 反向开仓保护期（分钟），在此期间内不触发ATR止损
    MIN_STOP_DISTANCE = 10            # 普通开仓止损线与开仓价格的最小距离（以最小变动价位为单位）
    
    # 普通开仓价格优化参数
    ENTRY_PRICE_OPTIMIZE_ENABLE = True  # 是否启用价格优化
    ENTRY_WAIT_SECONDS = 45            # 最长等待时间(秒)
    ENTRY_TARGET_DIP_PCT = 0.0005      # 做多时期望回调比例
    ENTRY_TARGET_BOUNCE_PCT = 0.0005   # 做空时期望反弹比例
    
    # ATR参数
    ATR_WINDOW = 15        # ATR计算窗口大小，从5改为15，减少短期波动影响
    
    # 新增：开仓最优价格回调/反弹百分比参数
    ENTRY_OPTIMAL_PCT = 0.0005  # 开仓最优价格回调/反弹百分比（0.05%）

import math
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 定义全局账户变量
account = "*********"

def log_trade(action, price, profit=None):
    """记录交易信息"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    if profit is not None:
        print(f"[{timestamp}] {action}: 价格={price:.2f}, 收益={profit:.2f}%")
    else:
        print(f"[{timestamp}] {action}: 价格={price:.2f}")

def calculate_profit(current_price, open_price, is_long=True):
    """
    计算收益率
    
    参数:
    current_price: 当前价格
    open_price: 开仓价格
    is_long: 是否为多头持仓
    
    返回:
    收益率(%)
    """
    if is_long:
        return (current_price/open_price - 1) * 100
    return (open_price/current_price - 1) * 100

class G():
    def __init__(self):
        # 持仓状态
        self.position = "none"  # "none", "long", "short"
        self.hold_price = 0     # 持仓最高/最低价
        self.open_price = 0     # 开仓价格
        
        # 交易计数
        self.buy_long = 0       # 做多次数
        self.buy_short = 0      # 做空次数
        
        # 做多止盈标记
        self.profit_taken_1 = False
        self.profit_taken_2 = False  
        self.profit_taken_3 = False
        
        # 做空止盈标记
        self.profit_taken_1_short = False
        self.profit_taken_2_short = False
        self.profit_taken_3_short = False
        
        # 其他状态
        self.trace_time_long = 0  # 上次做多交易时间
        self.trace_time_short = 0  # 上次做空交易时间
        self.last_close_time = 0   # 上次平仓时间，用于控制反向交易
        self.opened_t = []        # 已开仓的时间点
        self.hold_code = None     # 持仓代码
        self.code = None          # 当前代码
        self.position_size = 0    # 持仓数量记录
        self.sysid = {}           # 订单系统ID
        self.cover = 1            # 尾盘平仓标记
        self.info = None          # 合约信息
        
        # 反向开仓标记
        self.is_reverse_trade = False  # 是否为反向开仓
        self.reverse_reason = ""       # 反向开仓原因
        
        # ATR值
        self.atr_value = 0             # 当前ATR值
        
        # 统计计数
        self.buy_long = 0              # 开多次数
        self.buy_short = 0             # 开空次数
        
        # 分批止盈标记
        self.profit_taken_1 = False    # 多单第一档止盈标记
        self.profit_taken_2 = False    # 多单第二档止盈标记
        self.profit_taken_3 = False    # 多单第三档止盈标记
        self.profit_taken_1_short = False  # 空单第一档止盈标记
        self.profit_taken_2_short = False  # 空单第二档止盈标记
        self.profit_taken_3_short = False  # 空单第三档止盈标记
        
        # 限价单状态
        self.pending_limit_order = False    # 是否有挂单
        self.limit_order_time = 0           # 挂单时间
        
        # 其他设置
        self.code = None           # 合约代码
        self.future_code = None    # 期货合约代码
        self.future_account = None  # 期货账户
        self.sysid = {}           # 系统ID
        self.cover = 0            # 平仓标志
        self.trace_time_long = 0         # 多单开仓时间
        self.trace_time_short = 0        # 空单开仓时间
        self.position_time = datetime.now()  # 持仓时间
        self.last_position = "none"    # 上一次持仓方向
        self.reverse_open_time = None  # 反向开仓时间
        # ====== 新增：突破/跌破后回踩确认状态 ======
        self.waiting_long_confirm = False  # 是否等待回踩白线确认做多
        self.waiting_short_confirm = False # 是否等待反抽红线确认做空
        self.break_white_price = None      # 突破白线时的白线价格
        self.break_red_price = None        # 跌破红线时的红线价格
        self.waiting_long_time = 0         # 等待回踩确认开始时间戳
        self.waiting_short_time = 0        # 等待反抽确认开始时间戳
        self.waiting_long_bars = 0         # 等待回踩确认已历K线数
        self.waiting_short_bars = 0        # 等待反抽确认已历K线数
        self.break_white_break_price = None  # 记录突破时价格
        self.position_source = None  # 新增：记录本次持仓的开仓信号来源

# 实例化全局变量g
g = G()

def init(ContextInfo):
    """
    初始化函数 - 在策略启动时执行一次
    
    参数:
    ContextInfo: 上下文信息
    """
    global g  # 声明使用全局变量g
    
    # 设置交易账户
    ContextInfo.set_account(account)
    
    # 获取市场代码
    market = ContextInfo.market
    market = market.replace("SHFE",'SF').replace("CZCE",'ZF').replace("DCE",'DF').replace("CFFEX",'IF')
    
    # 获取合约代码
    code = ContextInfo.stockcode + '.' + ContextInfo.market
    
    # 自动获取主力合约
    g.code = ContextInfo.get_main_contract(code) + '.' + market
    g.market = market
    
    # 获取合约信息
    g.info = ContextInfo.get_instrumentdetail(g.code)
    
    # 初始化尾盘清仓标志
    g.cover = 0
    
    # 初始化上次交易时间，设置为当前时间减去开仓间隔时间，确保策略启动后不会立即开仓
    current_time = time.time()
    max_wait_seconds = 30 * 60  # 与should_reverse_trade函数中保持一致
    g.trace_time_long = current_time - Config.SLEEP_TIME * 60  # 设置为当前时间减去开仓间隔时间，确保开仓检查通过
    g.trace_time_short = current_time - Config.SLEEP_TIME * 60
    g.last_close_time = current_time - max_wait_seconds - 1  # 确保初始状态下不会立即触发反向交易
    
    # 输出初始化的时间戳
    print(f"初始化多头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_long))}")
    print(f"初始化空头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_short))}")
    print(f"初始化上次平仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.last_close_time))}")
    
    # 初始化其他变量
    g.position = "none"  # 当前持仓方向
    g.position_size = 0  # 当前持仓手数
    g.buy_long = 0  # 开多次数
    g.buy_short = 0  # 开空次数
    g.hold_price = 0  # 持仓价格
    g.open_price = 0  # 开仓价格
    g.hold_code = ""  # 持仓合约
    g.opened_t = []  # 已开仓时间
    g.sysid = {}  # 系统ID
    
    # 初始化止盈标记
    g.profit_taken_1 = False
    g.profit_taken_2 = False
    g.profit_taken_3 = False
    g.profit_taken_1_short = False
    g.profit_taken_2_short = False
    g.profit_taken_3_short = False
    
    # 初始化反向开仓标记
    g.is_reverse_trade = False
    g.reverse_reason = ""
    
    # 根据品种调整参数
    if "MA" in g.code:  # 甲醇
        g.volume_threshold = Config.VOLUME_THRESHOLD_LONG
        g.ma_period = 55
        print(f"{g.code} 红白线系统止损策略初始化完成 - 甲醇品种")
    elif "p" in g.code:  # 棕榈
        g.volume_threshold = Config.VOLUME_THRESHOLD_LONG * 1.2  # 棕榈品种成交量阈值调高20%
        g.ma_period = 55
        print(f"{g.code} 红白线系统止损策略初始化完成 - 棕榈品种")
    else:
        g.volume_threshold = Config.VOLUME_THRESHOLD_LONG
        g.ma_period = 55
        print(f"{g.code} 红白线系统止损策略初始化完成")
    
    # 输出初始化参数
    print(f"策略参数: 成交量阈值={g.volume_threshold}, MA周期={g.ma_period}")
    
    # 输出反向开仓参数
    if Config.REVERSE_TRADE_ENABLE:
        eval_mode = "评估模式" if Config.REVERSE_EVALUATION_ENABLE else "直接开仓模式"
        print(f"ATR止损反向开仓已启用: 反向开仓手数={Config.REVERSE_TRADE_HANDS}, 止盈档位={Config.REVERSE_TAKE_PROFIT_1}/{Config.REVERSE_TAKE_PROFIT_2}/{Config.REVERSE_TAKE_PROFIT_3}, {eval_mode}")
    else:
        print("ATR止损反向开仓功能已禁用")
    g.waiting_long_confirm = False
    g.waiting_short_confirm = False
    g.break_white_price = None
    g.break_red_price = None
    g.waiting_long_time = 0
    g.waiting_short_time = 0
    g.waiting_long_bars = 0
    g.waiting_short_bars = 0

def calculate_lines(price_series):
    """
    计算红白线
    
    参数:
    price_series: 价格序列
    
    返回:
    (white_line, red_line): 白线和红线序列
    """
    try:
        # 原有的计算逻辑
        length = len(price_series)
        white_line = pd.Series([None] * length, index=price_series.index)
        red_line = pd.Series([None] * length, index=price_series.index)
        
        last_white = None
        last_red = None
        
        for i in range(length):
            # 如果既没有白线也没有红线，初始化
            if last_white is None and last_red is None:
                # 根据第一个价格的位置确定初始线
                if i > 0:
                    if price_series.iloc[i] > price_series.iloc[i-1]:
                        last_red = price_series.iloc[i-1]  # 上涨，设置红线
                        red_line.iloc[i] = last_red
                        white_line.iloc[i] = None
                    else:
                        last_white = price_series.iloc[i-1]  # 下跌，设置白线
                        white_line.iloc[i] = last_white
                        red_line.iloc[i] = None
                else:
                    # 第一个价格，默认设置为白线
                    last_white = price_series.iloc[i]
                    white_line.iloc[i] = last_white
                    red_line.iloc[i] = None
            
            # 如果有白线，检查是否突破
            elif last_white is not None:
                if price_series.iloc[i] > last_white:  # 突破白线
                    white_line.iloc[i] = None
                    red_line.iloc[i] = last_white  # 白线转为红线
                    last_red = last_white
                    last_white = None
                else:  # 未突破，保持白线
                    white_line.iloc[i] = last_white
                    red_line.iloc[i] = None
                    
            # 如果有红线，检查是否跌破
            elif last_red is not None:
                if price_series.iloc[i] < last_red:  # 跌破红线
                    red_line.iloc[i] = None
                    white_line.iloc[i] = last_red  # 红线转为白线
                    last_white = last_red
                    last_red = None
                else:  # 未跌破，保持红线
                    red_line.iloc[i] = last_red
                    white_line.iloc[i] = None
        
        return white_line, red_line
    except Exception as e:
        print(f"计算红白线出错: {e}")
        return pd.Series([None] * len(price_series)), pd.Series([None] * len(price_series))

def calculate_ma(close, volume, period):
    """
    计算成交量加权均线
    
    参数:
    close: 收盘价序列
    volume: 成交量序列
    period: 周期
    
    返回:
    ma: 成交量加权均线
    """
    weighted_price = close * volume
    ma = weighted_price.rolling(window=period).sum() / volume.rolling(window=period).sum()
    return ma



def handlebar(ContextInfo):
    """主策略函数"""
    global g
    
    # 如果不是最后一根K线，直接返回
    if not ContextInfo.is_last_bar():
        return
    
    # 同步持仓状态
    sync_position_status(ContextInfo)
    
    # 获取时间和价格数据
    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    try:
        t = time.strftime("%H%M%S", time.localtime(timetag))
    except (TypeError, ValueError, OSError):
        t = "000000"
    
    # 添加持仓时间检查和记录
    current_time = time.time()
    if g.position == "long" and hasattr(g, 'trace_time_long') and g.trace_time_long > 0:
        holding_minutes = (current_time - g.trace_time_long) / 60
        print(f"\n持仓时间检查: 多头持仓已经 {holding_minutes:.2f} 分钟")
        print(f"持仓时间详情: 当前时间={time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))}, 开仓时间={time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_long))}")
        
        # 检查是否超过保护期
        if holding_minutes >= Config.NEW_POSITION_PROTECTION_MINUTES:
            print(f"警告: 持仓时间已超过保护期({holding_minutes:.2f} > {Config.NEW_POSITION_PROTECTION_MINUTES}分钟)，应启用ATR止损")
    elif g.position == "short" and hasattr(g, 'trace_time_short') and g.trace_time_short > 0:
        holding_minutes = (current_time - g.trace_time_short) / 60
        print(f"\n持仓时间检查: 空头持仓已经 {holding_minutes:.2f} 分钟")
        print(f"持仓时间详情: 当前时间={time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))}, 开仓时间={time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_short))}")
        
        # 检查是否超过保护期
        if holding_minutes >= Config.NEW_POSITION_PROTECTION_MINUTES:
            print(f"警告: 持仓时间已超过保护期({holding_minutes:.2f} > {Config.NEW_POSITION_PROTECTION_MINUTES}分钟)，应启用ATR止损")
    
    # ----------- 修复：初始化kaiduo和kaikong，防止未赋值引用报错 -----------
    kaiduo = False
    kaikong = False
    # ----------- 新增：初始化所有信号变量，防止未赋值引用报错 -----------
    confirm_long = False
    pullback_long_good = False
    allow_red_line_long = False
    red_support_long = False
    transition_long = False
    ratio_ok = False
    confirm_short = False
    pullback_short_good = False
    allow_white_line_short = False
    transition_short = False
    # ----------------------------------------------------------------------
    
    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    bar_date = timetag_to_datetime(timetag, '%Y%m%d%H%M%S')
    start, end = after_init(ContextInfo)
    price = ContextInfo.get_market_data(['close','amount','volume','high','low','open'],[g.code],period='1m',
                    start_time=start+'200000',
                    end_time=bar_date,
                    )
    price_1d = ContextInfo.get_market_data(['close','open'],[g.code],period='1d',
                    start_time=start,
                    end_time=start,
                    count=1)
    print('price_1d',price_1d)
    
    # 确保current_price已定义，防止后续代码出错
    try:
        # 如果current_price未定义，则获取它
        current_price
    except NameError:
        try:
            # 获取K线数据 - 使用5分钟周期
            price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                              [g.code], 
                                              period='5m',
                                              count=60)
            
            # 获取当前价格
            current_price = price_5m['close'].iloc[-1]
            
            # 计算ATR值并保存到全局变量
            g.atr_value = calculate_atr(ContextInfo)
        except Exception as e:
            print(f"获取价格数据出错: {e}")
            return
    
    #price.to_csv('d:/ma.csv')
    price_len = price.shape[0]
    C = price['close']
    # 获取当前时间
    hour = int(t[:2])
    minute = int(t[2:4])
    
    # 定义时间过滤条件
    is_day_open_no_trade = hour == 9 and minute < Config.DAY_OPEN_NO_TRADE_MINUTES
    is_night_open_no_trade = hour == 21 and minute < Config.NIGHT_OPEN_NO_TRADE_MINUTES
    
    # 定义b1时间过滤条件
    b1 = not ((hour == Config.DAY_CLOSE_HOUR and minute >= Config.DAY_CLOSE_MINUTE) or 
              (hour == Config.NIGHT_CLOSE_HOUR and minute >= Config.NIGHT_CLOSE_MINUTE) or
              is_day_open_no_trade or 
              is_night_open_no_trade)
    
    # 检查是否需要尾盘清仓
    if ((hour == Config.DAY_CLOSE_HOUR and minute >= Config.DAY_CLOSE_MINUTE) or 
        (hour == Config.NIGHT_CLOSE_HOUR and minute >= Config.NIGHT_CLOSE_MINUTE)):
        # 设置尾盘清仓标志
        g.cover = 1
        
        # 如果有持仓，执行尾盘清仓
        if g.position != "none" and g.position_size > 0:
            if g.position == "long":
                passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '尾盘平多', ContextInfo)
                log_trade("尾盘平多", 0)
            elif g.position == "short":
                passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '尾盘平空', ContextInfo)
                log_trade("尾盘平空", 0)
            
            g.position = "none"
            g.position_size = 0
            g.is_reverse_trade = False  # 重置反向开仓标记
            return
    else:
        g.cover = 0
    
    try:
        # 获取K线数据 - 使用5分钟周期
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                          [g.code], 
                                          period='5m',
                                          count=60)
        
        # 获取当前价格
        current_price = price_5m['close'].iloc[-1]
        
        # 计算ATR值并保存到全局变量
        g.atr_value = calculate_atr(ContextInfo)
        
        # 检查是否有持仓，进行止盈止损检查
        if g.position != "none":
            # 打印持仓详情
            print_position_details(current_price)
            
            # 普通止盈检查
            executed_take_profit = execute_take_profit(ContextInfo, current_price)
            
            # 新增ATR止盈检查
            if not executed_take_profit:
                executed_atr_take_profit = check_atr_take_profit(ContextInfo, current_price)
                if executed_atr_take_profit:
                    # 如果ATR止盈成功执行并全部平仓，跳过后续处理
                    if g.position == "none":
                        return
        # 检查是否有持仓，进行止盈止损检查
        if g.position != "none":
            # ...持仓管理相关代码...
            return

        # ====== 开仓信号判定日志 ======
        # 做多信号判定详细日志（中文）
        print(f"【做多信号判定】 突破白线确认: {confirm_long}，回调到红线: {pullback_long_good}，允许红线做多: {allow_red_line_long}，红线支撑: {red_support_long}，红白线转换: {transition_long}，回调比例合格: {ratio_ok}，MA均线: {MA_Line.iloc[-1] if 'MA_Line' in locals() else 'N/A'}，已开多仓数: {g.buy_long}，时间过滤: {b1}")
        print(f"kaiduo（最终多头开仓信号）: {kaiduo}")
        # 做空信号判定详细日志（中文）
        print(f"【做空信号判定】 跌破红线确认: {confirm_short}，反弹到白线: {pullback_short_good}，允许白线做空: {allow_white_line_short}，红白线转换: {transition_short}，MA均线: {MA_Line.iloc[-1] if 'MA_Line' in locals() else 'N/A'}，已开空仓数: {g.buy_short}，时间过滤: {b1}")
        print(f"kaikong（最终空头开仓信号）: {kaikong}")
        # ====== 开仓信号判定日志 ======
        # 做多信号
        if kaiduo:
            if confirm_long:
                print("最终满足开仓条件：突破白线后回踩确认做多（confirm_long）")
            elif pullback_long_good and allow_red_line_long:
                print("最终满足开仓条件：回调到红线做多（pullback_long_good）")
            elif red_support_long and not transition_signal == 1 and allow_red_line_long:
                print("最终满足开仓条件：红线支撑做多（red_support_long）")
            elif transition_long:
                print("最终满足开仓条件：红白线转换信号做多（transition_long）")
            else:
                print("最终满足开仓条件：其它做多信号")
        else:
            print("所有做多信号条件均未满足，未开多仓")
        # 做空信号
        if kaikong:
            if confirm_short:
                print("最终满足开仓条件：跌破红线后反抽确认做空（confirm_short）")
            elif pullback_short_good and allow_white_line_short:
                print("最终满足开仓条件：反弹到白线做空（pullback_short_good）")
            elif transition_short:
                print("最终满足开仓条件：红白线转换信号做空（transition_short）")
            else:
                print("最终满足开仓条件：其它做空信号")
        else:
            print("所有做空信号条件均未满足，未开空仓")
            # 如果有反向开仓的持仓，检查反向开仓的止盈条件
            if g.is_reverse_trade:
                executed_reverse_take_profit = execute_reverse_take_profit(ContextInfo, current_price)
                if executed_reverse_take_profit and g.position == "none":
                    # 如果反向开仓止盈成功执行并全部平仓，跳过后续处理
                    return
            
            # 普通的ATR止损检查
            executed_stop_loss = enhanced_stop_loss_check(ContextInfo, current_price)
            if executed_stop_loss and g.position == "none":
                # 如果止损成功执行并平仓，检查是否需要反向开仓
                if Config.REVERSE_TRADE_ENABLE:
                    reversed_position = should_reverse_after_atr_stop(ContextInfo, current_price, g.last_position)
                    if reversed_position:
                        # 如果成功反向开仓，无需继续处理
                        return
            
            # 如果是反向开仓的持仓，检查反向开仓的止损条件
            if g.is_reverse_trade:
                executed_reverse_stop_loss = check_reverse_stop_loss(ContextInfo, current_price)
                if executed_reverse_stop_loss and g.position == "none":
                    # 如果反向开仓止损成功执行并平仓，跳过后续处理
                    return
            
            # 红白线止损和反向开仓检查
            executed_line_stop = check_stop_loss_and_reverse(ContextInfo, current_price)
            if executed_line_stop:
                # 如果红白线止损和反向开仓成功执行，跳过后续处理
                return
        
        # 使用文华指标源代码计算红白线
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        
        # 计算红白线持续时间并设置全局标志
        red_line_duration = check_line_duration(red_line_5m, "red")
        white_line_duration = check_line_duration(white_line_5m, "white")
        print(f"红线持续时间检查: 已持续{red_line_duration}根K线")
        print(f"白线持续时间检查: 已持续{white_line_duration}根K线")
        
        # 设置全局标志，控制是否允许基于红/白线的交易
        allow_red_line_long = red_line_duration <= Config.RED_LINE_DURATION_MAX
        allow_white_line_short = white_line_duration <= Config.WHITE_LINE_DURATION_MAX
        
        if not allow_red_line_long:
            print(f"!! 警告: 红线已持续{red_line_duration}根K线，超过{Config.RED_LINE_DURATION_MAX}根最大阈值，禁止任何基于红线的做多操作")
        
        if not allow_white_line_short:
            print(f"!! 警告: 白线已持续{white_line_duration}根K线，超过{Config.WHITE_LINE_DURATION_MAX}根最大阈值，禁止任何基于白线的做空操作")
        
        # 计算30分钟红白线
        price_30m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                            [g.code], 
                                            period='30m',
                                            count=60)
        white_line_30m, red_line_30m = calculate_red_white_lines_exact(price_30m)
        
        # 计算成交量加权均线
        MA_Line = calculate_ma(price_5m['close'], price_5m['volume'], 55)
        
        # 打印回调/反弹比例信息
        print(f"\n=== 红白线与回调比例 ===")
        print(f"当前价格: {current_price:.2f}")
        
        # 修复格式化问题，避免对None值使用格式化字符串
        if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
            print(f"5分钟白线: {white_line_5m.iloc[-1]:.2f}")
        else:
            print("5分钟白线: 无")
            
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            print(f"5分钟红线: {red_line_5m.iloc[-1]:.2f}")
        else:
            print("5分钟红线: 无")
            
        # 使用增强版的回调比例打印函数
        print_enhanced_pullback_ratios(price_5m, white_line_5m.iloc[-1], red_line_5m.iloc[-1])
        
        # 初始化交易信号变量
        break_white_long = False    # 突破白线做多
        break_red_short = False     # 跌破红线做空
        pullback_long_good = False  # 回调到红线做多
        pullback_short_good = False # 反弹到白线做空
        red_support_long = False    # 红线支撑做多
        first_break_white = False   # 初始化突破白线变量
        
        # 检查是否跌破红线
        break_red = check_break_red_line(price_5m, 
                                       red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None, 
                                       red_line_5m.iloc[-1])
        
        # 检查是否突破白线
        if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
            if current_price > white_line_5m.iloc[-1]:
                break_white_long = True
                print(f"检测到突破白线: 价格{current_price} > 白线{white_line_5m.iloc[-1]}")
        
        # 检查是否回调到红线做多
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            if price_5m['low'].iloc[-1] <= red_line_5m.iloc[-1] and current_price > red_line_5m.iloc[-1]:
                # 首先检查全局标志
                if not allow_red_line_long:
                    pullback_long_good = False
                    print("回调到红线做多: 因红线持续时间过长，直接拒绝")
                else:
                    # 基本回调条件满足
                    pullback_long_good = True
                    
                    # 检查回调比例是否合理
                    ratio_ok, pullback_ratio = check_pullback_ratio(price_5m, red_line_5m.iloc[-1], True)
                    
                    # 根据红线持续时间动态调整入场条件
                    if red_line_duration > Config.RED_LINE_DURATION_THRESHOLD:
                        # 红线持续时间较长，增加额外条件检查
                        
                        # 1. 要求更严格的回调比例
                        ideal_pullback_min = 0.4  # 提高下限
                        ideal_pullback_max = 0.6  # 降低上限
                        strict_ratio_ok = ideal_pullback_min <= pullback_ratio <= ideal_pullback_max
                        
                        # 2. 检查最近K线的涨幅是否减缓
                        recent_gain = (price_5m['close'].iloc[-1] / price_5m['close'].iloc[-5] - 1) * 100
                        previous_gain = (price_5m['close'].iloc[-6] / price_5m['close'].iloc[-10] - 1) * 100
                        momentum_ok = recent_gain >= previous_gain * 0.7  # 动能未明显减弱
                        
                        # 3. 检查成交量特征
                        recent_volume = price_5m['volume'].iloc[-3:].mean()
                        previous_volume = price_5m['volume'].iloc[-6:-3].mean()
                        volume_ok = price_5m['volume'].iloc[-1] > previous_volume  # 当前成交量大于前期
                        
                        # 综合判断
                        if strict_ratio_ok and (momentum_ok or volume_ok):
                            print(f"红线持续时间较长({red_line_duration}根K线)，但满足严格条件，允许做多")
                        else:
                            print(f"红线持续时间较长({red_line_duration}根K线)，且不满足严格条件，拒绝做多")
                            pullback_long_good = False
                    elif ratio_ok:
                        print(f"检测到回调到红线做多: 最低价{price_5m['low'].iloc[-1]} <= 红线{red_line_5m.iloc[-1]}, "
                              f"收盘价{current_price} > 红线{red_line_5m.iloc[-1]}, "
                              f"回调比例{pullback_ratio:.2%}【理想区间】")
                    else:
                        pullback_long_good = False  # 回调比例不理想，取消信号
                        print(f"回调到红线做多比例不理想: 回调比例{pullback_ratio:.2%}，超出30%-70%理想区间，可能处于红线末端")
        
        # 检查是否反弹到白线做空
        if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
            if price_5m['high'].iloc[-1] >= white_line_5m.iloc[-1] and current_price < white_line_5m.iloc[-1]:
                # 首先检查全局标志
                if not allow_white_line_short:
                    pullback_short_good = False
                    print("反弹到白线做空: 因白线持续时间过长，直接拒绝")
                else:
                    # 基本反弹条件满足
                    pullback_short_good = True
                    
                    # 检查反弹比例是否合理
                    ratio_ok, pullback_ratio = check_pullback_ratio(price_5m, white_line_5m.iloc[-1], False)
                    
                    # 根据白线持续时间动态调整入场条件
                    if white_line_duration > Config.WHITE_LINE_DURATION_THRESHOLD:
                        # 白线持续时间较长，增加额外条件检查
                        
                        # 1. 要求更严格的反弹比例
                        ideal_pullback_min = 0.4  # 提高下限
                        ideal_pullback_max = 0.6  # 降低上限
                        strict_ratio_ok = ideal_pullback_min <= pullback_ratio <= ideal_pullback_max
                        
                        # 2. 检查最近K线的跌幅是否减缓
                        recent_decline = (price_5m['close'].iloc[-5] / price_5m['close'].iloc[-1] - 1) * 100
                        previous_decline = (price_5m['close'].iloc[-10] / price_5m['close'].iloc[-6] - 1) * 100
                        momentum_ok = recent_decline >= previous_decline * 0.7  # 下跌动能未明显减弱
                        
                        # 3. 检查成交量特征
                        recent_volume = price_5m['volume'].iloc[-3:].mean()
                        previous_volume = price_5m['volume'].iloc[-6:-3].mean()
                        volume_ok = price_5m['volume'].iloc[-1] > previous_volume  # 当前成交量大于前期
                        
                        # 综合判断
                        if strict_ratio_ok and (momentum_ok or volume_ok):
                            print(f"白线持续时间较长({white_line_duration}根K线)，但满足严格条件，允许做空")
                        else:
                            print(f"白线持续时间较长({white_line_duration}根K线)，且不满足严格条件，拒绝做空")
                            pullback_short_good = False
                    elif ratio_ok:
                        print(f"检测到反弹到白线做空: 最高价{price_5m['high'].iloc[-1]} >= 白线{white_line_5m.iloc[-1]}, "
                              f"收盘价{current_price} < 白线{white_line_5m.iloc[-1]}, "
                              f"反弹比例{pullback_ratio:.2%}【理想区间】")
                    else:
                        pullback_short_good = False  # 反弹比例不理想，取消信号
                        print(f"反弹到白线做空比例不理想: 反弹比例{pullback_ratio:.2%}，超出30%-70%理想区间，可能处于白线末端")
        
        # 修改红线支撑做多条件，增加转换期保护和持续时间检查
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            # 首先检查全局标志
            if not allow_red_line_long:
                red_support_long = False
                print("红线支撑做多: 因红线持续时间过长，直接拒绝")
            else:
                # 检查是否处于转换期（前一根是白线，当前是红线）
                is_transition_period = (white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]) and
                                      white_line_5m.iloc[-1] is None and
                                      red_line_5m.iloc[-1] is not None)
                
                # 检查红线持续时间
                red_line_duration = check_line_duration(red_line_5m, "red")
                print(f"红线持续时间检查: 已持续{red_line_duration}根K线")
                
                if current_price > red_line_5m.iloc[-1] and check_volume_increase(price_5m['volume']):
                    if not is_transition_period:
                        # 根据红线持续时间进行风险评估
                        if red_line_duration > Config.RED_LINE_DURATION_MAX:
                            print(f"? 红线已持续{red_line_duration}根K线，超过{Config.RED_LINE_DURATION_MAX}根最大阈值，拒绝红线支撑做多")
                            red_support_long = False
                        elif red_line_duration > Config.RED_LINE_DURATION_THRESHOLD:
                            # 检查回调比例是否合理，只有在理想区间才考虑做多
                            ratio_ok, pullback_ratio = check_pullback_ratio(price_5m, red_line_5m.iloc[-1], True)
                            if ratio_ok:
                                print(f"?? 红线已持续{red_line_duration}根K线，但回调比例在理想区间，谨慎做多")
                                red_support_long = True
                            else:
                                print(f"? 红线已持续{red_line_duration}根K线，且回调比例不理想，拒绝做多")
                                red_support_long = False
                        else:
                            # 红线持续时间在安全范围内，正常做多
                            red_support_long = True
                            print(f"检测到红线支撑做多: 价格{current_price} > 红线{red_line_5m.iloc[-1]}")
                    else:
                        print("处于白线转红线转换期，暂不考虑红线支撑做多")
        
        # 检测红白线转换
        transition_signal = detect_line_transition(
            white_line_5m.iloc[-2] if len(white_line_5m) > 1 else None,
            white_line_5m.iloc[-1],
            red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None,
            red_line_5m.iloc[-1]
        )
        
        # 计算趋势
        current_trend = check_trend(price_5m, MA_Line.iloc[-1])
        
        # 获取转换信号的交易建议
        transition_long, transition_short = should_trade_with_transition(
            transition_signal,
            current_price,
            MA_Line.iloc[-1],
            current_trend,
            price_5m['volume'],
            white_line_5m.iloc[-1],
            red_line_5m.iloc[-1],
            b1,  # 时间过滤参数
            MA_Line.iloc[-1],  # 55均线值
            white_line_5m.iloc[-2] if len(white_line_5m) > 1 else None,
            red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None
        )
        
        # ====== 新增：红白线转换信号多K线确认过滤 ======
        min_confirm_bars = 2  # 连续确认K线数
        # 多头转换信号过滤
        if transition_long:
            valid = True
            for i in range(1, min_confirm_bars+1):
                if red_line_5m.iloc[-i] is not None and not pd.isna(red_line_5m.iloc[-i]):
                    if price_5m['close'].iloc[-i] <= red_line_5m.iloc[-i]:
                        valid = False
                        break
            if not valid:
                print(f"多K线确认过滤：红白线转换做多信号未通过，最近{min_confirm_bars}根K线收盘价未全部站上红线，忽略本次做多信号")
                transition_long = False
        if transition_long == False:
            kaiduo = False
        # 空头转换信号过滤
        if transition_short:
            valid = True
            for i in range(1, min_confirm_bars+1):
                if white_line_5m.iloc[-i] is not None and not pd.isna(white_line_5m.iloc[-i]):
                    if price_5m['close'].iloc[-i] >= white_line_5m.iloc[-i]:
                        valid = False
                        break
            if not valid:
                print(f"多K线确认过滤：红白线转换做空信号未通过，最近{min_confirm_bars}根K线收盘价未全部跌破白线，忽略本次做空信号")
                transition_short = False
        if transition_short == False:
            kaikong = False
        # ====== 新增结束 ======
        
        # 修改开仓条件，增加转换期保护
        kaiduo = ((confirm_long or 
                   (pullback_long_good and allow_red_line_long) or 
                   (red_support_long and not transition_signal == 1 and allow_red_line_long) or  
                   transition_long) and 
                   current_price > MA_Line.iloc[-1] and 
                   g.buy_long < Config.OPEN_LONG_MAX and 
                   b1 and 
                   check_trend(price_5m, MA_Line.iloc[-1]) == 1 and
                   C[-1]>(price_1d.iloc[0]['close']+price_1d.iloc[0]['open'])/2
                   and ratio_ok)  # 新增：所有做多信号都必须满足回调比例
        
        kaikong = ((confirm_short or 
                  (pullback_short_good and allow_white_line_short) or 
                  transition_short) and 
                  current_price < MA_Line.iloc[-1] and 
                  g.buy_short < Config.OPEN_SHORT_MAX and 
                  b1 and 
                  check_trend(price_5m, MA_Line.iloc[-1]) == -1 and
                  (red_line_5m.iloc[-1] is None or current_price < red_line_5m.iloc[-1]))  and C[-1]<(price_1d.iloc[0]['close']+price_1d.iloc[0]['open'])/2
        
        # 是否可以开仓
        can_open_position = True  # 默认可以开仓
        
        # 增加价格与红白线距离的过滤条件
        red_line_distance_ok, white_line_distance_ok = check_and_print_line_distance(current_price, white_line_5m.iloc[-1], red_line_5m.iloc[-1])
        
        # 根据距离检查结果修改开仓条件
        if (confirm_long or pullback_long_good or (red_support_long and not transition_signal == 1) or transition_long) and not red_line_distance_ok:
            print("因价格与红线距离过大，取消做多信号")
            kaiduo = False
            
        if (confirm_short or pullback_short_good or transition_short) and not white_line_distance_ok:
            print("因价格与白线距离过大，取消做空信号")
            kaikong = False
        
        # 开仓逻辑
        if g.position == "none":
            # 获取当前K线时间戳，用于计算开仓间隔
            current_bar_time = timetag if isinstance(timetag, (int, float)) else time.time()
            
            # 开多仓 - 添加开盘禁止交易条件
            if kaiduo and can_open_position and not is_day_open_no_trade and not is_night_open_no_trade and (current_bar_time - g.trace_time_long) > Config.SLEEP_TIME*60 and t not in g.opened_t:
                # 确定仓位级别
                g.position_level = determine_position_level(ContextInfo, g.code, current_price)
                
                # 先更新开仓时间戳，避免持仓时间计算错误
                g.trace_time_long = time.time()
                print(f"设置多头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_long))}")
                
                if Config.ENTRY_PRICE_OPTIMIZE_ENABLE:
                    # 等待最优开仓价格
                    optimal_price = wait_for_optimal_entry(ContextInfo, "long", current_price, Config.ENTRY_WAIT_SECONDS)
                    
                    # 使用限价单，价格略低于计算出的最优价格，确保成交
                    order_price = optimal_price * 0.9995  # 从0.9999改为0.9995，更容易成交
                    print(f"执行开多: 优化后价格={order_price:.2f} (原价格{current_price:.2f})")
                    passorder(0, 1101, account, g.code, 12, order_price, Config.TRADE_HANDS, '', 1, '期货策略限价开多', ContextInfo)
                else:
                    # 使用市价单
                    passorder(0, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '期货策略开多', ContextInfo)
                
                # 更新状态变量
                g.position = "long"
                g.buy_long += 1
                g.opened_t.append(t)
                g.hold_code = g.code
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                g.is_reverse_trade = False
                # 不要在这里赋值g.open_price和g.hold_price，等deal_callback回报后再赋值
                # g.hold_price = optimal_price if Config.ENTRY_PRICE_OPTIMIZE_ENABLE else current_price
                # g.open_price = optimal_price if Config.ENTRY_PRICE_OPTIMIZE_ENABLE else current_price
                # 重置止盈标记
                g.profit_taken_1 = False
                g.profit_taken_2 = False
                g.profit_taken_3 = False
                log_trade("开多" + ("(限价单)" if Config.ENTRY_PRICE_OPTIMIZE_ENABLE else ""), 
                         order_price if Config.ENTRY_PRICE_OPTIMIZE_ENABLE else current_price)
                if transition_long and kaiduo and can_open_position:
                    g.position_source = 'transition'
            
            # 开空仓 - 添加开盘禁止交易条件
            elif kaikong and can_open_position and not is_day_open_no_trade and not is_night_open_no_trade and (current_bar_time - g.trace_time_short) > Config.SLEEP_TIME*60 and t not in g.opened_t:
                # 确定仓位级别
                g.position_level = determine_position_level(ContextInfo, g.code, current_price)
                
                # 先更新开仓时间戳，避免持仓时间计算错误
                g.trace_time_short = time.time()
                print(f"设置空头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_short))}")
                
                if Config.ENTRY_PRICE_OPTIMIZE_ENABLE:
                    # 等待最优开仓价格
                    optimal_price = wait_for_optimal_entry(ContextInfo, "short", current_price, Config.ENTRY_WAIT_SECONDS)
                    
                    # 使用限价单，价格略高于计算出的最优价格，确保成交
                    order_price = optimal_price * 1.0001
                    print(f"执行开空: 优化后价格={order_price:.2f} (原价格{current_price:.2f})")
                    passorder(3, 1101, account, g.code, 12, order_price, Config.TRADE_HANDS, '', 1, '期货策略限价开空', ContextInfo)
                else:
                    # 使用市价单
                    passorder(3, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '期货策略开空', ContextInfo)
                
                # 更新状态变量
                g.position = "short"
                g.buy_short += 1
                g.opened_t.append(t)
                g.hold_code = g.code
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                g.is_reverse_trade = False
                # 不要在这里赋值g.open_price和g.hold_price，等deal_callback回报后再赋值
                # g.hold_price = optimal_price if Config.ENTRY_PRICE_OPTIMIZE_ENABLE else current_price
                # g.open_price = optimal_price if Config.ENTRY_PRICE_OPTIMIZE_ENABLE else current_price
                # 重置止盈标记
                g.profit_taken_1_short = False
                g.profit_taken_2_short = False
                g.profit_taken_3_short = False
                log_trade("开空" + ("(限价单)" if Config.ENTRY_PRICE_OPTIMIZE_ENABLE else ""), 
                         order_price if Config.ENTRY_PRICE_OPTIMIZE_ENABLE else current_price)
                if transition_short and kaikong and can_open_position:
                    g.position_source = 'transition'
        
        # 持仓管理
        elif g.position == "long":
            # 根据是否为反向开仓选择不同的止盈逻辑
            if g.is_reverse_trade:
                executed_take_profit = execute_reverse_take_profit(ContextInfo, current_price)
                if not executed_take_profit:
                    check_reverse_stop_loss(ContextInfo, current_price)
            else:
                # 原有止盈逻辑
                executed_take_profit = execute_take_profit(ContextInfo, current_price)
                
                # 如果没有执行止盈，检查止损条件
                if not executed_take_profit:
                    # 检查是否跌破红线
                    if break_red:
                        # 平多仓
                        passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单止损', ContextInfo)
                        log_trade("多单止损", current_price)
                        
                        # 更新持仓状态
                        g.position = "none"
                        g.position_size = 0
                        
                        # 记录平仓时间
                        g.last_close_time = time.time()
        
        elif g.position == "short":
            # 止盈逻辑
            executed_take_profit = execute_take_profit(ContextInfo, current_price)
            
            # 如果没有执行止盈，检查止损条件
            if not executed_take_profit:
                # 检查是否突破白线
                if first_break_white:
                    # 平空仓
                    passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '空单止损', ContextInfo)
                    log_trade("空单止损", current_price)
                    
                    # 更新持仓状态
                    g.position = "none"
                    g.position_size = 0
                    
                    # 记录平仓时间
                    g.last_close_time = time.time()
    
    except Exception as e:
        print(f"处理K线数据出错: {e}")
        return
    
    # 检查止损和反向开仓 - 只要有持仓就执行检查
    if g.position != "none":
        check_stop_loss_and_reverse(ContextInfo, current_price)
        # 使用最新价格检查快速止损条件
        quick_stop_loss_check(ContextInfo, current_price)
        # 添加实时止损监控
        real_time_stop_loss_monitor(ContextInfo, current_price)

def check_stop_loss(ContextInfo, current_price):
    """检查是否触发止损 - 简化版，仅使用红白线系统"""
    # --- 新增：开仓价健壮性检查 ---
    fix_open_price(ContextInfo)
    # --- 原有止损逻辑 ---
    # 获取当前时间
    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    try:
        t = time.strftime("%H%M%S", time.localtime(timetag))
    except (TypeError, ValueError, OSError):
        if isinstance(timetag, str):
            t = timetag[-6:] if len(timetag) >= 6 else "000000"
        else:
            t = time.strftime("%H%M%S")
    
    # 获取5分钟K线数据
    try:
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='5m',
                                           count=10)
        
        # 使用文华指标源代码计算红白线
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
    except Exception as e:
        print(f"获取5分钟K线数据出错: {str(e)}")
        return False
    
    if g.position == "long":
        # 做多止损条件：5分钟K线收盘价跌破5分钟红色支撑线
        red_line_broken = False
        
        # 检查是否跌破红线 - 情况1：红线消失
        if red_line_5m.iloc[-2] is not None and not pd.isna(red_line_5m.iloc[-2]) and (red_line_5m.iloc[-1] is None or pd.isna(red_line_5m.iloc[-1])):
            if price_5m['close'].iloc[-1] < red_line_5m.iloc[-2]:
                red_line_broken = True
                print("多单止损信号：检测到跌破红线支撑（红线消失）")
        
        # 检查是否跌破红线 - 情况2：价格低于红线
        elif red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            if price_5m['close'].iloc[-1] < red_line_5m.iloc[-1]:
                red_line_broken = True
                print(f"多单止损信号：检测到跌破红线支撑（价格{price_5m['close'].iloc[-1]}低于红线{red_line_5m.iloc[-1]}）")
        
        if red_line_broken:
            # 计算当前盈亏
            profit_pct = calculate_profit(current_price, g.open_price, True)
            
            # 执行止损
            passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单红线止损', ContextInfo)
            log_trade("多单红线止损", current_price, profit_pct)
            g.position = "none"
            g.position_size = 0
            return True
    
    elif g.position == "short":
        # 做空止损条件：5分钟K线收盘价突破白色压力线
        white_line_broken = False
        
        # 检查是否突破白线 - 情况1：白线消失
        if white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]) and (white_line_5m.iloc[-1] is None or pd.isna(white_line_5m.iloc[-1])):
            if price_5m['close'].iloc[-1] > white_line_5m.iloc[-2]:
                white_line_broken = True
                print("空单止损信号：检测到突破白线压力（白线消失）")
        
        # 检查是否突破白线 - 情况2：价格高于白线
        elif white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
            if price_5m['close'].iloc[-1] > white_line_5m.iloc[-1]:
                white_line_broken = True
                print(f"空单止损信号：检测到突破白线压力（价格{price_5m['close'].iloc[-1]}高于白线{white_line_5m.iloc[-1]}）")
        
        if white_line_broken:
            # 计算当前盈亏
            profit_pct = calculate_profit(current_price, g.open_price, False)
            
            # 执行止损
            passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '空单白线止损', ContextInfo)
            log_trade("空单白线止损", current_price, profit_pct)
            g.position = "none"
            g.position_size = 0
            return True
    
    return False

def execute_take_profit(ContextInfo, current_price):
    """
    执行分批止盈策略
    
    参数:
    ContextInfo: 上下文信息
    current_price: 当前价格
    
    返回:
    True: 执行了止盈
    False: 未执行止盈
    """
    # --- 新增：开仓价健壮性检查 ---
    fix_open_price(ContextInfo)
    # --- 原有止盈逻辑 ---
    # 多单止盈逻辑
    if g.position == "long":
        # 计算盈利百分比
        profit_pct = calculate_profit(current_price, g.open_price, True)
        
        # 计算初始总手数
        total_hands = Config.TRADE_HANDS
        
        # 第一批止盈（30%仓位）- 盈利0.2%
        if not g.profit_taken_1 and profit_pct >= Config.TAKE_PROFIT_1:
            # 简化手数计算，直接使用百分比乘以总手数
            first_batch = max(1, int(total_hands * Config.PROFIT_PCT_1))
            
            # 确保不超过当前持仓
            first_batch = min(first_batch, g.position_size)
            
            if first_batch > 0:
                if g.future_account and g.future_code:
                    g.future_account.sell_to_close(g.future_code, current_price, first_batch)
                else:
                    print("[警告] future_account或future_code未初始化，自动回退为passorder平仓！")
                    passorder(7, 1101, account, g.code, 14, 0, first_batch, '', 1, '多单首批止盈', ContextInfo)
                g.profit_taken_1 = True
                g.position_size -= first_batch
                log_trade("多单首批止盈(30%)", current_price, profit_pct)
                return True
            
        # 第二批止盈（30%仓位）- 盈利0.4%
        elif g.profit_taken_1 and not g.profit_taken_2 and profit_pct >= Config.TAKE_PROFIT_2:
            # 简化手数计算，直接使用百分比乘以总手数
            second_batch = max(1, int(total_hands * Config.PROFIT_PCT_2))
            
            # 确保不超过当前持仓
            second_batch = min(second_batch, g.position_size)
            
            if second_batch > 0:
                if g.future_account and g.future_code:
                    g.future_account.sell_to_close(g.future_code, current_price, second_batch)
                else:
                    print("[警告] future_account或future_code未初始化，自动回退为passorder平仓！")
                    passorder(7, 1101, account, g.code, 14, 0, second_batch, '', 1, '多单第二批止盈', ContextInfo)
                g.profit_taken_2 = True
                g.position_size -= second_batch
                log_trade("多单第二批止盈(30%)", current_price, profit_pct)
                return True
            
        # 第三批止盈（40%仓位）- 盈利0.6%
        elif g.profit_taken_1 and g.profit_taken_2 and not g.profit_taken_3 and profit_pct >= Config.TAKE_PROFIT_3:
            # 剩余全部手数
            third_batch = g.position_size
            
            if third_batch > 0:
                if g.future_account and g.future_code:
                    g.future_account.sell_to_close(g.future_code, current_price, third_batch)
                else:
                    print("[警告] future_account或future_code未初始化，自动回退为passorder平仓！")
                    passorder(7, 1101, account, g.code, 14, 0, third_batch, '', 1, '多单第三批止盈', ContextInfo)
                g.profit_taken_3 = True
                g.position_size = 0
                g.position = "none"
                log_trade("多单第三批止盈(40%)", current_price, profit_pct)
                return True
    
    # 空单止盈逻辑
    elif g.position == "short":
        # 计算盈利百分比
        profit_pct = calculate_profit(current_price, g.open_price, False)
        
        # 计算初始总手数
        total_hands = Config.TRADE_HANDS
        
        # 第一批止盈（30%仓位）- 盈利0.2%
        if not g.profit_taken_1_short and profit_pct >= Config.TAKE_PROFIT_1:
            # 简化手数计算，直接使用百分比乘以总手数
            first_batch = max(1, int(total_hands * Config.PROFIT_PCT_1))
            
            # 确保不超过当前持仓
            first_batch = min(first_batch, g.position_size)
            
            if first_batch > 0:
                if g.future_account and g.future_code:
                    g.future_account.buy_to_close(g.future_code, current_price, first_batch)
                else:
                    print("[警告] future_account或future_code未初始化，自动回退为passorder平仓！")
                    passorder(9, 1101, account, g.code, 14, 0, first_batch, '', 1, '空单首批止盈', ContextInfo)
                g.profit_taken_1_short = True
                g.position_size -= first_batch
                log_trade("空单首批止盈(30%)", current_price, profit_pct)
                return True
            
        # 第二批止盈（30%仓位）- 盈利0.4%
        elif g.profit_taken_1_short and not g.profit_taken_2_short and profit_pct >= Config.TAKE_PROFIT_2:
            # 简化手数计算，直接使用百分比乘以总手数
            second_batch = max(1, int(total_hands * Config.PROFIT_PCT_2))
            
            # 确保不超过当前持仓
            second_batch = min(second_batch, g.position_size)
            
            if second_batch > 0:
                if g.future_account and g.future_code:
                    g.future_account.buy_to_close(g.future_code, current_price, second_batch)
                else:
                    print("[警告] future_account或future_code未初始化，自动回退为passorder平仓！")
                    passorder(9, 1101, account, g.code, 14, 0, second_batch, '', 1, '空单第二批止盈', ContextInfo)
                g.profit_taken_2_short = True
                g.position_size -= second_batch
                log_trade("空单第二批止盈(30%)", current_price, profit_pct)
                return True
            
        # 第三批止盈（40%仓位）- 盈利0.6%
        elif g.profit_taken_1_short and g.profit_taken_2_short and not g.profit_taken_3_short and profit_pct >= Config.TAKE_PROFIT_3:
            # 剩余全部手数
            third_batch = g.position_size
            
            if third_batch > 0:
                if g.future_account and g.future_code:
                    g.future_account.buy_to_close(g.future_code, current_price, third_batch)
                else:
                    print("[警告] future_account或future_code未初始化，自动回退为passorder平仓！")
                    passorder(9, 1101, account, g.code, 14, 0, third_batch, '', 1, '空单第三批止盈', ContextInfo)
                g.profit_taken_3_short = True
                g.position_size = 0
                g.position = "none"
                log_trade("空单第三批止盈(40%)", current_price, profit_pct)
                return True
    
    return False

def check_volume_increase(volume_data, threshold_base=1.2):
    """
    检查成交量是否增加
    
    参数:
    volume_data: 成交量数据
    threshold_base: 基础阈值倍数
    
    返回:
    True: 成交量增加
    False: 成交量未增加
    """
    try:
        # 使用品种特定的阈值
        threshold = g.volume_threshold if hasattr(g, 'volume_threshold') else threshold_base
        
        # 确保数据足够
        if len(volume_data) < 2:
            return False
            
        # 计算当前成交量与前一根K线成交量的比值
        volume_ratio = volume_data.iloc[-1] / volume_data.iloc[-2]
        
        # 返回是否超过阈值
        return volume_ratio > threshold
    except Exception as e:
        print(f"检查成交量增加出错: {e}")
        return False

def check_trend(price_data, ma_value, lookback=5):
    """
    检查价格趋势 - 优化版
    
    参数:
    price_data: 价格数据
    ma_value: 均线值
    lookback: 回溯周期
    
    返回:
    1: 上升趋势
    0: 无明显趋势
    -1: 下降趋势
    """
    # 计算短期趋势
    short_trend = price_data['close'].iloc[-1] - price_data['close'].iloc[-3]
    
    # 计算价格相对均线位置
    price_vs_ma = price_data['close'].iloc[-1] - ma_value
    
    # 判断趋势
    if short_trend > 0 and price_vs_ma > 0:
        return 1  # 上升趋势
    elif short_trend < 0 and price_vs_ma < 0:
        return -1  # 下降趋势
    
    # 如果短期趋势和均线位置不一致，以短期趋势为准
    if abs(short_trend) > abs(price_vs_ma) * 0.01:  # 短期趋势明显
        return 1 if short_trend > 0 else -1
        
    return 0  # 无明显趋势

def should_reverse_trade(ContextInfo, last_position_type, current_price):
    """
    评估是否应该进行反向交易
    
    参数:
    ContextInfo: 上下文信息
    last_position_type: 上一个持仓类型 ("long" 或 "short")
    current_price: 当前价格
    
    返回:
    (should_reverse, reverse_type, confidence_score)
    """
    # 检查距离上次平仓的时间
    time_since_close = time.time() - g.last_close_time
    min_wait_seconds = 5 * 60  # 最少等待5分钟
    max_wait_seconds = 30 * 60  # 最多等待30分钟
    
    if time_since_close < min_wait_seconds:
        print(f"反向交易评估: 距离上次平仓时间不足5分钟，暂不考虑反向")
        return (False, None, 0)
    
    if time_since_close > max_wait_seconds:
        print(f"反向交易评估: 距离上次平仓时间超过30分钟，不再考虑反向")
        return (False, None, 0)
    
    # 获取价格数据
    price_5m = ContextInfo.get_market_data(['close','high','low','open','volume'], 
                                       [g.code], 
                                       period='5m',
                                       count=60)
    
    # 计算技术指标
    MA_Line = calculate_ma(price_5m['close'], price_5m['volume'], 55)
    white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
    
    # 计算RSI
    delta = price_5m['close'].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window=9).mean()
    avg_loss = loss.rolling(window=9).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))
    current_rsi = rsi.iloc[-1]
    
    # 初始化信心分数
    confidence_score = 0
    
    # 根据上一个持仓类型决定反向交易方向
    if last_position_type == "long":
        # 考虑做空
        reverse_type = "short"
        
        # 核心条件 - 价格在MA线下方
        if current_price < MA_Line.iloc[-1]:
            confidence_score += 30
            print(f"反向交易评估(空): 价格在MA线下方")
        
        # 白线压力确认
        if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]) and current_price < white_line_5m.iloc[-1]:
            confidence_score += 30
            print("反向交易评估(空): 白线压力确认")
        
        # RSI超买检查
        if current_rsi > 70:
            confidence_score += 20
            print(f"反向交易评估(空): RSI超买 ({current_rsi:.1f})")
        
        # 跌破红线
        first_break_red = False
        if len(red_line_5m) > 1 and red_line_5m.iloc[-2] is not None and not pd.isna(red_line_5m.iloc[-2]):  # 前一根K线有红线
            # 情况1：标准检测 - 前一根K线有红线，当前K线红线消失
            if (red_line_5m.iloc[-1] is None or pd.isna(red_line_5m.iloc[-1])) and price_5m['close'].iloc[-1] < red_line_5m.iloc[-2]:
                first_break_red = True
                print(f"检测到标准跌破红线信号: 价格{price_5m['close'].iloc[-1]}跌破红线{red_line_5m.iloc[-2]}")
                confidence_score += 20
            
            # 情况2：强力跌破 - 即使红线没消失，但价格大幅低于红线
            elif red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
                # 价格低于红线1%以上视为强力跌破
                price_below_red = (red_line_5m.iloc[-1] - price_5m['close'].iloc[-1]) / red_line_5m.iloc[-1] > 0.01
                if price_below_red:
                    first_break_red = True
                    print(f"检测到强力跌破红线信号: 价格{price_5m['close'].iloc[-1]}大幅低于红线{red_line_5m.iloc[-1]}")
                    confidence_score += 20
    
    elif last_position_type == "short":
        # 考虑做多
        reverse_type = "long"
        
        # 核心条件 - 价格在MA线上方
        if current_price > MA_Line.iloc[-1]:
            confidence_score += 30
            print(f"反向交易评估(多): 价格在MA线上方")
        
        # 红线支撑确认
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]) and current_price > red_line_5m.iloc[-1]:
            confidence_score += 30
            print("反向交易评估(多): 红线支撑确认")
        
        # RSI超卖检查
        if current_rsi < 30:
            confidence_score += 20
            print(f"反向交易评估(多): RSI超卖 ({current_rsi:.1f})")
        
        # 突破白线
        first_break_white = False
        if len(white_line_5m) > 1 and white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]):
            if (white_line_5m.iloc[-1] is None or pd.isna(white_line_5m.iloc[-1])) and current_price > white_line_5m.iloc[-2]:
                first_break_white = True
                confidence_score += 20
                print("反向交易评估(多): 突破白线")
    
    else:
        # 无法确定上一个持仓类型
        return (False, None, 0)
    
    # 增加趋势确认条件
    price_30m = ContextInfo.get_market_data(['close','high','low','open','volume'], 
                                       [g.code], 
                                       period='30m',
                                       count=60)
    
    # 计算30分钟趋势
    trend_30m = check_trend(price_30m, MA_Line.iloc[-1])
    
    # 趋势一致性检查
    if reverse_type == "long" and trend_30m == 1:
        confidence_score += 20
        print("反向交易评估(多): 30分钟趋势向上")
    elif reverse_type == "short" and trend_30m == -1:
        confidence_score += 20
        print("反向交易评估(空): 30分钟趋势向下")
    
    # 信心分数阈值
    should_reverse = confidence_score >= 60
    
    # 记录最终决策
    print(f"反向交易评估: 最终信心分数 {confidence_score}/100, {'执行' if should_reverse else '不执行'}反向{reverse_type}交易")
    
    return (should_reverse, reverse_type, confidence_score)

def get_future_positions(ContextInfo, accountid):
    """
    获取期货持仓信息
    
    参数:
    ContextInfo: 上下文信息
    accountid: 账户ID
    
    返回:
    持仓字典，格式为 {(合约代码, 方向): 持仓量}
    """
    positions = get_trade_detail_data(accountid, 'FUTURE', 'POSITION')
    hold_dict = {}
    for p in positions:
        market = p.m_strExchangeID
        market = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(p.m_strExchangeID, p.m_strExchangeID)

        code = p.m_strInstrumentID +'.'+market
        direction = p.m_nDirection
        volume = p.m_nVolume
        key = (code, direction)
        hold_dict[key] = hold_dict.get(key, 0)+volume
    return hold_dict
    

def order_callback(ContextInfo, orderInfo):
    """订单状态回调函数"""
    print(orderInfo.m_strRemark, orderInfo.m_nOrderStatus, orderInfo.m_nOffsetFlag, orderInfo.m_dTradedPrice)
    
    # 处理限价单超时逻辑
    if orderInfo.m_strRemark in ['期货策略限价开多', '期货策略限价开空', 'ATR止损反向开多', 'ATR止损反向开空'] and orderInfo.m_nOrderStatus == 48:  # 48是未成交状态
        # 检查订单是否超过设定的等待时间
        current_time = time.time()
        order_time = orderInfo.m_createtime if hasattr(orderInfo, 'm_createtime') else current_time - 60  # 默认一分钟前
        
        # 如果超过等待时间（例如3分钟），则撤单并不再补市价单
        if current_time - order_time > Config.PRICE_ORDER_TIMEOUT * 60:
            print(f"限价单等待超时，撤销订单，不再补市价单: {orderInfo.m_strRemark}")
            # 撤销原订单
            ContextInfo.cancel_order(orderInfo.m_strOrderSysID)
    
    # 原有代码保持不变
    # ... 其余代码 ...

def orderError_callback(ContextInfo, passOrderInfo, msg):
    """
    订单错误回调函数 - 处理订单错误
    
    参数:
    ContextInfo: 上下文信息
    passOrderInfo: 订单信息
    msg: 错误信息
    """
    if '期货策略' not in passOrderInfo.strategyName:
        return
        
    g.hold = 0
    print("orderError_callback set 0", msg)
    
    if '期货策略开空' in passOrderInfo.strategyName:
        g.buy_short += 1
        print(f"{g.code} 开空次数+1 {g.buy_short}")
    
    if '期货策略开多' in passOrderInfo.strategyName:
        g.buy_long += 1
        print(f"{g.code} 开多次数+1 {g.buy_long}")


def deal_callback(ContextInfo, dealInfo):
    """
    成交回报回调函数 - 处理成交信息
    
    参数:
    ContextInfo: 上下文信息
    dealInfo: 成交信息
    """
    current_time = time.time()
    print(f"\n【成交回报】 系统时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))}")
    
    # 处理订单系统ID
    if g.sysid:
        if dealInfo.m_strOrderSysID in g.sysid:  # 如果是order_callback先收到
            g.open_price, g.hold_price = round(dealInfo.m_dPrice, 1), round(dealInfo.m_dPrice, 1)
            print(f"[赋值] 成交回报赋值g.open_price: {g.open_price}")
        else:
            g.sysid[dealInfo.m_strOrderSysID] = [round(dealInfo.m_dPrice, 1), round(dealInfo.m_dPrice, 1)]
    else:  # 如果是deal_callback先收到
        g.sysid[dealInfo.m_strOrderSysID] = [round(dealInfo.m_dPrice, 1), round(dealInfo.m_dPrice, 1)]

    print(f"成交回报详情: m_nOffsetFlag:[{dealInfo.m_nOffsetFlag}] m_strRemark:[{dealInfo.m_strRemark}], [{dealInfo.m_strInstrumentID}] [{dealInfo.m_strExchangeID}] [{dealInfo.m_dPrice}]")
    
    # 处理普通开仓回报(包括市价单和限价单)
    if dealInfo.m_strRemark in ['期货策略开多','期货策略开空','期货策略限价开多','期货策略限价开空']:
        market = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
        k = dealInfo.m_strInstrumentID+'.'+market
        
        if dealInfo.m_nOffsetFlag == 48 and g.code.find(dealInfo.m_strInstrumentID)>=0:
            print(f"处理普通开仓成交回报: {dealInfo.m_strRemark}, 价格={dealInfo.m_dPrice}")
            g.open_price = round(dealInfo.m_dPrice, 1)
            g.hold_price = round(dealInfo.m_dPrice, 1)
            
            # 记录开仓前的trace_time状态，用于调试
            if dealInfo.m_strRemark in ['期货策略开多', '期货策略限价开多']:
                old_trace_time = getattr(g, 'trace_time_long', 0)
                print(f"多头开仓前trace_time_long: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(old_trace_time)) if old_trace_time > 0 else '未设置'}")
            else:
                old_trace_time = getattr(g, 'trace_time_short', 0)
                print(f"空头开仓前trace_time_short: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(old_trace_time)) if old_trace_time > 0 else '未设置'}")
            
            # 更新持仓状态 - 处理市价单和限价单
            if dealInfo.m_strRemark in ['期货策略开多', '期货策略限价开多']:
                g.position = "long"
                g.position_size = Config.TRADE_HANDS
                g.is_reverse_trade = False  # 确保不是反向开仓
                # 更新多头开仓时间
                g.trace_time_long = time.time()
                print(f"更新多头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_long))}")
                # 重置止盈标记
                g.profit_taken_1 = False
                g.profit_taken_2 = False
                g.profit_taken_3 = False
                
            elif dealInfo.m_strRemark in ['期货策略开空', '期货策略限价开空']:
                g.position = "short"
                g.position_size = Config.TRADE_HANDS
                g.is_reverse_trade = False  # 确保不是反向开仓
                # 更新空头开仓时间
                g.trace_time_short = time.time()
                print(f"更新空头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_short))}")
                # 重置止盈标记
                g.profit_taken_1_short = False
                g.profit_taken_2_short = False
                g.profit_taken_3_short = False
    
    # 处理ATR止损反向开仓回报
    elif dealInfo.m_strRemark in ['ATR止损反向开多', 'ATR止损反向开空', 'ATR止损反手开多', 'ATR止损反手开空']:
        market = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
        k = dealInfo.m_strInstrumentID+'.'+market
        
        if dealInfo.m_nOffsetFlag == 48 and g.code.find(dealInfo.m_strInstrumentID)>=0:
            print(f"处理ATR止损反向开仓成交回报: {dealInfo.m_strRemark}, 价格={dealInfo.m_dPrice}")
            g.open_price = round(dealInfo.m_dPrice, 1)
            g.hold_price = round(dealInfo.m_dPrice, 1)
            
            # 记录开仓前的trace_time状态，用于调试
            if dealInfo.m_strRemark in ['ATR止损反向开多', 'ATR止损反手开多']:
                old_trace_time = getattr(g, 'trace_time_long', 0)
                print(f"反向多头开仓前trace_time_long: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(old_trace_time)) if old_trace_time > 0 else '未设置'}")
            else:
                old_trace_time = getattr(g, 'trace_time_short', 0)
                print(f"反向空头开仓前trace_time_short: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(old_trace_time)) if old_trace_time > 0 else '未设置'}")
            
            # 更新持仓状态
            if dealInfo.m_strRemark in ['ATR止损反向开多', 'ATR止损反手开多']:
                g.position = "long"
                g.position_size = Config.REVERSE_TRADE_HANDS
                g.is_reverse_trade = True  # 标记为反向开仓
                # 更新多头开仓时间
                g.trace_time_long = time.time()
                print(f"更新反向多头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_long))}")
                # 重置止盈标记
                g.profit_taken_1 = False
                g.profit_taken_2 = False
                g.profit_taken_3 = False
                # 确保空单止盈标记也被重置
                g.profit_taken_1_short = False
                g.profit_taken_2_short = False
                g.profit_taken_3_short = False
                
            elif dealInfo.m_strRemark in ['ATR止损反向开空', 'ATR止损反手开空']:
                g.position = "short"
                g.position_size = Config.REVERSE_TRADE_HANDS
                g.is_reverse_trade = True  # 标记为反向开仓
                # 更新空头开仓时间
                g.trace_time_short = time.time()
                print(f"更新反向空头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_short))}")
                # 重置止盈标记
                g.profit_taken_1_short = False
                g.profit_taken_2_short = False
                g.profit_taken_3_short = False
                # 确保多单止盈标记也被重置
                g.profit_taken_1 = False
                g.profit_taken_2 = False
                g.profit_taken_3 = False

# ====== 技术指标计算函数 ======
def REF(S, N=1):
    """
    对序列整体下移动N,返回序列(shift后会产生NAN)
    
    参数:
    S: 输入序列
    N: 移动周期数
    
    返回:
    移动后的序列
    """
    return pd.Series(S).shift(N).values

def SMA(S, N, M=1):
    """
    中国式的SMA,至少需要120周期才精确 (雪球180周期)
    
    参数:
    S: 输入序列
    N: 周期数
    M: 权重因子
    
    返回:
    SMA序列
    """
    alpha = 1/(1+N-M/M)  # 等价于 alpha=M/N
    return pd.Series(S).ewm(alpha=M/N, adjust=False).mean().values

def SUM(S, N):
    """
    对序列求N天累计和，返回序列
    N=0对序列所有依次求和
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    累计和序列
    """
    return pd.Series(S).rolling(N).sum().values if N>0 else pd.Series(S).cumsum().values

def HHV(S, N):
    """
    求N周期内的最高值
    例如: HHV(C, 5) 最近5天收盘最高价
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最高值序列
    """
    return pd.Series(S).rolling(N).max().values

def LLV(S, N):
    """
    求N周期内的最低值
    例如: LLV(C, 5) 最近5天收盘最低价
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最低值序列
    """
    return pd.Series(S).rolling(N).min().values

def HHVBARS(S, N):
    """
    求N周期内S最高值到当前周期数, 返回序列
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最高值位置序列
    """
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]), raw=True).values

def LLVBARS(S, N):
    """
    求N周期内S最低值到当前周期数, 返回序列
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最低值位置序列
    """
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]), raw=True).values

def MA(S, N):
    """
    求序列的N日简单移动平均值，返回序列
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    移动平均序列
    """
    return pd.Series(S).rolling(N).mean().values

def EMA(source, N:int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    
    参数:
    source: 输入序列
    N: 周期数
    result_type: 返回类型，'np'表示numpy数组
    
    返回:
    EMA序列
    """
    M = 2
    if N<M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N-1
    M = 2
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M*x+d*temp)/(N+1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
    """
    Python实现的LLV函数，适用于pandas Series
    
    参数:
    S: 输入Series
    N: 周期数
    
    返回:
    最低值Series
    """
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i+1-N):i+1]
        result_list.append(min(l))
    return pd.Series(result_list, index=index)

def PyHHV(S, N):
    """
    Python实现的HHV函数，适用于pandas Series
    
    参数:
    S: 输入Series
    N: 周期数
    
    返回:
    最高值Series
    """
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i+1-N):i+1]
        result_list.append(max(l))
    return pd.Series(result_list, index=index)

def after_init(ContextInfo):
    """
    在初始化后执行，用于获取交易日历信息
    
    参数:
    ContextInfo: 上下文信息
    
    返回:
    交易日期列表
    """
    # 修改最大起始时间为********，防止超出权限
    return ContextInfo.get_trading_dates(g.market,'','********',count=2, period='1d')

def sync_position_status(ContextInfo):
    """同步持仓状态，确保g.position与实际持仓一致"""
    positions = get_future_positions(ContextInfo, account)
    
    # 检查是否有多头持仓
    has_long_position = False
    long_volume = 0
    for (code, direction), volume in positions.items():
        if code == g.code and direction == 48 and volume > 0:
            has_long_position = True
            long_volume = volume
            break
    
    # 检查是否有空头持仓
    has_short_position = False
    short_volume = 0
    for (code, direction), volume in positions.items():
        if code == g.code and direction == 49 and volume > 0:
            has_short_position = True
            short_volume = volume
            break
    
    # 更新系统状态
    if has_long_position:
        if g.position != "long":
            log_trade("同步状态：检测到多头持仓但系统状态不一致，已更新", 0)
        g.position = "long"
        g.position_size = long_volume
        # ====== 自动同步开仓价 ======
        if g.open_price == 0:
            fix_open_price(ContextInfo)
    elif has_short_position:
        if g.position != "short":
            log_trade("同步状态：检测到空头持仓但系统状态不一致，已更新", 0)
        g.position = "short"
        g.position_size = short_volume
        # ====== 自动同步开仓价 ======
        if g.open_price == 0:
            fix_open_price(ContextInfo)
    else:
        if g.position != "none":
            log_trade("同步状态：没有检测到持仓但系统状态不一致，已更新", 0)
        g.position = "none"
        g.position_size = 0

def determine_position_level(ContextInfo, code, current_price):
    """
    根据当前价格确定仓位级别
    
    参数:
    ContextInfo: 上下文信息
    code: 合约代码
    current_price: 当前价格
    
    返回:
    仓位级别
    """
    # 实现仓位级别判断逻辑
    # 这里可以根据实际需求实现不同的判断逻辑
    # 例如，可以根据价格波动幅度、成交量等因素来确定仓位级别
    return 1  # 临时返回值，需要根据实际逻辑实现



def calculate_atr(ContextInfo, period=14):  # 使用标准14天周期
    price_data = ContextInfo.get_market_data(['high', 'low', 'close', 'open'], 
                                         [g.code], 
                                         period='1d',
                                         count=period+10)
    
    # 计算真实波动幅度
    tr1 = abs(price_data['high'] - price_data['low'])
    tr2 = abs(price_data['high'] - price_data['close'].shift(1))
    tr3 = abs(price_data['low'] - price_data['close'].shift(1))
    
    # 取三者最大值
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算ATR
    atr = tr.rolling(window=period).mean().iloc[-1]
    return atr

def check_pullback_ratio(price_data, line_value, is_support=True):
    """检查回调幅度是否合理"""
    # 使用配置参数确定回溯K线数
    lookback = Config.PULLBACK_LOOKBACK
    
    high = price_data['high'].iloc[-lookback:].max()
    low = price_data['low'].iloc[-lookback:].min()
    current = price_data['close'].iloc[-1]
    
    if is_support:  # 回调至支撑位
        pullback_ratio = (high - current) / (high - low)
        # 使用配置参数设定理想区间
        return Config.PULLBACK_RATIO_MIN <= pullback_ratio <= Config.PULLBACK_RATIO_MAX, pullback_ratio
    else:  # 反弹至压力位
        pullback_ratio = (current - low) / (high - low)
        # 使用配置参数设定理想区间
        return Config.PULLBACK_RATIO_MIN <= pullback_ratio <= Config.PULLBACK_RATIO_MAX, pullback_ratio

def check_downtrend_volume(volume, close):
    """
    专门针对下跌行情的成交量检查
    
    参数:
    volume: 成交量序列
    close: 收盘价序列
    
    返回:
    True: 成交量符合下跌行情特征
    False: 成交量不符合下跌行情特征
    """
    # 计算价格变化
    price_change = close.pct_change()
    
    # 下跌确认
    is_declining = close.iloc[-1] < close.iloc[-3]
    
    # 情况1: 放量下跌 - 传统意义上的看空信号
    volume_increase = volume.iloc[-1] > volume.iloc[-2] * 1.2
    
    # 情况2: 缩量下跌 - 买盘撤离导致的下跌
    volume_decrease = volume.iloc[-1] < volume.iloc[-5:].mean()
    price_decline = price_change.iloc[-1] < -0.002  # 价格下跌超过0.2%
    
    # 情况3: 连续下跌 - 持续的下跌趋势
    continuous_decline = all(price_change.iloc[-3:] < 0)
    
    # 综合判断
    return is_declining and (volume_increase or (volume_decrease and price_decline) or continuous_decline)

def detect_line_transition(prev_white, curr_white, prev_red, curr_red):
    """
    检测红白线转换
    
    参数:
    prev_white: 前一根K线的白线
    curr_white: 当前K线的白线
    prev_red: 前一根K线的红线
    curr_red: 当前K线的红线
    
    返回:
    1: 白线转红线
    -1: 红线转白线
    0: 无转换
    """
    try:
        print("\n=== 红白线转换检测 ===")
        print(f"前一根K线白线: {prev_white}")
        print(f"当前K线白线: {curr_white}")
        print(f"前一根K线红线: {prev_red}")
        print(f"当前K线红线: {curr_red}")
        
        # 修复: 为了避免None和NaN值导致字符串格式化问题，使用安全的格式化方法
        prev_white_str = str(prev_white)
        curr_white_str = str(curr_white)
        prev_red_str = str(prev_red)
        curr_red_str = str(curr_red)
        
        print(f"转换检测详情: 前白={prev_white_str}, 当白={curr_white_str}, 前红={prev_red_str}, 当红={curr_red_str}")
        
        # 白线转红线
        if (prev_white is not None and not pd.isna(prev_white) and 
            (curr_red is not None and not pd.isna(curr_red)) and 
            (curr_white is None or pd.isna(curr_white))):
            print(f"检测到标准白线转红线: {prev_white} -> {curr_red}")
            return 1
            
        # 红线转白线
        if (prev_red is not None and not pd.isna(prev_red) and 
            (curr_white is not None and not pd.isna(curr_white)) and 
            (curr_red is None or pd.isna(curr_red))):
            print(f"检测到标准红线转白线: {prev_red} -> {curr_white}")
            return -1
            
        return 0
    except Exception as e:
        print(f"检测红白线转换出错: {e}")
        return 0

# 在全局作用域中定义函数
def check_historical_transition(white_line, red_line):
    """
    检查历史数据中是否有白线转红线
    
    参数:
    white_line: 白线序列
    red_line: 红线序列
    
    返回:
    (has_transition, bars_ago): 是否有转换，以及发生在多少根K线之前
    """
    try:
        # 确保数据足够
        if white_line is None or red_line is None or len(white_line) < 5 or len(red_line) < 5:
            return False, 0
            
        # 从最近的数据开始向前查找
        for i in range(len(white_line)-2, 0, -1):
            # 检查是否有白线转红线
            if (not pd.isna(white_line.iloc[i]) and white_line.iloc[i] is not None and 
                pd.isna(white_line.iloc[i+1]) and 
                not pd.isna(red_line.iloc[i+1]) and red_line.iloc[i+1] is not None):
                
                # 计算发生在多少根K线之前
                bars_ago = len(white_line) - 1 - i
                print(f"检测到白线转红线发生在{bars_ago}根K线之前")
                return True, bars_ago
                
        return False, 0
    except Exception as e:
        print(f"检查历史转换出错: {e}")
        return False, 0

# 在handlebar函数中，在使用can_open_position之前添加定义

# 其他可能的限制条件
# ...

# 修改红白线检查函数
def check_break_red_line(price_data, red_line_prev, red_line_curr):
    """
    检查是否跌破红线
    
    参数:
    price_data: 价格数据
    red_line_prev: 前一根K线的红线
    red_line_curr: 当前K线的红线
    
    返回:
    是否跌破红线
    """
    try:
        print("=== 跌破红线详细检查 ===")
        print(f"前一根K线红线: {red_line_prev}")
        print(f"当前K线红线: {red_line_curr}")
        
        if price_data is None or price_data.empty:
            print("价格数据为空")
            return False
            
        current_price = price_data['close'].iloc[-1]
        print(f"当前收盘价: {current_price}")
        
        # 添加空值检查 - 红线
        if red_line_prev is not None and not pd.isna(red_line_prev) and current_price is not None:
            price_prev_pct = (current_price - red_line_prev) / red_line_prev * 100
            print(f"价格与前一根红线比较: {price_prev_pct:.2f}%")
        else:
            print("前一根红线不存在或为空，无法计算百分比")
            price_prev_pct = None
            
        if red_line_curr is not None and not pd.isna(red_line_curr) and current_price is not None:
            price_curr_pct = (current_price - red_line_curr) / red_line_curr * 100
            print(f"价格与当前红线比较: {price_curr_pct:.2f}%")
            
            # 检查价格与红线的距离（用于做多）
            red_line_distance = (current_price - red_line_curr) / red_line_curr * 100
            red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
            print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
        else:
            print("当前红线不存在或为空，无法计算百分比")
            price_curr_pct = None
            
        # 获取5分钟白线值
        try:
            # 计算白线值
            white_line_data, _ = calculate_red_white_lines_exact(price_data)
            white_line_value = white_line_data.iloc[-1] if not white_line_data.empty else None
            
            # 如果白线存在，计算价格与白线的距离
            if white_line_value is not None and not pd.isna(white_line_value):
                white_line_distance = (white_line_value - current_price) / white_line_value * 100
                white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
                print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
        except Exception as e:
            print(f"获取白线或计算白线距离时出错: {e}")
        
        # 判断是否跌破红线
        if red_line_prev is not None and not pd.isna(red_line_prev) and current_price < red_line_prev:
            return True
        if red_line_curr is not None and not pd.isna(red_line_curr) and current_price < red_line_curr:
            return True
            
        return False
    except Exception as e:
        print(f"检查红白线出错: {e}")
        return False

# 修改打印红白线详情函数
def print_line_details(price_data, white_line, red_line):
    """
    打印红白线详细信息
    
    参数:
    price_data: 价格数据
    white_line: 白线值
    red_line: 红线值
    """
    try:
        print("\n=== 红白线详细信息 ===")
        
        if price_data is None or price_data.empty:
            print("价格数据为空")
            return
            
        current_price = price_data['close'].iloc[-1]
        print(f"当前价格: {current_price}")
        print(f"白线价格: {white_line}")
        print(f"红线价格: {red_line}")
        
        # 检查价格与白线的距离（用于做空）
        if white_line is not None and not pd.isna(white_line) and current_price is not None:
            white_line_distance = (white_line - current_price) / white_line * 100
            white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
            print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
            
            # 检查是否突破白线
            if current_price > white_line:
                print(f"突破白线: 价格{current_price} > 白线{white_line}")
            else:
                print("无白线突破")
        else:
            print("无白线或白线为空")
            
        # 检查价格与红线的距离（用于做多）
        if red_line is not None and not pd.isna(red_line) and current_price is not None:
            red_line_distance = (current_price - red_line) / red_line * 100
            red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
            print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
            
            # 检查是否跌破红线
            if current_price < red_line:
                print(f"跌破红线: 价格{current_price} < 红线{red_line}")
            else:
                print("无红线跌破")
        else:
            print("无红线或红线为空")
            
        # 打印最高价和最低价
        if 'high' in price_data.columns and 'low' in price_data.columns:
            print(f"最高价: {price_data['high'].iloc[-1]}")
            print(f"最低价: {price_data['low'].iloc[-1]}")
            
    except Exception as e:
        print(f"打印红白线详情出错: {e}")

# 添加新的红白线计算函数
def calculate_red_white_lines(price_data):
    """
    根据原始公式计算红白线指标
    
    参数:
    price_data: 包含open, high, low, close的DataFrame
    
    返回:
    white_line: 白线序列
    red_line: 红线序列
    """
    try:
        # 确保数据足够
        if len(price_data) < 10:
            return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data))
        
        # 计算高低点
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        
        # 计算2根K线的最高价和最低价
        hx = high.rolling(window=2).max()
        lx = low.rolling(window=2).min()
        
        # 初始化结果序列
        white_line = pd.Series([None] * len(price_data), index=price_data.index)
        red_line = pd.Series([None] * len(price_data), index=price_data.index)
        k2 = pd.Series([0] * len(price_data), index=price_data.index)
        
        # 计算H1和L1
        for i in range(5, len(price_data)):
            # 白线条件
            if (lx[i] > lx[i-1] and lx[i] > lx[i-3] and lx[i] > lx[i-5] and 
                hx[i] > hx[i-1] and hx[i] > hx[i-2] and hx[i] > hx[i-4] and 
                open_price[i] < close[i] and 
                (close[i] - open_price.iloc[:i+1].min()) > 0):
                
                # 设置白线值为前4根K线的最低价
                white_line[i] = lx[i-4]
                k2[i] = 1
            
            # 红线条件
            elif (hx[i] < hx[i-1] and hx[i] < hx[i-2] and hx[i] < hx[i-4] and 
                  lx[i] < lx[i-1] and lx[i] < lx[i-3] and lx[i] < lx[i-5] and 
                  open_price[i] > close[i] and 
                  (open_price.iloc[:i+1].max() - close[i]) > 0):
                
                # 设置红线值为前4根K线的最高价
                red_line[i] = hx[i-4]
                k2[i] = -3
        
        # 填充空值 - 使用前一个有效值
        for i in range(1, len(price_data)):
            if k2[i] == 0:
                k2[i] = k2[i-1]
            
            if k2[i] == 1 and pd.isna(white_line[i]):
                # 找到前一个白线值
                for j in range(i-1, -1, -1):
                    if not pd.isna(white_line[j]):
                        white_line[i] = white_line[j]
                        break
            
            elif k2[i] == -3 and pd.isna(red_line[i]):
                # 找到前一个红线值
                for j in range(i-1, -1, -1):
                    if not pd.isna(red_line[j]):
                        red_line[i] = red_line[j]
                        break
        
        print(f"红白线计算完成: 数据长度={len(price_data)}, 白线非空值={white_line.notna().sum()}, 红线非空值={red_line.notna().sum()}")
        return white_line, red_line
    
    except Exception as e:
        print(f"计算红白线出错: {e}")
        # 返回空序列
        return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)

def calculate_red_white_lines_exact(price_data):
    try:
        # 确保数据足够
        if len(price_data) < 10:
            return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data))
        
        # 检查必需的列是否存在
        required_columns = ['high', 'low', 'open', 'close']
        for col in required_columns:
            if col not in price_data.columns:
                print(f"计算红白线错误: 缺少必需的列 '{col}'")
                return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)
        
        # 计算HX和LX
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        
        # HX:=HHV(HIGH,2);
        hx = high.rolling(window=2).max()
        
        # LX:=LLV(LOW,2);
        lx = low.rolling(window=2).min()
        
        # 初始化H1, L1, K1, K2, G
        h1 = pd.Series([0] * len(price_data), index=price_data.index)
        l1 = pd.Series([0] * len(price_data), index=price_data.index)
        k1 = pd.Series([0] * len(price_data), index=price_data.index)
        k2 = pd.Series([0] * len(price_data), index=price_data.index)
        g = pd.Series([None] * len(price_data), index=price_data.index)
        
        # 计算H1和L1
        for i in range(5, len(price_data)):
            # H1:=IFELSE(HX<REF(HX,1)&&HX<REF(HX,2)&&HX<REF(HX,4)&&LX<REF(LX,1)&&LX<REF(LX,3)&&LX<REF(LX,5)&&OPEN>CLOSE&&(HHV(OPEN,0)-CLOSE)>0,REF(HX,4),0);
            if (hx[i] < hx[i-1] and hx[i] < hx[i-2] and hx[i] < hx[i-4] and 
                lx[i] < lx[i-1] and lx[i] < lx[i-3] and lx[i] < lx[i-5] and 
                open_price[i] > close[i] and 
                (open_price.iloc[:i+1].max() - close[i]) > 0):
                h1[i] = hx[i-4]
            else:
                h1[i] = 0
            
            # L1:=IFELSE(LX>REF(LX,1)&&LX>REF(LX,3)&&LX>REF(LX,5)&&HX>REF(HX,1)&&HX>REF(HX,2)&&HX>REF(HX,4)&&OPEN<CLOSE&&(CLOSE-LLV(OPEN,0))>0,REF(LX,4),0);
            if (lx[i] > lx[i-1] and lx[i] > lx[i-3] and lx[i] > lx[i-5] and 
                hx[i] > hx[i-1] and hx[i] > hx[i-2] and hx[i] > hx[i-4] and 
                open_price[i] < close[i] and 
                (close[i] - open_price.iloc[:i+1].min()) > 0):
                l1[i] = lx[i-4]
            else:
                l1[i] = 0
        
        # H2:=VALUEWHEN(H1>0,H1);
        h2 = pd.Series([None] * len(price_data), index=price_data.index)
        last_valid_h1 = None
        for i in range(len(price_data)):
            if h1[i] > 0:
                last_valid_h1 = h1[i]
            h2[i] = last_valid_h1
        
        # L2:=VALUEWHEN(L1>0,L1);
        l2 = pd.Series([None] * len(price_data), index=price_data.index)
        last_valid_l1 = None
        for i in range(len(price_data)):
            if l1[i] > 0:
                last_valid_l1 = l1[i]
            l2[i] = last_valid_l1
        
        # K1:=IFELSE(CLOSE>H2,-3,IFELSE(CLOSE<L2,1,0));
        for i in range(len(price_data)):
            if h2[i] is not None and close[i] > h2[i]:
                k1[i] = -3
            elif l2[i] is not None and close[i] < l2[i]:
                k1[i] = 1
            else:
                k1[i] = 0
        
        # K2:=VALUEWHEN(K1<>0,K1);
        last_valid_k1 = 0
        for i in range(len(price_data)):
            if k1[i] != 0:
                last_valid_k1 = k1[i]
            k2[i] = last_valid_k1
        
        # G:=IFELSE(K2=1,H2,L2);
        for i in range(len(price_data)):
            if k2[i] == 1:
                g[i] = h2[i]
            else:
                g[i] = l2[i]
        
        # 转换为红白线
        white_line = pd.Series([None] * len(price_data), index=price_data.index)
        red_line = pd.Series([None] * len(price_data), index=price_data.index)
        
        for i in range(len(price_data)):
            if k2[i] == 1:  # 白线
                white_line[i] = g[i]
                red_line[i] = None
            elif k2[i] == -3:  # 红线
                red_line[i] = g[i]
                white_line[i] = None
        
        print(f"文华指标红白线计算完成: 数据长度={len(price_data)}, 白线非空值={white_line.notna().sum()}, 红线非空值={red_line.notna().sum()}")
        return white_line, red_line
    
    except Exception as e:
        print(f"计算文华指标红白线出错: {e}")
        # 返回空序列
        return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)

def should_trade_with_transition(transition_signal, current_price, MA_Line, trend, volume_data, 
                               white_line=None, red_line=None, time_filter=True, ma55_value=None,
                               prev_white=None, prev_red=None):  # 添加前一根K线的线值
    
    # 解析转换信号
    transition_long = transition_signal == 1  # 白线转红线，做多信号
    transition_short = transition_signal == -1  # 红线转白线，做空信号
    
    # 价格与MA的关系
    price_above_ma = current_price > MA_Line
    
    # 趋势方向
    trend_up = trend == 1
    trend_down = trend == -1
    
    # 成交量确认
    volume_confirmed = check_volume_increase(volume_data, Config.VOLUME_THRESHOLD_LONG)
    
    # 检查与55均线的距离
    ma55_distance_ok = True
    ma55_distance_pct = 0
    
    if ma55_value is not None:
        ma55_distance_pct = abs(current_price - ma55_value) / ma55_value
        ma55_distance_ok = ma55_distance_pct <= Config.MA55_DISTANCE_THRESHOLD
    
    # 检查价格与红白线的距离
    red_line_distance_ok = True
    white_line_distance_ok = True
    red_line_distance_pct = 0
    white_line_distance_pct = 0
    
    # 计算与红线的距离（用于做多）
    if red_line is not None and not pd.isna(red_line):
        red_line_distance_pct = (current_price - red_line) / red_line
        red_line_distance_ok = red_line_distance_pct <= Config.RED_LINE_DISTANCE_THRESHOLD
    
    # 计算与白线的距离（用于做空）
    if white_line is not None and not pd.isna(white_line):
        white_line_distance_pct = (white_line - current_price) / white_line
        white_line_distance_ok = white_line_distance_pct <= Config.WHITE_LINE_DISTANCE_THRESHOLD
    
    # ===== 新增：转换确认增强条件 =====
    transition_strength_ok = True
    
    if transition_long:  # 白线转红线时的增强确认
        # 计算与前白线的突破强度，而非与新红线的距离
        price_strength_ok = False
        price_strength_pct = 0
        
        # 添加安全检查
        if prev_white is not None and not pd.isna(prev_white) and prev_white > 0:
            try:
                price_strength_pct = (current_price / prev_white - 1)
                price_strength_ok = price_strength_pct >= Config.PRICE_STRENGTH_THRESHOLD
                # 打印正确的突破强度信息
                print(f"价格强度检查: {'通过' if price_strength_ok else '不通过'} (价格高于原白线{price_strength_pct*100:.2f}%)")
            except Exception as e:
                print(f"计算价格强度出错: {e}")
                price_strength_ok = False
        else:
            print("价格强度检查: 不通过 (前白线数据缺失或无效)")
        
        # 2. 检查成交量强度 - 要求转换时成交量明显放大
        volume_strength_ok = volume_data.iloc[-1] > volume_data.iloc[-3:].mean() * Config.VOLUME_STRENGTH_THRESHOLD
        
        # 3. 检查K线形态 - 要求为阳线且收盘接近最高点(实体占比大)
        if len(volume_data.index) > 0 and hasattr(volume_data, 'index'):
            try:
                last_idx = volume_data.index[-1]
                candle_data = ContextInfo.get_market_data(['open', 'high', 'low', 'close'], 
                                                    [g.code], 
                                                    period='5m',
                                                    count=1)
                if not candle_data.empty:
                    open_price = candle_data['open'].iloc[-1]
                    high_price = candle_data['high'].iloc[-1]
                    close_price = candle_data['close'].iloc[-1]
                    
                    # 是阳线
                    is_bullish = close_price > open_price
                    # 实体占比大
                    body_ratio = 0
                    if high_price > open_price:
                        body_ratio = (close_price - open_price) / (high_price - open_price)
                    candle_strength_ok = is_bullish and body_ratio > Config.CANDLE_BODY_RATIO
                else:
                    candle_strength_ok = True
            except Exception as e:
                print(f"检查K线形态出错: {e}")
                candle_strength_ok = True
        else:
            candle_strength_ok = True
            
        # 综合判断转换强度
        transition_strength_ok = price_strength_ok and (volume_strength_ok or candle_strength_ok)
        
        # 打印转换强度判断结果
        if transition_long:
            print("\n=== 转换强度增强检查 ===")
            if prev_white is not None and not pd.isna(prev_white) and prev_white > 0:
                print(f"价格强度检查: {'通过' if price_strength_ok else '不通过'} (价格高于原白线{price_strength_pct*100:.2f}%)")
            else:
                print("价格强度检查: 不通过 (前白线数据缺失或无效)")
            print(f"成交量强度检查: {'通过' if volume_strength_ok else '不通过'}")
            print(f"K线形态检查: {'通过' if candle_strength_ok else '不通过'}")
            print(f"转换强度总体检查: {'通过' if transition_strength_ok else '不通过'}")
    
    # ===== 新增部分结束 =====
    
    # 判断做多条件 - 添加转换强度确认
    can_long = (transition_long and  # 白线转红线
                price_above_ma and   # 价格在MA上方
                trend_up and         # 趋势向上
                volume_confirmed and # 成交量确认
                ma55_distance_ok and # 与55均线距离适中
                red_line_distance_ok and # 与红线距离适中
                transition_strength_ok and # 新增：转换强度确认
                time_filter)         # 时间过滤
    
    # 判断做空条件 - 空仓条件暂不修改
    can_short = (transition_short and  # 红线转白线
                 not price_above_ma and # 价格在MA下方
                 trend_down and        # 趋势向下
                 volume_confirmed and  # 成交量确认
                 ma55_distance_ok and  # 与55均线距离适中
                 white_line_distance_ok and # 与白线距离适中
                 time_filter)          # 时间过滤
    
    # 输出交易信号信息
    if transition_long or transition_short:
        print("\n=== 转换信号交易条件检查 ===")
        print(f"转换信号: {'白转红' if transition_long else '红转白' if transition_short else '无'}")
        print(f"价格位置: {'MA上方' if price_above_ma else 'MA下方'}")
        print(f"趋势方向: {'向上' if trend_up else '向下' if trend_down else '无明显趋势'}")
        print(f"成交量确认: {'是' if volume_confirmed else '否'}")
        print(f"时间过滤: {'是' if time_filter else '否'}")
        
        # 添加55均线距离信息
        if ma55_value is not None:
            print(f"与55均线距离: {ma55_distance_pct*100:.2f}% {'(符合要求)' if ma55_distance_ok else '(超出阈值)'}")
        
        # 添加红白线距离信息
        if red_line is not None and not pd.isna(red_line):
            print(f"价格与红线距离: {red_line_distance_pct*100:.2f}% {'(符合要求)' if red_line_distance_ok else '(超出阈值)'}")
        if white_line is not None and not pd.isna(white_line):
            print(f"价格与白线距离: {white_line_distance_pct*100:.2f}% {'(符合要求)' if white_line_distance_ok else '(超出阈值)'}")
        
        if red_line:
            print(f"价格与红线关系: {'价格在红线上方' if current_price > red_line else '价格在红线下方'}")
        if white_line:
            print(f"价格与白线关系: {'价格在白线上方' if current_price > white_line else '价格在白线下方'}")
        print(f"可以做多: {can_long}")
        print(f"可以做空: {can_short}")
        
        # 添加警告信息
        if transition_long and current_price < red_line:
            print("警告：白线转红线但价格在红线下方，建议观望")
        if transition_short and current_price > white_line:
            print("警告：红线转白线但价格在白线上方，建议观望")
        if not ma55_distance_ok:
            print(f"警告：价格偏离55均线过远({ma55_distance_pct*100:.2f}%)，建议等待回归后入场")
        if not red_line_distance_ok and transition_long:
            print(f"警告：价格偏离红线支撑过远({red_line_distance_pct*100:.2f}%)，建议等待回调后入场")
        if not white_line_distance_ok and transition_short:
            print(f"警告：价格偏离白线压力过远({white_line_distance_pct*100:.2f}%)，建议等待反弹后入场")
        # 新增警告
        if transition_long and not transition_strength_ok:
            print("警告：白线转红线强度不足，可能是假突破，建议观望")
    
    return can_long, can_short

def check_price_line_distance(current_price, white_line, red_line):
    """
    检查价格与红白线的距离
    
    参数:
    current_price: 当前价格
    white_line: 白线值
    red_line: 红线值
    
    返回:
    (red_line_distance_ok, white_line_distance_ok): 是否符合做多和做空的距离要求
    """
    red_line_distance_ok = True
    white_line_distance_ok = True
    
    # 检查价格与白线的距离（用于做空）
    if white_line is not None and not pd.isna(white_line) and current_price is not None and not pd.isna(current_price):
        try:
            white_line_distance = (white_line - current_price) / white_line * 100
            white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
            print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
        except Exception as e:
            print(f"计算白线距离出错: {e}")
            white_line_distance_ok = False
    else:
        print("白线不存在或为空，无法计算与白线的距离")
        
    # 检查价格与红线的距离（用于做多）
    if red_line is not None and not pd.isna(red_line) and current_price is not None and not pd.isna(current_price):
        try:
            red_line_distance = (current_price - red_line) / red_line * 100
            red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
            print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
        except Exception as e:
            print(f"计算红线距离出错: {e}")
            red_line_distance_ok = False
    else:
        print("红线不存在或为空，无法计算与红线的距离")
        
    return red_line_distance_ok, white_line_distance_ok

def check_and_print_line_distance(current_price, white_line, red_line):
    """
    检查并打印价格与红白线的距离
    
    参数:
    current_price: 当前价格
    white_line: 白线值
    red_line: 红线值
    
    返回:
    (red_line_distance_ok, white_line_distance_ok): 距离是否符合要求
    """
    red_line_distance_ok = True
    white_line_distance_ok = True
    
    print("\n=== 价格与红白线距离检查 ===")
    print(f"当前价格: {current_price}")
    
    # 检查价格与白线的距离（用于做空）
    if white_line is not None and not pd.isna(white_line) and current_price is not None and not pd.isna(current_price):
        try:
            white_line_distance = (white_line - current_price) / white_line * 100
            white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
            print(f"白线价格: {white_line}")
            print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
            if not white_line_distance_ok:
                print(f"警告: 价格与白线距离超过{Config.WHITE_LINE_DISTANCE_THRESHOLD * 100:.2f}%阈值，建议等待反弹后再考虑做空")
        except Exception as e:
            print(f"计算白线距离出错: {e}")
            white_line_distance_ok = False
    else:
        print("白线不存在或为空，无法计算距离")
    
    # 检查价格与红线的距离（用于做多）
    if red_line is not None and not pd.isna(red_line) and current_price is not None and not pd.isna(current_price):
        try:
            red_line_distance = (current_price - red_line) / red_line * 100
            red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
            print(f"红线价格: {red_line}")
            print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
            if not red_line_distance_ok:
                print(f"警告: 价格与红线距离超过{Config.RED_LINE_DISTANCE_THRESHOLD * 100:.2f}%阈值，建议等待回调后再考虑做多")
        except Exception as e:
            print(f"计算红线距离出错: {e}")
            red_line_distance_ok = False
    else:
        print("红线不存在或为空，无法计算距离")
    
    return red_line_distance_ok, white_line_distance_ok

def enhanced_stop_loss_check(ContextInfo, current_price):
    # 新增：仅transition信号持仓才启用ATR止损
    if not hasattr(g, 'position_source') or g.position_source != 'transition':
        return False, None, None
    """
    增强版止损检查
    
    参数:
    ContextInfo: 上下文信息
    current_price: 当前价格
    
    返回:
    should_stop: 是否应该止损
    stop_price: 止损价格
    stop_reason: 止损原因
    """
    try:
        # 1. 获取更实时的数据
        # 获取1分钟K线数据，更及时地监控价格变化
        price_1m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='1m',
                                           count=20)
        
        # 获取5分钟K线数据用于红白线计算
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='5m',
                                           count=10)
        
        # 2. 计算红白线
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        
        # 3. 多重止损条件检查
        stop_conditions = []
        
        # 条件1: 1分钟K线收盘价跌破红线
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            if price_1m['close'].iloc[-1] < red_line_5m.iloc[-1]:
                stop_conditions.append({
                    'type': 'close_below_red',
                    'price': red_line_5m.iloc[-1],
                    'reason': '1分钟收盘价跌破红线'
                })
        
        # 条件2: 盘中价格跌破红线
        if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
            if price_1m['low'].iloc[-1] < red_line_5m.iloc[-1]:
                stop_conditions.append({
                    'type': 'low_below_red',
                    'price': red_line_5m.iloc[-1],
                    'reason': '盘中最低价跌破红线'
                })
        
        # 条件3: 红线消失且价格低于前一根红线
        if red_line_5m.iloc[-2] is not None and not pd.isna(red_line_5m.iloc[-2]) and (red_line_5m.iloc[-1] is None or pd.isna(red_line_5m.iloc[-1])):
            if price_1m['close'].iloc[-1] < red_line_5m.iloc[-2]:
                stop_conditions.append({
                    'type': 'red_line_disappear',
                    'price': red_line_5m.iloc[-2],
                    'reason': '红线消失且价格低于前一根红线'
                })
        
        # 4. 成交量确认
        volume_confirmed = False
        if len(price_1m) >= 3:
            # 检查是否放量跌破
            volume_ratio = price_1m['volume'].iloc[-1] / price_1m['volume'].iloc[-2]
            if volume_ratio > 1.2:  # 成交量放大20%
                volume_confirmed = True
        
        # 5. 综合判断
        if stop_conditions and volume_confirmed:
            # 选择最严格的止损条件
            stop_condition = min(stop_conditions, key=lambda x: x['price'])
            return True, stop_condition['price'], stop_condition['reason']
        
        return False, None, None
        
    except Exception as e:
        print(f"增强版止损检查出错: {str(e)}")
        return False, None, None

def real_time_stop_loss_monitor(ContextInfo, current_price):
    """
    实时止损监控函数
    """
    try:
        if g.position == "none":
            return
        
        # 多单止损逻辑
        if g.position == "long":
            # 获取动态止损价格
            should_stop, stop_price, stop_reason = enhanced_stop_loss_check(ContextInfo, current_price)
            
            
            # 如果有止损价格且当前价格低于止损价格，执行止损
            if should_stop and stop_price and current_price < stop_price:
                print(f"触发止损: {stop_reason}, 价格={stop_price}")
                    
                # 执行止损
                passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单止损', ContextInfo)
                log_trade("多单止损", current_price)
                
                # 更新持仓状态
                g.position = "none"
                g.position_size = 0
                
                # 记录平仓时间
                g.last_close_time = time.time()
                
    except Exception as e:
        print(f"实时止损监控出错: {str(e)}")

def check_stop_loss_and_reverse(ContextInfo, current_price):
    # 新增：仅transition信号持仓才启用ATR止损和反向开仓
    if not hasattr(g, 'position_source') or g.position_source != 'transition':
        return False
    try:
        print("\n=== 检查止损和反向开仓 ===")
        print(f"当前持仓: {g.position}")
        print(f"当前价格: {current_price}")
        
        # 获取5分钟K线数据
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='5m',
                                           count=10)
        
        # 计算红白线
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        
        # 多单止损并反手做空
        if g.position == "long":
            # 检查是否跌破红线
            red_line_broken = False
            
            # 情况1：红线消失且价格低于前一根红线
            if red_line_5m.iloc[-2] is not None and not pd.isna(red_line_5m.iloc[-2]) and (red_line_5m.iloc[-1] is None or pd.isna(red_line_5m.iloc[-1])):
                if price_5m['close'].iloc[-1] < red_line_5m.iloc[-2]:
                    red_line_broken = True
                    print("多单止损信号：检测到跌破红线支撑（红线消失）")
            
            # 情况2：价格低于当前红线
            elif red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
                if price_5m['close'].iloc[-1] < red_line_5m.iloc[-1]:
                    red_line_broken = True
                    print(f"多单止损信号：检测到跌破红线支撑（价格{price_5m['close'].iloc[-1]}低于红线{red_line_5m.iloc[-1]}）")
            
            if red_line_broken:
                # 1. 先平多仓
                passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单止损', ContextInfo)
                log_trade("多单止损", current_price)
                
                # 记录平仓时间
                g.last_close_time = time.time()
                
                # 2. 直接开空仓
                passorder(3, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '反手开空', ContextInfo)
                log_trade("反手开空", current_price)
                
                # 3. 更新状态
                g.position = "short"
                g.buy_short += 1
                g.trace_time_short = time.time()
                g.hold_price = current_price
                g.open_price = current_price
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                g.is_reverse_trade = True  # 设置反向开仓标志
                
                # 4. 重置止盈标记
                g.profit_taken_1_short = False
                g.profit_taken_2_short = False
                g.profit_taken_3_short = False
                
                # 在触发止损和反向开仓时添加更详细的日志
                print(f"触发止损和反向开仓:")
                print(f"- 原持仓方向: long")
                print(f"- 新持仓方向: short")
                print(f"- 开仓价格: {current_price}")
                print(f"- 反向开仓标志: 是")
                
                return True
                
        # 空单止损并反手做多
        elif g.position == "short":
            # 检查是否突破白线
            white_line_broken = False
            
            # 情况1：白线消失且价格高于前一根白线
            if white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]) and (white_line_5m.iloc[-1] is None or pd.isna(white_line_5m.iloc[-1])):
                if price_5m['close'].iloc[-1] > white_line_5m.iloc[-2]:
                    white_line_broken = True
                    print("空单止损信号：检测到突破白线压力（白线消失）")
            
            # 情况2：价格高于当前白线
            elif white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
                if price_5m['close'].iloc[-1] > white_line_5m.iloc[-1]:
                    white_line_broken = True
                    print(f"空单止损信号：检测到突破白线压力（价格{price_5m['close'].iloc[-1]}高于白线{white_line_5m.iloc[-1]}）")
            
            if white_line_broken:
                # 1. 先平空仓
                passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '空单止损', ContextInfo)
                log_trade("空单止损", current_price)
                
                # 记录平仓时间
                g.last_close_time = time.time()
                
                # 2. 直接开多仓
                passorder(0, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '反手开多', ContextInfo)
                log_trade("反手开多", current_price)
                
                # 3. 更新状态
                g.position = "long"
                g.buy_long += 1
                g.trace_time_long = time.time()
                g.hold_price = current_price
                g.open_price = current_price
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                
                # 4. 重置止盈标记
                g.profit_taken_1 = False
                g.profit_taken_2 = False
                g.profit_taken_3 = False
                
                # 在触发止损和反向开仓时添加更详细的日志
                print(f"触发止损和反向开仓:")
                print(f"- 原持仓方向: short")
                print(f"- 新持仓方向: long")
                print(f"- 开仓价格: {current_price}")
                print(f"- 反向开仓标志: 是")
                
                return True
                
        return False
        
    except Exception as e:
        print(f"止损和反向开仓检查出错: {str(e)}")
        return False

# 增强版打印回调比例函数
def print_enhanced_pullback_ratios(price_data, white_line_value, red_line_value):
    """打印增强版回调/反弹比例，无论是否满足交易条件都打印"""
    try:
        if price_data is None or len(price_data) < Config.PULLBACK_LOOKBACK:
            return
            
        high = price_data['high'].iloc[-Config.PULLBACK_LOOKBACK:].max()
        low = price_data['low'].iloc[-Config.PULLBACK_LOOKBACK:].min()
        current = price_data['close'].iloc[-1]
        
        # 检查数据有效性
        if pd.isna(high) or pd.isna(low) or pd.isna(current):
            print("回调/反弹比例计算: 数据中存在NaN值，无法计算")
            return
            
        # 防止分母为0或接近0
        range_value = high - low
        if range_value <= 0.0001:  # 设置一个小的阈值
            print(f"回调/反弹比例计算: 价格区间过小 (高点={high}, 低点={low}, 区间={range_value})")
            return
        
        # 计算回调比例（从高点回落）
        pullback_ratio = (high - current) / range_value
        in_ideal_range = Config.PULLBACK_RATIO_MIN <= pullback_ratio <= Config.PULLBACK_RATIO_MAX
        status = "【理想区间】" if in_ideal_range else "【超出理想区间】"
        print(f"回调比例: {pullback_ratio:.2%} {status}")
        
        # 如果有红线，检查是否处于回调到红线做多的理想位置
        if red_line_value is not None and not pd.isna(red_line_value):
            if price_data['low'].iloc[-1] <= red_line_value and current > red_line_value:
                if in_ideal_range:
                    print(f"? 回调到红线支撑位做多信号成立 - 回调比例在理想区间内")
                else:
                    print(f"? 回调到红线支撑位但回调比例不理想 - 可能处于红线末端")
        
        # 计算反弹比例（从低点回升）
        rebound_ratio = (current - low) / range_value
        in_ideal_range = Config.PULLBACK_RATIO_MIN <= rebound_ratio <= Config.PULLBACK_RATIO_MAX
        status = "【理想区间】" if in_ideal_range else "【超出理想区间】"
        print(f"反弹比例: {rebound_ratio:.2%} {status}")
        
        # 如果有白线，检查是否处于反弹到白线做空的理想位置
        if white_line_value is not None and not pd.isna(white_line_value):
            if price_data['high'].iloc[-1] >= white_line_value and current < white_line_value:
                if in_ideal_range:
                    print(f"? 反弹到白线压力位做空信号成立 - 反弹比例在理想区间内")
                else:
                    print(f"? 反弹到白线压力位但反弹比例不理想 - 可能处于白线末端")
    except Exception as e:
        print(f"计算回调/反弹比例出错: {e}")

# 在技术指标计算函数部分添加红白线持续时间检查函数
def check_line_duration(line_series, line_type="red"):
    """
    检查红线或白线持续的K线数量
    
    参数:
    line_series: 红线或白线序列
    line_type: 线的类型，"red"表示红线，"white"表示白线
    
    返回:
    持续的K线数量
    """
    if line_series is None or len(line_series) < 2:
        return 0
    
    # 从最新数据向前查找
    duration = 0
    for i in range(len(line_series)-1, -1, -1):
        # 对于红线，检查值是否非空
        if line_type == "red" and line_series.iloc[i] is not None and not pd.isna(line_series.iloc[i]):
            duration += 1
        # 对于白线，检查值是否非空
        elif line_type == "white" and line_series.iloc[i] is not None and not pd.isna(line_series.iloc[i]):
            duration += 1
        else:
            # 如果当前值为空或不符合要求，终止计数
            if (line_type == "red" and (line_series.iloc[i] is None or pd.isna(line_series.iloc[i]))) or \
               (line_type == "white" and (line_series.iloc[i] is None or pd.isna(line_series.iloc[i]))):
                break
    
    return duration

def quick_stop_loss_check(ContextInfo, current_price):
    """
    快速移动止损检查函数 - 优化版本
    添加错误处理、校验和防护措施，防止误触发止损
    """
    # 新增：ATR止损开关
    if not Config.ATR_STOP_LOSS_ENABLE:
        return False
        
    if g.position == "none":
        return False
        
    try:
        # 1. 获取1分钟实时数据
        price_1m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                            [g.code], 
                                            period='1m',
                                            count=20)
        
        # 验证获取到的数据是否有效
        if price_1m is None or price_1m.empty or len(price_1m) < 5:
            print("快速止损检查: 获取1分钟K线数据失败或数据不足，跳过止损检查")
            return False
            
        # 检查必要的列是否存在
        required_columns = ['high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in price_1m.columns:
                print(f"快速止损检查: 缺少必要的列 '{col}'，跳过止损检查")
                return False
        
        # 2. 计算ATR指标 - 用于动态止损幅度设置
        tr1 = abs(price_1m['high'] - price_1m['low'])
        tr2 = abs(price_1m['high'] - price_1m['close'].shift(1))
        tr3 = abs(price_1m['low'] - price_1m['close'].shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=Config.ATR_WINDOW).mean().iloc[-1]  # 使用配置的ATR窗口大小
        
        # 验证ATR是否有效
        if pd.isna(atr) or atr <= 0:
            print(f"快速止损检查: 计算出的ATR无效 ({atr})，跳过止损检查")
            return False
        
        # 3. 持仓时间检查 - 调整止损敏感度
        # 优化：确保持仓时间计算正确
        current_time = time.time()
        
        # 修复持仓时间计算，避免重置为5分钟的问题
        if g.position == "long":
            # 防止trace_time_long未初始化或值异常
            if not hasattr(g, 'trace_time_long') or g.trace_time_long <= 0:
                print("快速止损检查: 多头持仓时间异常，使用系统当前时间")
                g.trace_time_long = current_time - 300  # 默认设置为5分钟前
            
            holding_minutes = (current_time - g.trace_time_long) / 60
            print(f"多头持仓时间计算: 当前时间={time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))}, "
                  f"开仓时间={time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_long))}, "
                  f"时间差={current_time - g.trace_time_long:.1f}秒 = {holding_minutes:.2f}分钟")
        else:  # short
            # 防止trace_time_short未初始化或值异常
            if not hasattr(g, 'trace_time_short') or g.trace_time_short <= 0:
                print("快速止损检查: 空头持仓时间异常，使用系统当前时间")
                g.trace_time_short = current_time - 300  # 默认设置为5分钟前
            
            holding_minutes = (current_time - g.trace_time_short) / 60
            print(f"空头持仓时间计算: 当前时间={time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(current_time))}, "
                  f"开仓时间={time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_short))}, "
                  f"时间差={current_time - g.trace_time_short:.1f}秒 = {holding_minutes:.2f}分钟")
        
        # 持仓时间防护：确保持仓时间在合理范围内（但不重置为5分钟）
        if holding_minutes < 0:
            print(f"快速止损检查: 持仓时间计算为负值 ({holding_minutes:.1f}分钟)，可能是时间戳异常")
            holding_minutes = 0
        elif holding_minutes > 60*24*7:  # 不超过一周
            print(f"快速止损检查: 持仓时间过长 ({holding_minutes:.1f}分钟)，可能是时间戳异常")
            holding_minutes = 60*24  # 最大值限制为1天
            
        # 输出正确的持仓时间信息，帮助诊断
        print(f"快速止损检查: 当前持仓时间 {holding_minutes:.1f}分钟")
        
        # 使用统一的保护期检查函数
        in_protection, protection_msg = check_protection_period()
        if in_protection:
            print(f"快速止损检查: {protection_msg}，暂不触发ATR止损")
            return False
        print(f"快速止损检查: {protection_msg}，启用ATR止损")
        
        # 设置止损系数，持仓时间越短越敏感
        if holding_minutes < 30:
            stop_factor = 0.8  # 更敏感
        elif holding_minutes < 120:
            stop_factor = 1.0  # 标准敏感度
        else:
            stop_factor = 1.2  # 更宽松
        
        # 4. 设置动态止损线
        if g.position == "long":
            # 红线止损检查 - 添加错误处理
            try:
                price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                              [g.code], 
                                              period='5m',
                                              count=10)
                
                # 验证数据是否有效
                if price_5m is None or price_5m.empty or not all(col in price_5m.columns for col in ['open', 'high', 'low', 'close']):
                    print("快速止损检查: 5分钟K线数据无效，仅使用ATR止损")
                    red_line_stop_price = float('-inf')
                else:
                    white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
                    
                    # 获取实时红线价格
                    current_red_line = red_line_5m.iloc[-1] if not pd.isna(red_line_5m.iloc[-1]) else None
                    red_line_stop_price = current_red_line if current_red_line else float('-inf')
                    
                    # 打印红线止损信息
                    if current_red_line:
                        print(f"快速止损检查: 红线止损价格 {red_line_stop_price}")
                    else:
                        print("快速止损检查: 未检测到有效红线")
            except Exception as e:
                print(f"快速止损检查: 计算红线止损出错: {str(e)}，跳过红线止损")
                red_line_stop_price = float('-inf')
            
            # 4.1 设置基于ATR的动态止损价格
            # 确保g.open_price有效
            if not hasattr(g, 'open_price') or pd.isna(g.open_price) or g.open_price <= 0:
                print(f"快速止损检查: 开仓价格异常 ({getattr(g, 'open_price', 'N/A')})，使用当前价格")
                g.open_price = current_price
                
            # 计算ATR止损价格
            atr_stop_price = g.open_price - (atr * stop_factor)
            
            # 获取当前合约的最小变动价位
            try:
                # 注释掉导致异常的调用
                # contract_info = ContextInfo.get_instrument_detail(g.code)
                # min_price_change = contract_info.variable_dict['PriceTick']
                
                # 直接使用默认最小变动价位
                min_price_change = 0.5
                print(f"快速止损检查: 使用默认最小变动价位: {min_price_change}")
            except Exception as e:
                print(f"快速止损检查: 获取合约详情失败: {str(e)}")
                # 使用默认最小变动价位
                min_price_change = 0.5
            
            # 计算最小止损距离
            min_stop_distance = g.open_price - (Config.MIN_STOP_DISTANCE * min_price_change)
            
            # 确保止损线不会太接近开仓价格
            atr_stop_price = min(atr_stop_price, min_stop_distance)
            print(f"快速止损检查: 原始ATR止损线={g.open_price - (atr * stop_factor):.2f}, 最小止损距离线={min_stop_distance:.2f}")
            
            # 4.3 设置基于移动高点的止损价格 (跟踪止损)
            if holding_minutes > 60:  # 持仓超过1小时才启用跟踪止损
                trailing_high = price_1m['high'].iloc[-15:].max()
                trailing_stop_price = trailing_high - (atr * 1.5)  # 从最高点回撤1.5个ATR
                print(f"快速止损检查: 跟踪止损线 {trailing_stop_price} (基于高点 {trailing_high})")
            else:
                trailing_stop_price = float('-inf')
                print("快速止损检查: 持仓未满1小时，未启用跟踪止损")
            
            # 4.4 取所有止损价格中的最高值作为最终止损价
            final_stop_price = max(atr_stop_price, red_line_stop_price, trailing_stop_price)
            
            # 输出所有止损线供分析
            print(f"快速止损检查: ATR止损线 {atr_stop_price}, 红线止损 {red_line_stop_price}, 跟踪止损 {trailing_stop_price}")
            print(f"快速止损检查: 最终止损线 {final_stop_price}, 当前价格 {current_price}")
            
            # 5. 止损触发条件检查 - 添加额外确认机制
            if current_price < final_stop_price:
                # 添加价格确认：止损价与当前价差异必须超过最小阈值
                price_diff_pct = (final_stop_price - current_price) / current_price * 100
                min_diff_threshold = 0.05  # 最小差距阈值0.05%
                
                # 检查是否有放量下跌确认
                volume_surge = price_1m['volume'].iloc[-1] > price_1m['volume'].iloc[-5:].mean() * 1.2
                
                # 添加K线形态确认
                is_bearish = price_1m['close'].iloc[-1] < price_1m['open'].iloc[-1]  # 是否阴线
                
                # 增强确认机制：价格需要显著低于止损线，或者满足多重条件
                significant_break = price_diff_pct > min_diff_threshold
                multi_conditions = (current_price < atr_stop_price) and (is_bearish or volume_surge)
                
                if significant_break or multi_conditions:
                    # 执行止损
                    passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单快速止损', ContextInfo)
                    log_trade(f"多单快速止损(持仓{holding_minutes:.1f}分钟)", current_price)
                    
                    # 记录止损原因
                    if current_price < red_line_stop_price:
                        print(f"止损原因: 跌破红线支撑 (价格{current_price} < 红线{red_line_stop_price})")
                    elif current_price < atr_stop_price:
                        print(f"止损原因: 跌破ATR动态止损线 (价格{current_price} < ATR止损线{atr_stop_price})")
                    elif current_price < trailing_stop_price:
                        print(f"止损原因: 跌破跟踪止损线 (价格{current_price} < 跟踪止损线{trailing_stop_price})")
                    
                    # 保存原始持仓方向
                    original_position = "long"
                    
                    # 记录平仓时间
                    g.last_close_time = time.time()
                    
                    # 检查是否需要反向开仓 - 保持与原代码的兼容性
                    if Config.REVERSE_TRADE_ENABLE:
                        # 判断是否应该反向开仓
                        if not Config.REVERSE_EVALUATION_ENABLE:
                            # 如果禁用评估机制，直接开仓
                            should_reverse = True
                            confidence_score = 100
                        else:
                            # 如果启用评估机制，计算开仓信心分数
                            should_reverse, confidence_score = should_reverse_after_atr_stop(ContextInfo, current_price, original_position)
                            
                        # 根据信心分数决定是否开仓    
                        if should_reverse and confidence_score >= Config.REVERSE_MIN_CONFIDENCE:
                            # 确定最优反向开仓价格
                            optimal_price = wait_for_optimal_reverse_entry(ContextInfo, original_position, current_price)
                            
                            if original_position == "long":  # 多单止损后开空
                                print(f"执行ATR快速止损后反向开仓(空): 优化后价格={optimal_price:.2f}")
                                # 使用限价单，价格略高于计算出的最优价格，确保成交
                                order_price = optimal_price * 1.0001
                                passorder(3, 1101, account, g.code, 12, order_price, Config.REVERSE_TRADE_HANDS, '', 1, 'ATR止损反向开空', ContextInfo)
                                
                                # 更新状态
                                g.position = "short"
                                g.position_size = Config.REVERSE_TRADE_HANDS
                                g.is_reverse_trade = True
                                g.open_price = optimal_price
                                g.hold_price = optimal_price
                                # 更新空头开仓时间
                                g.trace_time_short = time.time()
                                print(f"更新反向空头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_short))}")
                                
                                # 重置止盈标记
                                g.profit_taken_1_short = False
                                g.profit_taken_2_short = False
                                g.profit_taken_3_short = False
                            else:  # 空单止损后开多
                                print(f"执行ATR快速止损后反向开仓(多): 优化后价格={optimal_price:.2f}")
                                # 使用限价单，价格略低于计算出的最优价格，确保成交
                                order_price = optimal_price * 0.9999
                                passorder(0, 1101, account, g.code, 12, order_price, Config.REVERSE_TRADE_HANDS, '', 1, 'ATR止损反向开多', ContextInfo)
                                
                                # 更新状态
                                g.position = "long"
                                g.position_size = Config.REVERSE_TRADE_HANDS
                                g.is_reverse_trade = True
                                g.open_price = optimal_price
                                g.hold_price = optimal_price
                                # 更新多头开仓时间
                                g.trace_time_long = time.time()
                                print(f"更新反向多头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_long))}")
                                
                                # 重置止盈标记
                                g.profit_taken_1 = False
                                g.profit_taken_2 = False
                                g.profit_taken_3 = False
                        else:
                            print(f"评估分数不足，放弃反向开仓: 信心分数={confidence_score}，最低要求={Config.REVERSE_MIN_CONFIDENCE}")
                            # 更新持仓状态
                            g.position = "none"
                            g.position_size = 0
                    else:
                        # 更新持仓状态
                        g.position = "none"
                        g.position_size = 0
                    
                    return True
        elif g.position == "short":
            # 白线止损检查
            try:
                price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                              [g.code], 
                                              period='5m',
                                              count=10)
                
                if price_5m is None or price_5m.empty or not all(col in price_5m.columns for col in ['open', 'high', 'low', 'close']):
                    print("快速止损检查: 5分钟K线数据无效，仅使用ATR止损")
                    white_line_stop_price = float('inf')
                else:
                    white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
                    
                    # 获取实时白线价格
                    current_white_line = white_line_5m.iloc[-1] if not pd.isna(white_line_5m.iloc[-1]) else None
                    white_line_stop_price = current_white_line if current_white_line else float('inf')
                    
                    # 打印白线止损信息
                    if current_white_line:
                        print(f"快速止损检查: 白线止损价格 {white_line_stop_price}")
                    else:
                        print("快速止损检查: 未检测到有效白线")
            except Exception as e:
                print(f"快速止损检查: 计算白线止损出错: {str(e)}，跳过白线止损")
                white_line_stop_price = float('inf')
            
            # 4.1 设置基于ATR的动态止损价格
            # 确保g.open_price有效
            if not hasattr(g, 'open_price') or pd.isna(g.open_price) or g.open_price <= 0:
                print(f"快速止损检查: 开仓价格异常 ({getattr(g, 'open_price', 'N/A')})，使用当前价格")
                g.open_price = current_price
                
            # 计算ATR止损价格
            atr_stop_price = g.open_price + (atr * stop_factor)
            
            # 获取当前合约的最小变动价位
            try:
                # 注释掉导致异常的调用
                # contract_info = ContextInfo.get_instrument_detail(g.code)
                # min_price_change = contract_info.variable_dict['PriceTick']
                
                # 直接使用默认最小变动价位
                min_price_change = 0.5
                print(f"快速止损检查: 使用默认最小变动价位: {min_price_change}")
            except Exception as e:
                print(f"快速止损检查: 获取合约详情失败: {str(e)}")
                # 使用默认最小变动价位
                min_price_change = 0.5
            
            # 计算最小止损距离
            min_stop_distance = g.open_price + (Config.MIN_STOP_DISTANCE * min_price_change)
            
            # 确保止损线不会太接近开仓价格
            atr_stop_price = max(atr_stop_price, min_stop_distance)
            print(f"快速止损检查: 原始ATR止损线={g.open_price + (atr * stop_factor):.2f}, 最小止损距离线={min_stop_distance:.2f}")
            
            # 4.2 设置基于移动低点的止损价格 (跟踪止损)
            if holding_minutes > 60:
                trailing_low = price_1m['low'].iloc[-15:].min()
                trailing_stop_price = trailing_low + (atr * 1.5)  # 从最低点反弹1.5个ATR
                print(f"快速止损检查: 跟踪止损线 {trailing_stop_price} (基于低点 {trailing_low})")
            else:
                trailing_stop_price = float('inf')
                print("快速止损检查: 持仓未满1小时，未启用跟踪止损")
            
            # 取所有止损价格中的最低值作为最终止损价 (空单是价格高于止损线触发)
            final_stop_price = min(atr_stop_price, white_line_stop_price, trailing_stop_price)
            
            # 输出所有止损线供分析
            print(f"快速止损检查: ATR止损线 {atr_stop_price}, 白线止损 {white_line_stop_price}, 跟踪止损 {trailing_stop_price}")
            print(f"快速止损检查: 最终止损线 {final_stop_price}, 当前价格 {current_price}")
            
            # 止损触发条件检查 - 添加额外确认机制
            if current_price > final_stop_price:
                # 添加价格确认：止损价与当前价差异必须超过最小阈值
                price_diff_pct = (current_price - final_stop_price) / final_stop_price * 100
                min_diff_threshold = 0.05  # 最小差距阈值0.05%
                
                # 检查是否有放量上涨确认
                volume_surge = price_1m['volume'].iloc[-1] > price_1m['volume'].iloc[-5:].mean() * 1.2
                
                # 添加K线形态确认
                is_bullish = price_1m['close'].iloc[-1] > price_1m['open'].iloc[-1]  # 是否阳线
                
                # 增强确认机制：价格需要显著高于止损线，或者满足多重条件
                significant_break = price_diff_pct > min_diff_threshold
                multi_conditions = (current_price > atr_stop_price) and (is_bullish or volume_surge)
                
                if significant_break or multi_conditions:
                    # 执行止损
                    passorder(9, 1101, account, g.code, 13, 0, g.position_size, '', 1, '空单快速止损', ContextInfo)
                    log_trade(f"空单快速止损(持仓{holding_minutes:.1f}分钟)", current_price)
                    
                    # 记录止损原因
                    if current_price > white_line_stop_price:
                        print(f"止损原因: 突破白线压力 (价格{current_price} > 白线{white_line_stop_price})")
                    elif current_price > atr_stop_price:
                        print(f"止损原因: 突破ATR动态止损线 (价格{current_price} > ATR止损线{atr_stop_price})")
                    elif current_price > trailing_stop_price:
                        print(f"止损原因: 突破跟踪止损线 (价格{current_price} > 跟踪止损线{trailing_stop_price})")
                    
                    # 保存原始持仓方向
                    original_position = "short"
                    
                    # 记录平仓时间
                    g.last_close_time = time.time()
                    
                    # 检查是否需要反向开仓
                    if Config.REVERSE_TRADE_ENABLE:
                        # 判断是否应该反向开仓
                        if not Config.REVERSE_EVALUATION_ENABLE:
                            # 如果禁用评估机制，直接开仓
                            should_reverse = True
                            confidence_score = 100
                        else:
                            # 如果启用评估机制，计算开仓信心分数
                            should_reverse, confidence_score = should_reverse_after_atr_stop(ContextInfo, current_price, original_position)
                            
                        # 根据信心分数决定是否开仓    
                        if should_reverse and confidence_score >= Config.REVERSE_MIN_CONFIDENCE:
                            # 确定最优反向开仓价格
                            optimal_price = wait_for_optimal_reverse_entry(ContextInfo, original_position, current_price)
                            
                            if original_position == "short":  # 空单止损后开多
                                print(f"执行ATR快速止损后反向开仓(多): 优化后价格={optimal_price:.2f}")
                                # 使用限价单，价格略低于计算出的最优价格，确保成交
                                order_price = optimal_price * 0.9999
                                passorder(0, 1101, account, g.code, 12, order_price, Config.REVERSE_TRADE_HANDS, '', 1, 'ATR止损反向开多', ContextInfo)
                                
                                # 更新状态
                                g.position = "long"
                                g.position_size = Config.REVERSE_TRADE_HANDS
                                g.is_reverse_trade = True
                                g.open_price = optimal_price
                                g.hold_price = optimal_price
                                # 更新多头开仓时间
                                g.trace_time_long = time.time()
                                print(f"更新反向多头开仓时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(g.trace_time_long))}")
                                
                                # 重置止盈标记
                                g.profit_taken_1 = False
                                g.profit_taken_2 = False
                                g.profit_taken_3 = False
                        else:
                            print(f"评估分数不足，放弃反向开仓: 信心分数={confidence_score}，最低要求={Config.REVERSE_MIN_CONFIDENCE}")
                            # 更新持仓状态
                            g.position = "none"
                            g.position_size = 0
                    else:
                        # 更新持仓状态
                        g.position = "none"
                        g.position_size = 0
                        
                    return True
        
        return False
        
    except Exception as e:
        print(f"快速止损检查发生异常: {str(e)}")
        # 异常时不执行止损，返回False
        return False

def should_reverse_after_atr_stop(ContextInfo, current_price, original_position):
    """
    判断是否应该在ATR止损后进行反向开仓
    
    参数:
    ContextInfo: 上下文信息
    current_price: 当前价格
    original_position: 原持仓方向 ("long" 或 "short")
    
    返回:
    (should_reverse, confidence_score): 是否应该反向开仓及信心分数
    """
    try:
        # 如果未启用反向开仓功能，直接返回False
        if not Config.REVERSE_TRADE_ENABLE:
            return False, 0
            
        # 如果禁用评估机制，直接返回True，表示无需评估直接开仓
        if not Config.REVERSE_EVALUATION_ENABLE:
            print("反向开仓评估已禁用，直接执行反向开仓")
            return True, 100  # 返回最高信心分数
        
        # 获取5分钟K线数据
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                          [g.code], 
                                          period='5m',
                                          count=20)
        
        # 计算红白线
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        
        # 计算MA线
        MA_Line = calculate_ma(price_5m['close'], price_5m['volume'], 55)
        
        # 计算RSI
        delta = price_5m['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(window=9).mean()
        avg_loss = loss.rolling(window=9).mean()
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        current_rsi = rsi.iloc[-1]
        
        # 判断趋势
        trend = check_trend(price_5m, MA_Line.iloc[-1])
        
        # 初始化信心分数
        confidence_score = 0
        
        print("\n=== ATR止损后反向开仓评估 ===")
        print(f"原持仓方向: {original_position}")
        print(f"当前价格: {current_price}")
        
        if original_position == "long":
            # 原来是多单，现在考虑做空
            # 1. 价格应该在MA线下方
            price_below_ma = current_price < MA_Line.iloc[-1]
            if price_below_ma:
                confidence_score += 30
                print(f"价格在MA线下方: +30分")
            else:
                print(f"价格在MA线上方: +0分")
            
            # 2. 趋势应该向下
            trend_down = trend == -1
            if trend_down:
                confidence_score += 20
                print(f"趋势向下: +20分")
            else:
                print(f"趋势不向下: +0分")
            
            # 3. 白线压力确认
            white_line_ok = white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]) and current_price < white_line_5m.iloc[-1]
            if white_line_ok:
                confidence_score += 20
                print(f"白线压力确认: +20分")
            else:
                print(f"无白线压力: +0分")
            
            # 4. RSI超买检查
            if current_rsi > 70:
                confidence_score += 20
                print(f"RSI超买({current_rsi:.1f}): +20分")
            elif current_rsi > 60:
                confidence_score += 10
                print(f"RSI偏高({current_rsi:.1f}): +10分")
            else:
                print(f"RSI正常({current_rsi:.1f}): +0分")
            
            # 5. 成交量确认
            volume_increase = price_5m['volume'].iloc[-1] > price_5m['volume'].iloc[-5:].mean() * 1.2
            if volume_increase:
                confidence_score += 10
                print(f"成交量放大: +10分")
            
        elif original_position == "short":
            # 原来是空单，现在考虑做多
            # 1. 价格应该在MA线上方
            price_above_ma = current_price > MA_Line.iloc[-1]
            if price_above_ma:
                confidence_score += 30
                print(f"价格在MA线上方: +30分")
            else:
                print(f"价格在MA线下方: +0分")
            
            # 2. 趋势应该向上
            trend_up = trend == 1
            if trend_up:
                confidence_score += 20
                print(f"趋势向上: +20分")
            else:
                print(f"趋势不向上: +0分")
            
            # 3. 红线支撑确认
            red_line_ok = red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]) and current_price > red_line_5m.iloc[-1]
            if red_line_ok:
                confidence_score += 20
                print(f"红线支撑确认: +20分")
            else:
                print(f"无红线支撑: +0分")
            
            # 4. RSI超卖检查
            if current_rsi < 30:
                confidence_score += 20
                print(f"RSI超卖({current_rsi:.1f}): +20分")
            elif current_rsi < 40:
                confidence_score += 10
                print(f"RSI偏低({current_rsi:.1f}): +10分")
            else:
                print(f"RSI正常({current_rsi:.1f}): +0分")
            
            # 5. 成交量确认
            volume_increase = price_5m['volume'].iloc[-1] > price_5m['volume'].iloc[-5:].mean() * 1.2
            if volume_increase:
                confidence_score += 10
                print(f"成交量放大: +10分")
        
        # 判断是否应该反向开仓
        should_reverse = confidence_score >= Config.REVERSE_MIN_CONFIDENCE
        
        print(f"反向开仓信心分数: {confidence_score}/100")
        print(f"反向开仓决策: {'执行' if should_reverse else '不执行'}")
        
        return should_reverse, confidence_score
        
    except Exception as e:
        print(f"反向开仓评估出错: {str(e)}")
        return False, 0

def execute_reverse_take_profit(ContextInfo, current_price):
    """
    执行反向开仓的分批止盈策略
    
    参数:
    ContextInfo: 上下文信息
    current_price: 当前价格
    
    返回:
    True: 执行了止盈
    False: 未执行止盈
    """
    # 多单止盈逻辑
    if g.position == "long":
        # 计算盈利百分比
        profit_pct = calculate_profit(current_price, g.open_price, True)
        
        # 计算初始总手数
        total_hands = Config.REVERSE_TRADE_HANDS
        
        # 第一批止盈（30%仓位）- 使用反向开仓特定的止盈比例
        if not g.profit_taken_1 and profit_pct >= Config.REVERSE_TAKE_PROFIT_1:
            # 简化手数计算，直接使用百分比乘以总手数
            first_batch = max(1, int(total_hands * Config.REVERSE_PROFIT_PCT_1))
            
            # 确保不超过当前持仓
            first_batch = min(first_batch, g.position_size)
            
            if first_batch > 0:
                # 平多仓使用7
                passorder(7, 1101, account, g.code, 14, 0, first_batch, '', 1, '反向多单首批止盈', ContextInfo)
                g.profit_taken_1 = True
                g.position_size -= first_batch
                log_trade("反向多单首批止盈(30%)", current_price, profit_pct)
                return True
            
        # 第二批止盈（30%仓位）- 使用反向开仓特定的止盈比例
        elif g.profit_taken_1 and not g.profit_taken_2 and profit_pct >= Config.REVERSE_TAKE_PROFIT_2:
            # 简化手数计算，直接使用百分比乘以总手数
            second_batch = max(1, int(total_hands * Config.REVERSE_PROFIT_PCT_2))
            
            # 确保不超过当前持仓
            second_batch = min(second_batch, g.position_size)
            
            if second_batch > 0:
                # 平多仓使用7
                passorder(7, 1101, account, g.code, 14, 0, second_batch, '', 1, '反向多单第二批止盈', ContextInfo)
                g.profit_taken_2 = True
                g.position_size -= second_batch
                log_trade("反向多单第二批止盈(30%)", current_price, profit_pct)
                return True
            
        # 第三批止盈（40%仓位）- 使用反向开仓特定的止盈比例
        elif g.profit_taken_1 and g.profit_taken_2 and not g.profit_taken_3 and profit_pct >= Config.REVERSE_TAKE_PROFIT_3:
            # 剩余全部手数
            third_batch = g.position_size
            
            if third_batch > 0:
                # 平多仓使用7
                passorder(7, 1101, account, g.code, 14, 0, third_batch, '', 1, '反向多单第三批止盈', ContextInfo)
                g.profit_taken_3 = True
                g.position_size = 0
                g.position = "none"
                g.is_reverse_trade = False  # 重置反向开仓标记
                log_trade("反向多单第三批止盈(40%)", current_price, profit_pct)
                return True
    
    # 空单止盈逻辑
    elif g.position == "short":
        # 计算盈利百分比
        profit_pct = calculate_profit(current_price, g.open_price, False)
        
        # 计算初始总手数
        total_hands = Config.REVERSE_TRADE_HANDS
        
        # 第一批止盈（30%仓位）- 使用反向开仓特定的止盈比例
        if not g.profit_taken_1_short and profit_pct >= Config.REVERSE_TAKE_PROFIT_1:
            # 简化手数计算，直接使用百分比乘以总手数
            first_batch = max(1, int(total_hands * Config.REVERSE_PROFIT_PCT_1))
            
            # 确保不超过当前持仓
            first_batch = min(first_batch, g.position_size)
            
            if first_batch > 0:
                # 平空仓使用9
                passorder(9, 1101, account, g.code, 14, 0, first_batch, '', 1, '反向空单首批止盈', ContextInfo)
                g.profit_taken_1_short = True
                g.position_size -= first_batch
                log_trade("反向空单首批止盈(30%)", current_price, profit_pct)
                return True
            
        # 第二批止盈（30%仓位）- 使用反向开仓特定的止盈比例
        elif g.profit_taken_1_short and not g.profit_taken_2_short and profit_pct >= Config.REVERSE_TAKE_PROFIT_2:
            # 简化手数计算，直接使用百分比乘以总手数
            second_batch = max(1, int(total_hands * Config.REVERSE_PROFIT_PCT_2))
            
            # 确保不超过当前持仓
            second_batch = min(second_batch, g.position_size)
            
            if second_batch > 0:
                # 平空仓使用9
                passorder(9, 1101, account, g.code, 14, 0, second_batch, '', 1, '反向空单第二批止盈', ContextInfo)
                g.profit_taken_2_short = True
                g.position_size -= second_batch
                log_trade("反向空单第二批止盈(30%)", current_price, profit_pct)
                return True
            
        # 第三批止盈（40%仓位）- 使用反向开仓特定的止盈比例
        elif g.profit_taken_1_short and g.profit_taken_2_short and not g.profit_taken_3_short and profit_pct >= Config.REVERSE_TAKE_PROFIT_3:
            # 剩余全部手数
            third_batch = g.position_size
            
            if third_batch > 0:
                # 平空仓使用9
                passorder(9, 1101, account, g.code, 14, 0, third_batch, '', 1, '反向空单第三批止盈', ContextInfo)
                g.profit_taken_3_short = True
                g.position_size = 0
                g.position = "none"
                g.is_reverse_trade = False  # 重置反向开仓标记
                log_trade("反向空单第三批止盈(40%)", current_price, profit_pct)
                return True
    
    return False

def check_reverse_stop_loss(ContextInfo, current_price):
    """
    检查反向开仓的止损条件
    
    参数:
    ContextInfo: 上下文信息
    current_price: 当前价格
    
    返回:
    True: 执行了止损
    False: 未执行止损
    """
    # 新增：ATR止损开关
    if not Config.ATR_STOP_LOSS_ENABLE:
        return False
        
    try:
        if not g.is_reverse_trade:
            return False
            
        # 获取1分钟K线数据
        price_1m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='1m',
                                           count=20)
        
        # 计算ATR指标
        tr1 = abs(price_1m['high'] - price_1m['low'])
        tr2 = abs(price_1m['high'] - price_1m['close'].shift(1))
        tr3 = abs(price_1m['low'] - price_1m['close'].shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=Config.ATR_WINDOW).mean().iloc[-1]  # 使用配置的ATR窗口大小
        
        # 获取5分钟K线数据
        price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], 
                                           [g.code], 
                                           period='5m',
                                           count=10)
        
        # 计算红白线
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        
        print("\n=== 反向开仓止损检查 ===")
        print(f"当前价格: {current_price}")
        print(f"开仓价格: {g.open_price}")
        print(f"ATR值: {atr}")
        
        # 使用统一的保护期检查函数
        in_protection, protection_msg = check_protection_period()
        if in_protection:
            print(f"反向止损检查: {protection_msg}，暂不触发止损")
            return False
        
        # 获取当前合约的最小变动价位
        try:
            # 注释掉导致异常的调用
            # contract_info = ContextInfo.get_instrument_detail(g.code)
            # min_price_change = contract_info.variable_dict['PriceTick']
            
            # 直接使用默认最小变动价位
            min_price_change = 0.5
            print(f"反向止损检查: 使用默认最小变动价位: {min_price_change}")
        except Exception as e:
            print(f"反向止损检查: 获取合约详情失败: {str(e)}")
            # 使用默认最小变动价位
            min_price_change = 0.5
        
        if g.position == "long":
            # 设置ATR止损线
            atr_stop_loss = g.open_price - (atr * Config.REVERSE_STOP_LOSS_ATR)
            
            # 计算最小止损距离
            min_stop_distance = g.open_price - (Config.REVERSE_MIN_STOP_DISTANCE * min_price_change)
            print(f"最小止损距离线: {min_stop_distance}")
            
            # 确保止损线不会太接近开仓价格
            stop_price = min(atr_stop_loss, min_stop_distance)
            
            # 如果有红线，也考虑红线止损
            if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
                red_line_stop = red_line_5m.iloc[-1]
                # 取较高的止损价
                stop_price = max(stop_price, red_line_stop)
                print(f"红线止损价: {red_line_stop}")
            
            print(f"反向多单止损价: {stop_price}")
            
            # 检查是否触发止损
            if current_price < stop_price:
                # 执行止损
                passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '反向多单止损', ContextInfo)
                
                # 计算盈亏
                profit_pct = calculate_profit(current_price, g.open_price, True)
                log_trade("反向多单止损", current_price, profit_pct)
                
                # 更新状态
                g.position = "none"
                g.position_size = 0
                g.is_reverse_trade = False
                
                return True
        
        elif g.position == "short":
            # 设置ATR止损线
            atr_stop_loss = g.open_price + (atr * Config.REVERSE_STOP_LOSS_ATR)
            
            # 计算最小止损距离
            min_stop_distance = g.open_price + (Config.REVERSE_MIN_STOP_DISTANCE * min_price_change)
            print(f"最小止损距离线: {min_stop_distance}")
            
            # 确保止损线不会太接近开仓价格
            stop_price = max(atr_stop_loss, min_stop_distance)
            
            # 如果有白线，也考虑白线止损
            if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
                white_line_stop = white_line_5m.iloc[-1]
                # 取较低的止损价
                stop_price = min(stop_price, white_line_stop)
                print(f"白线止损价: {white_line_stop}")
            
            print(f"反向空单止损价: {stop_price}")
            
            # 检查是否触发止损
            if current_price > stop_price:
                # 执行止损
                passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '反向空单止损', ContextInfo)
                
                # 计算盈亏
                profit_pct = calculate_profit(current_price, g.open_price, False)
                log_trade("反向空单止损", current_price, profit_pct)
                
                # 更新状态
                g.position = "none"
                g.position_size = 0
                g.is_reverse_trade = False
                
                return True
        
        return False
        
    except Exception as e:
        print(f"反向开仓止损检查出错: {str(e)}")
        return False

def wait_for_optimal_reverse_entry(ContextInfo, original_position, current_price, max_wait_seconds=45):
    """等待最优反向开仓价格"""
    try:
        start_time = time.time()
        best_price = current_price
        atr_value = calculate_atr(ContextInfo) * 0.2  # 使用ATR的20%作为最小回调幅度
        
        # 获取红白线值作为参考
        price_5m = ContextInfo.get_market_data(['close','high','low','open','volume'], [g.code], period='5m', count=10)
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        
        # 设置目标价格
        if original_position == "long":  # 多单止损后开空
            target_bounce_pct = 0.0006  # 期望反弹0.06%
            target_price = current_price * (1 + target_bounce_pct)
            min_price = current_price
            
            # 如果有红线，将其作为参考
            if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
                red_line = red_line_5m.iloc[-1]
                # 如果价格已经跌破红线，理想开空位置是反弹回红线附近
                if current_price < red_line:
                    target_price = min(target_price, red_line)
        else:  # 空单止损后开多
            target_dip_pct = 0.0006  # 期望回调0.06%
            target_price = current_price * (1 - target_dip_pct)
            max_price = current_price
            
            # 如果有白线，将其作为参考
            if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
                white_line = white_line_5m.iloc[-1]
                # 如果价格已经突破白线，理想开多位置是回调到白线附近
                if current_price > white_line:
                    target_price = max(target_price, white_line)
        
        # 获取当前合约的最小变动价位（添加异常处理）
        try:
            # 注释掉导致异常的调用
            # contract_info = ContextInfo.get_instrument_detail(g.code)
            # min_price_change = contract_info.variable_dict['PriceTick']
            
            # 直接使用默认最小变动价位
            min_price_change = 0.5
            print(f"最优反向入场: 使用默认最小变动价位: {min_price_change}")
        except Exception as e:
            print(f"最优反向入场: 获取合约详情失败: {str(e)}")
            # 使用默认最小变动价位
            min_price_change = 0.5
        
        # 设置最长等待时间的三分之一作为最小等待时间
        min_wait_seconds = max_wait_seconds / 3
        min_wait_end = start_time + min_wait_seconds
        price_met = False
        
        print(f"开始等待最优反向开仓价格，最长等待{max_wait_seconds}秒")
        
        # 循环检查价格
        while time.time() - start_time < max_wait_seconds:
            # 获取最新价格
            latest_data = ContextInfo.get_market_data(['close', 'open', 'high', 'low'], [g.code], period='1m', count=1)
            latest_price = latest_data['close'].iloc[-1]
            
            if original_position == "long":  # 等待反弹后开空
                if latest_price > best_price:
                    best_price = latest_price
                    print(f"发现更高价格: {best_price}，继续等待")
                
                # 检查是否达到目标价格
                if latest_price >= target_price:
                    price_met = True
                    print(f"价格已达到目标反弹位置: {latest_price} >= {target_price}")
                    
                    # 如果已经满足最小等待时间，立即返回
                    if time.time() > min_wait_end:
                        return latest_price
            else:  # 等待回调后开多
                if latest_price < best_price:
                    best_price = latest_price
                    print(f"发现更低价格: {best_price}，继续等待")
                
                # 检查是否达到目标价格
                if latest_price <= target_price:
                    price_met = True
                    print(f"价格已达到目标回调位置: {latest_price} <= {target_price}")
                    
                    # 如果已经满足最小等待时间，立即返回
                    if time.time() > min_wait_end:
                        return latest_price
            
            # 短暂休眠，避免频繁查询
            time.sleep(1)
        
        # 如果等待超时
        if price_met:
            print(f"已找到理想价格但等待时间已到，使用最后价格: {latest_price}")
            return latest_price
        else:
            print(f"等待超时，未找到理想价格，使用最佳记录价格: {best_price}")
            return best_price
            
    except Exception as e:
        print(f"等待最优反向开仓价格出错: {str(e)}")
        # 出错时直接返回当前价格
        return current_price

def wait_for_optimal_entry(ContextInfo, direction, current_price, max_wait_seconds=30):
    """等待最优普通开仓价格"""
    try:
        start_time = time.time()
        best_price = current_price
        atr_value = calculate_atr(ContextInfo) * 0.15  # 使用ATR的15%作为最小回调幅度
        
        # 获取红白线值作为参考
        price_5m = ContextInfo.get_market_data(['close','high','low','open','volume'], [g.code], period='5m', count=10)
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        
        # 设置目标价格
        if direction == "long":  # 开多仓
            target_dip_pct = Config.ENTRY_OPTIMAL_PCT  # 使用Config参数
            target_price = current_price * (1 - target_dip_pct)
            
            # 如果有红线，将其作为参考
            if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
                red_line = red_line_5m.iloc[-1]
                # 如果价格在红线上方，理想开多位置是回调接近红线
                if current_price > red_line and current_price < red_line * 1.01:  # 价格在红线上方1%以内
                    target_price = max(target_price, red_line * 1.0002)  # 略高于红线
        else:  # 开空仓
            target_bounce_pct = Config.ENTRY_OPTIMAL_PCT  # 使用Config参数
            target_price = current_price * (1 + target_bounce_pct)
            
            # 如果有白线，将其作为参考
            if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
                white_line = white_line_5m.iloc[-1]
                # 如果价格在白线下方，理想开空位置是反弹接近白线
                if current_price < white_line and current_price > white_line * 0.99:  # 价格在白线下方1%以内
                    target_price = min(target_price, white_line * 0.9998)  # 略低于白线
        
        # 设置最长等待时间的三分之一作为最小等待时间
        min_wait_seconds = max_wait_seconds / 3
        min_wait_end = start_time + min_wait_seconds
        price_met = False
        
        print(f"开始等待最优{direction}仓价格，最长等待{max_wait_seconds}秒")
        
        # 循环检查价格
        while time.time() - start_time < max_wait_seconds:
            # 获取最新价格
            latest_data = ContextInfo.get_market_data(['close', 'open', 'high', 'low'], [g.code], period='1m', count=1)
            latest_price = latest_data['close'].iloc[-1]
            
            if direction == "long":  # 等待回调后开多
                if latest_price < best_price:
                    best_price = latest_price
                    print(f"发现更低价格: {best_price}，继续等待")
                
                # 检查是否达到目标价格
                if latest_price <= target_price:
                    price_met = True
                    print(f"价格已达到目标回调位置: {latest_price} <= {target_price}")
                    
                    # 如果已经满足最小等待时间，立即返回
                    if time.time() > min_wait_end:
                        # 记录价格优化信息
                        log_price_optimization(direction, current_price, latest_price)
                        return latest_price
            else:  # 等待反弹后开空
                if latest_price > best_price:
                    best_price = latest_price
                    print(f"发现更高价格: {best_price}，继续等待")
                
                # 检查是否达到目标价格
                if latest_price >= target_price:
                    price_met = True
                    print(f"价格已达到目标反弹位置: {latest_price} >= {target_price}")
                    
                    # 如果已经满足最小等待时间，立即返回
                    if time.time() > min_wait_end:
                        # 记录价格优化信息
                        log_price_optimization(direction, current_price, latest_price)
                        return latest_price
            
            # 短暂休眠，避免频繁查询
            time.sleep(1)
        
        # 如果等待超时
        final_price = None
        if price_met:
            print(f"已找到理想价格但等待时间已到，使用最后价格: {latest_price}")
            final_price = latest_price
        else:
            print(f"等待超时，未找到理想价格，使用最佳记录价格: {best_price}")
            final_price = best_price
        
        # 记录价格优化信息
        log_price_optimization(direction, current_price, final_price)
        
        return final_price
            
    except Exception as e:
        print(f"等待最优普通开仓价格出错: {str(e)}")
        # 出错时直接返回当前价格
        return current_price

def print_position_details(current_price):
    # --- 新增：开仓价健壮性检查 ---
    fix_open_price(ContextInfo)
    # --- 原有持仓详情打印逻辑 ---
    if g.position != "none":
        position_type = "多单" if g.position == "long" else "空单"
        profit_pct = calculate_profit(current_price, g.open_price, g.position == "long")
        print(f"\n=== 当前持仓详情 ===")
        print(f"持仓类型: {position_type}")
        print(f"持仓手数: {g.position_size}")
        print(f"开仓价格: {g.open_price:.2f}")
        print(f"当前价格: {current_price:.2f}")
        print(f"浮动盈亏: {profit_pct:.2f}%")
        print(f"是否反向开仓: {'是' if g.is_reverse_trade else '否'}")
        
        # 添加ATR值和止盈止损线
        if hasattr(g, 'atr_value') and g.atr_value > 0:
            # 计算ATR止盈线
            atr_take_profit_line = g.open_price + g.atr_value * Config.ATR_TAKE_PROFIT_MULTIPLE if g.position == "long" else g.open_price - g.atr_value * Config.ATR_TAKE_PROFIT_MULTIPLE
            
            # 计算ATR点数
            profit_points = current_price - g.open_price if g.position == "long" else g.open_price - current_price
            atr_profit_ratio = profit_points / g.atr_value if g.atr_value > 0 else 0
            
            print(f"当前ATR值: {g.atr_value:.2f}")
            print(f"ATR止盈线: {atr_take_profit_line:.2f}")
            print(f"浮动盈亏(ATR倍数): {atr_profit_ratio:.2f}倍")
            print(f"ATR止盈触发倍数: {Config.ATR_TAKE_PROFIT_MULTIPLE}倍")

def check_atr_take_profit(ContextInfo, current_price):
    """
    检查是否触发ATR止盈条件
    """
    # --- 新增：开仓价健壮性检查 ---
    fix_open_price(ContextInfo)
    # --- 原有ATR止盈逻辑 ---
    # 如果没有持仓或未启用ATR止盈，直接返回
    if g.position == "none" or not Config.ATR_TAKE_PROFIT_ENABLE:
        return False
    
    # 使用统一的保护期检查函数
    in_protection, protection_msg = check_protection_period()
    if in_protection:
        print(f"ATR止盈检查: {protection_msg}，暂不触发ATR止盈")
        return False
    
    # 计算当前ATR值
    atr_value = g.atr_value
    
    # 计算浮动盈亏
    profit_points = current_price - g.open_price if g.position == "long" else g.open_price - current_price
    
    # 计算ATR止盈阈值
    atr_take_profit_threshold = atr_value * Config.ATR_TAKE_PROFIT_MULTIPLE
    
    # 检查是否触发ATR止盈
    if profit_points >= atr_take_profit_threshold:
        # 计算平仓手数
        close_size = int(g.position_size * Config.ATR_TAKE_PROFIT_PCT)
        
        # 确保至少平一手
        if close_size < 1:
            close_size = 1
            
        # 确保不超过持仓量
        if close_size > g.position_size:
            close_size = g.position_size
            
        log_trade("ATR止盈", current_price, calculate_profit(current_price, g.open_price, g.position == "long"))
        print(f"ATR止盈触发：当前盈利点数 {profit_points:.2f}，ATR值 {atr_value:.2f}，" 
              f"ATR倍数 {Config.ATR_TAKE_PROFIT_MULTIPLE}，阈值 {atr_take_profit_threshold:.2f}")
        
        # 执行平仓
        if g.position == "long":
            # 平多
            if g.future_account and g.future_code:
                g.future_account.sell_to_close(g.future_code, current_price, close_size)
            else:
                print("[警告] future_account或future_code未初始化，自动回退为passorder平仓！")
                passorder(7, 1101, account, g.code, 14, 0, close_size, '', 1, 'ATR止盈平多', ContextInfo)
        else:
            # 平空
            if g.future_account and g.future_code:
                g.future_account.buy_to_close(g.future_code, current_price, close_size)
            else:
                print("[警告] future_account或future_code未初始化，自动回退为passorder平仓！")
                passorder(9, 1101, account, g.code, 14, 0, close_size, '', 1, 'ATR止盈平空', ContextInfo)
            
        # 如果全部平仓，重置持仓状态
        if close_size >= g.position_size:
            g.position = "none"
            g.position_size = 0
            g.open_price = 0
            g.is_reverse_trade = False
            g.reverse_open_time = None
            print("ATR止盈全部平仓完成")
        else:
            # 部分平仓，更新持仓状态
            g.position_size -= close_size
            print(f"ATR止盈部分平仓完成，剩余持仓: {g.position_size}手")
            
        return True
        
    return False

# 添加统一的保护期检查函数
def check_protection_period():
    """
    统一检查持仓保护期
    
    返回:
    (bool, str): 返回一个元组，第一个元素表示是否在保护期内，第二个元素是保护期状态描述
    """
    if g.position == "none":
        return False, "无持仓"
        
    # 获取当前时间
    current_time = time.time()
    
    # 计算持仓时间
    if g.position == "long":
        if not hasattr(g, 'trace_time_long') or g.trace_time_long <= 0:
            g.trace_time_long = current_time - 300  # 默认设置为5分钟前
        
        holding_minutes = (current_time - g.trace_time_long) / 60
    else:  # short
        if not hasattr(g, 'trace_time_short') or g.trace_time_short <= 0:
            g.trace_time_short = current_time - 300  # 默认设置为5分钟前
        
        holding_minutes = (current_time - g.trace_time_short) / 60
    
    # 持仓时间防护：确保持仓时间在合理范围内
    if holding_minutes < 0:
        holding_minutes = 0
    elif holding_minutes > 60*24*7:  # 不超过一周
        holding_minutes = 60*24  # 最大值限制为1天
    
    # 检查是否是反向开仓
    if hasattr(g, 'is_reverse_trade') and g.is_reverse_trade:
        # 反向开仓使用独立的保护时间
        if holding_minutes < Config.REVERSE_PROTECTION_MINUTES:
            return True, f"处于反向开仓保护期({holding_minutes:.1f}/{Config.REVERSE_PROTECTION_MINUTES}分钟)"
        else:
            return False, f"已超过反向开仓保护期({holding_minutes:.1f}>{Config.REVERSE_PROTECTION_MINUTES}分钟)"
    else:
        # 普通开仓使用原有保护时间
        if holding_minutes < Config.NEW_POSITION_PROTECTION_MINUTES:
            return True, f"处于开仓保护期({holding_minutes:.1f}/{Config.NEW_POSITION_PROTECTION_MINUTES}分钟)"
        else:
            return False, f"已超过开仓保护期({holding_minutes:.1f}>{Config.NEW_POSITION_PROTECTION_MINUTES}分钟)"

def log_price_optimization(direction, original_price, optimized_price):
    """
    记录价格优化信息的简单函数，只输出日志，不进行复杂分析
    
    参数:
    direction - 交易方向，"long"或"short"
    original_price - 原始价格
    optimized_price - 优化后价格
    """
    try:
        # 计算价格差异
        if direction == "long":
            price_diff = original_price - optimized_price
            better_text = "更低" if price_diff > 0 else "更高"
        else:
            price_diff = optimized_price - original_price
            better_text = "更高" if price_diff > 0 else "更低"
        
        # 计算价格差异百分比
        price_diff_pct = abs(price_diff / original_price * 100)
        
        # 输出简单的日志信息
        print(f"价格优化结果 - {direction}方向:")
        print(f"  原始价格: {original_price:.2f}")
        print(f"  优化价格: {optimized_price:.2f}")
        print(f"  价格差异: {price_diff:.2f} ({price_diff_pct:.4f}%)")
        print(f"  优化结果: 获得{better_text}价格")
        
    except Exception as e:
        # 确保即使出错也不影响主流程
        print(f"记录价格优化信息出错: {str(e)}")

# === 新增：统一修复开仓价函数 ===
def fix_open_price(ContextInfo):
    """
    检查并修复g.open_price，优先用成交价兜底，无则用市价，再无则给出明确报错。
    """
    if not hasattr(g, 'open_price') or pd.isna(g.open_price) or g.open_price <= 0:
        try:
            # 兼容ContextInfo无get_deal_data的情况
            get_deal_data = getattr(ContextInfo, 'get_deal_data', None)
            if callable(get_deal_data):
                deals = ContextInfo.get_deal_data(account, g.code)
                open_deals = [d for d in deals if getattr(d, 'm_nOffsetFlag', None) in [48, 49]]
                if open_deals:
                    g.open_price = round(open_deals[-1].m_dPrice, 1)
                    print(f"[修复] 用最近成交价兜底: {g.open_price}")
                    return
            # 若无成交记录或无get_deal_data，尝试用市价
            get_market_data = getattr(ContextInfo, 'get_market_data', None)
            if callable(get_market_data):
                price = ContextInfo.get_market_data(['close'], [g.code], period='1m', count=1)
                g.open_price = float(price['close'].iloc[-1])
                print(f"[修复] 用市价兜底: {g.open_price}")
                return
            print(f"[严重警告] ContextInfo对象无get_deal_data和get_market_data方法，无法修复开仓价！")
        except Exception as e:
            print(f"[严重警告] 仍无法修复开仓价，请人工核查！错误: {e}")

