def deal_callback(event):
    """
    成交回调函数, 处理交易执行
    """
    print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][成交回调] 收到成交回调: {event}")
    
    try:
        # 确保事件包含必要数据
        if not all(key in event for key in ['security', 'offset']):
            print("成交回调数据不完整")
            return {'status': 'error', 'msg': "成交回调数据不完整"}
        
        # 检查是否是我们的策略订单
        if not (event['security'] == g.call_one or event['security'] == g.put_one or event['security'] == g.curr_hold):
            print(f"非策略管理的合约成交，忽略: {event['security']}")
            return {'status': 'skip', 'msg': "非策略管理的合约"}
        
        # 开仓处理
        if event['offset'] == 'open':
            # 更新成交价格
            if 'price' in event and event['price'] > 0:
                g.open_price = event['price']
                g.hold_price = event['price']
                print(f"开仓成交，价格更新为: {g.open_price}")
            
            # 更新持仓状态
            if event['side'] == 'buy':
                g.hold = 1  # 多头持仓
                g.curr_hold = event['security']
                g.call_one = event['security']
                print(f"开多成交: {g.curr_hold}")
            elif event['side'] == 'sell':
                g.hold = -1  # 空头持仓
                g.curr_hold = event['security']
                g.put_one = event['security']
                print(f"开空成交: {g.curr_hold}")
            
            # 更新持仓手数
            g.remaining_hands = event['volume'] if 'volume' in event else hand
            print(f"开仓成交，剩余手数: {g.remaining_hands}")
            
            # 重置平仓比例
            g.closed_ratio = 0
            print(f"开仓成交，重置已平仓比例为0")
        
        # 平仓处理
        elif event['offset'] == 'close':
            # 确保持仓存在
            if g.hold == 0:
                print("警告: 收到平仓回报，但当前无持仓状态")
                # 尝试根据成交合约恢复持仓方向
                if event['security'] == g.call_one:
                    g.hold = 1
                    g.curr_hold = g.call_one
                elif event['security'] == g.put_one:
                    g.hold = -1
                    g.curr_hold = g.put_one
            
            # 更新剩余手数
            close_volume = event['volume'] if 'volume' in event else 0
            g.remaining_hands -= close_volume
            
            # 更新已平仓比例
            if g.remaining_hands <= 0:
                g.closed_ratio = 1.0  # 全部平仓
            else:
                # 根据剩余手数计算已平仓比例
                g.closed_ratio = round(1 - (g.remaining_hands / hand), 2)
            
            print(f"平仓成交，减少手数: {close_volume}, 剩余手数: {g.remaining_hands}, 已平仓比例: {g.closed_ratio}")
            
            # 如果全部平仓，更新状态
            if g.remaining_hands <= 0:
                g.hold = 0
                g.open_price = 0
                g.hold_price = 0
                g.remaining_hands = 0
                g.closed_ratio = 1.0
                print("全部平仓，重置持仓状态")
        
        # 检查状态一致性
        check_and_fix_state()
        
        # 记录当前持仓状态
        print(f"当前持仓状态: hold={g.hold}, "
              f"curr_hold={g.curr_hold}, "
              f"remaining_hands={g.remaining_hands}, "
              f"closed_ratio={g.closed_ratio}, "
              f"open_price={g.open_price}")
        
        return {'status': 'success', 'msg': "成交回调处理完成"}
    
    except Exception as e:
        print(f"成交回调处理异常: {e}")
        import traceback
        traceback.print_exc()
        # 尝试恢复状态一致性
        try:
            check_and_fix_state()
        except Exception as recovery_error:
            print(f"状态恢复失败: {recovery_error}")
        
        return {'status': 'error', 'msg': f"成交回调处理异常: {str(e)}"} 