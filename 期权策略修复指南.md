# 期权策略买方修复指南

根据错误日志和代码分析，期权策略买方存在以下问题需要修复：

## 1. 变量名不一致问题

在代码中定义了`stoploss_ratio`（小写），但在持仓管理部分使用了`STOPLOSS_RATIO`（大写），导致变量未定义错误。

**修复方法**：
将所有`STOPLOSS_RATIO`替换为`stoploss_ratio`，修改位置在handlebar函数中的止损逻辑部分：

```python
# 修改前
if hold_ratio <= STOPLOSS_RATIO and g.remaining_hands > 0:
    # 止损代码...

# 修改后
if hold_ratio <= stoploss_ratio and g.remaining_hands > 0:
    # 止损代码...
```

## 2. 优化deal_callback函数

成交回调函数需要优化，确保在成交后正确更新持仓信息和开仓价格：

```python
def deal_callback(event):
    """
    成交回调函数 - 优化版
    """
    try:
        # 检查错误码
        if hasattr(event, 'error_code') and event.error_code != 0:
            print(f"交易失败: {event.error_msg}")
            return

        # 记录当前时间用于日志
        current_time = time.strftime('%Y-%m-%d %H:%M:%S')
        
        # === 处理不同格式的成交回调 ===
        instrument_code = None  # 合约代码
        trade_price = 0         # 成交价格
        trade_volume = 0        # 成交数量
        trade_direction = 0     # 交易方向：1=买入/开仓，2=卖出/平仓
        is_open_position = False  # 是否为开仓操作
        
        # 格式1：原有格式的成交回调
        if hasattr(event, 'm_strRemark') and hasattr(event, 'm_strInstrumentID'):
            print(f"[{current_time}][成交] 收到原格式成交回调: {event.m_strInstrumentID}, 价格:{event.m_dPrice}")
            
            # 获取交易所代码
            market = {"SHFE":'SF', "CZCE":'ZF', "DCE":'DF', "CFFEX":'IF', "SH":'SHO', "SZ":'SZO'}.get(
                event.m_strExchangeID, event.m_strExchangeID)
            
            # 构建完整合约代码
            instrument_code = event.m_strInstrumentID + '.' + market
            trade_price = round(event.m_dPrice, 4)
            trade_volume = event.m_dVolume if hasattr(event, 'm_dVolume') else hand
            
            # 判断交易方向和开平仓类型
            is_open_position = (event.m_nOffsetFlag == 48)  # 48表示开仓
            
            # 判断多空方向
            if is_open_position:
                if '开多' in event.m_strRemark:
                    trade_direction = 1  # 买入开仓
                elif '开空' in event.m_strRemark:
                    trade_direction = 2  # 卖出开仓
            else:
                trade_direction = 2 if g.hold > 0 else 1  # 平仓方向与持仓方向相反
        
        # 格式2：新格式的成交回调
        elif hasattr(event, 'instrument') and hasattr(event, 'price'):
            print(f"[{current_time}][成交] 收到新格式成交回调: {event.order_id} {event.instrument} {event.volume}手 价格:{event.price}")
            
            instrument_code = event.instrument
            trade_price = round(event.price, 4)
            trade_volume = event.volume
            trade_direction = event.direction if hasattr(event, 'direction') else 0
            
            # 新格式中，direction=1表示买入，direction=2表示卖出
            # 根据当前持仓状态判断是开仓还是平仓
            if trade_direction == 1:  # 买入
                is_open_position = (g.hold == 0)  # 如果当前无持仓，则为开仓
            elif trade_direction == 2:  # 卖出
                is_open_position = False  # 卖出一般为平仓
        
        # 如果无法识别成交回调格式，则退出
        if instrument_code is None or trade_price <= 0 or trade_volume <= 0:
            print(f"[{current_time}][警告] 无法识别的成交回调格式")
            return
            
        # === 更新持仓状态 ===
        # 开仓操作
        if is_open_position:
            # 确定持仓方向
            if trade_direction == 1:  # 买入开仓
                g.hold = 1 if '购' in instrument_code else -1  # 根据合约类型判断多空
            else:  # 卖出开仓
                g.hold = -1
                
            g.curr_hold = instrument_code
            g.open_price = trade_price
            g.hold_price = trade_price
            g.remaining_hands = trade_volume
            g.max_price = trade_price  # 初始化最高价
            g.min_price = trade_price  # 初始化最低价
            
            # 重置止盈状态
            g.profit_taken_1 = False
            g.profit_taken_2 = False
            g.profit_taken_3 = False
            g.profit_taken_1_short = False
            g.profit_taken_2_short = False
            g.profit_taken_3_short = False
            
            print(f"[{current_time}][成交] ✅ 开仓成功: {g.curr_hold}, 方向:{g.hold}, 价格:{g.open_price}, 数量:{g.remaining_hands}手")
            
        # 平仓操作
        else:
            # 减少持仓数量
            g.remaining_hands -= trade_volume
            
            # 检查是否全部平仓
            if g.remaining_hands <= 0:
                g.hold = 0
                g.curr_hold = None
                g.open_price = 0
                g.hold_price = 0
                g.remaining_hands = 0
                g.max_price = 0
                g.min_price = 9999999
                
                # 重置止盈状态
                g.profit_taken_1 = False
                g.profit_taken_2 = False
                g.profit_taken_3 = False
                g.profit_taken_1_short = False
                g.profit_taken_2_short = False
                g.profit_taken_3_short = False
                
                print(f"[{current_time}][成交] ✅ 全部平仓完成")
            else:
                print(f"[{current_time}][成交] ✅ 部分平仓: 剩余{g.remaining_hands}手")
        
        # 立即保存成交记录到全局状态
        if not hasattr(g, 'deal_records'):
            g.deal_records = []
        
        # 统一格式记录交易
        g.deal_records.append({
            'time': current_time,
            'instrument': instrument_code,
            'direction': trade_direction,
            'price': trade_price,
            'volume': trade_volume,
            'order_id': event.order_id if hasattr(event, 'order_id') else (
                event.m_strOrderSysID if hasattr(event, 'm_strOrderSysID') else '')
        })
        
        # 打印当前持仓状态
        print(f"[{current_time}][成交] 当前持仓状态: hold={g.hold}, 持仓代码={g.curr_hold}, 开仓价={g.open_price}, 剩余手数={g.remaining_hands}")
        
        # 强制同步持仓状态
        if hasattr(g, 'context'):
            try:
                check_and_sync_positions(g.context, force_sync=True)
            except Exception as sync_error:
                print(f"[{current_time}][警告] 强制同步持仓状态失败: {sync_error}")
        
    except Exception as e:
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][错误] 成交回调处理异常: {e}")
        import traceback
        traceback.print_exc()
```

## 3. 优化check_and_sync_positions函数

增强持仓同步机制，确保能正确获取持仓信息和开仓价格：

```python
def check_and_sync_positions(ContextInfo, force_sync=False):
    """
    检查并同步持仓状态 - 增强版
    """
    try:
        current_time = time.strftime('%Y-%m-%d %H:%M:%S')
        print(f"[{current_time}][同步] {'强制' if force_sync else ''}开始同步持仓状态...")
        
        # 保存context以便在成交回调中使用
        g.context = ContextInfo
        
        # 获取当前的交易明细
        account_id = account if 'account' in globals() else ContextInfo.accID
        
        # 记录当前状态
        old_hold = g.hold
        old_code = g.curr_hold
        old_price = g.open_price
        old_hands = g.remaining_hands
        
        # 标记是否找到持仓
        has_position = False
        
        # ===== 方法1: 直接使用交易API获取持仓 =====
        try:
            # 尝试使用API直接查询持仓
            print(f"[{current_time}][同步] 方法1: 尝试使用交易API直接查询持仓")
            
            # 尝试使用ContextInfo.get_positions
            if hasattr(ContextInfo, 'get_positions'):
                positions = ContextInfo.get_positions()
                if positions and len(positions) > 0:
                    for pos in positions:
                        if hasattr(pos, 'volume') and pos.volume > 0:
                            g.curr_hold = pos.code
                            g.hold = 1 if hasattr(pos, 'side') and pos.side > 0 else -1
                            g.remaining_hands = pos.volume
                            g.open_price = pos.cost if hasattr(pos, 'cost') and pos.cost > 0 else 0
                            
                            print(f"[{current_time}][同步] ✅ 通过API获取到持仓: {g.curr_hold}, 方向:{g.hold}, 数量:{g.remaining_hands}手, 成本价:{g.open_price}")
                            has_position = True
                            break
            
            # 尝试使用其他API获取持仓
            if not has_position and 'get_stock_positions' in globals():
                try:
                    positions = get_stock_positions(account_id)
                    if positions and len(positions) > 0:
                        for pos in positions:
                            if hasattr(pos, 'volume') and pos.volume > 0:
                                g.curr_hold = pos.code
                                g.hold = 1 if '购' in pos.code else -1
                                g.remaining_hands = pos.volume
                                g.open_price = pos.cost if hasattr(pos, 'cost') and pos.cost > 0 else 0
                                
                                print(f"[{current_time}][同步] ✅ 通过get_stock_positions获取到持仓: {g.curr_hold}, 方向:{g.hold}, 数量:{g.remaining_hands}手, 成本价:{g.open_price}")
                                has_position = True
                                break
                except Exception as e:
                    print(f"[{current_time}][警告] get_stock_positions异常: {e}")
        except Exception as e:
            print(f"[{current_time}][警告] 交易API获取持仓异常: {e}")
        
        # ===== 方法2: 从成交回调记录中恢复持仓状态 =====
        if not has_position and hasattr(g, 'deal_records') and g.deal_records:
            print(f"[{current_time}][同步] 方法2: 从成交记录中恢复持仓状态")
            
            # 按时间排序，确保最新的交易记录在最后
            sorted_records = sorted(g.deal_records, key=lambda x: x['time'])
            
            # 计算每个合约的净持仓
            position_map = {}
            price_map = {}  # 记录每个合约的开仓价格
            
            for record in sorted_records:
                if not all(k in record for k in ['instrument', 'direction', 'volume', 'price']):
                    continue
                    
                instrument = record['instrument']
                direction = record['direction']
                volume = record['volume']
                price = record['price']
                
                if instrument not in position_map:
                    position_map[instrument] = 0
                    price_map[instrument] = price
                
                # 更新持仓数量
                if direction == 1:  # 买入/开仓
                    # 如果之前没有持仓，记录这次的价格为开仓价
                    if position_map[instrument] <= 0:
                        price_map[instrument] = price
                    position_map[instrument] += volume
                elif direction == 2:  # 卖出/平仓
                    position_map[instrument] -= volume
            
            # 查找有净持仓的合约
            for instrument, net_volume in position_map.items():
                if net_volume > 0:
                    g.curr_hold = instrument
                    g.hold = 1 if '购' in instrument else -1
                    g.remaining_hands = net_volume
                    g.open_price = price_map[instrument]
                    
                    print(f"[{current_time}][同步] ✅ 从成交记录恢复持仓: {g.curr_hold}, 方向:{g.hold}, 数量:{g.remaining_hands}手, 开仓价:{g.open_price}")
                    has_position = True
                    break
        
        # ===== 方法3: 通过交易明细获取持仓 =====
        if not has_position:
            try:
                print(f"[{current_time}][同步] 方法3: 通过交易明细获取持仓")
                orders = get_trade_detail_data(account_id, 'STOCK', 'STOCK_OPTION')
                
                if orders and len(orders) > 0:
                    print(f"[{current_time}][同步] 获取到{len(orders)}个交易明细")
                    
                    # 过滤已成交的开仓订单
                    active_orders = [o for o in orders if hasattr(o, 'm_nOrderStatus') and o.m_nOrderStatus == 56 
                                  and hasattr(o, 'm_nOffsetFlag') and o.m_nOffsetFlag == 48]
                    
                    if active_orders and len(active_orders) > 0:
                        # 按时间排序，获取最新的订单
                        latest_order = sorted(active_orders, 
                                           key=lambda o: o.m_strTradeTime if hasattr(o, 'm_strTradeTime') else '0', 
                                           reverse=True)[0]
                        
                        # 获取交易所代码
                        market = {"SHFE":'SF', "CZCE":'ZF', "DCE":'DF', "CFFEX":'IF', "SH":'SHO', "SZ":'SZO'}.get(
                            latest_order.m_strExchangeID, latest_order.m_strExchangeID)
                        
                        # 构建完整合约代码
                        k = latest_order.m_strInstrumentID + '.' + market
                        
                        g.curr_hold = k
                        g.hold = 1 if '购' in k or '开多' in latest_order.m_strRemark else -1
                        g.remaining_hands = latest_order.m_nVolume if hasattr(latest_order, 'm_nVolume') else hand
                        
                        # 获取成交价格
                        if hasattr(latest_order, 'm_dTradedPrice') and latest_order.m_dTradedPrice > 0:
                            g.open_price = latest_order.m_dTradedPrice
                        elif hasattr(latest_order, 'm_dLimitPrice') and latest_order.m_dLimitPrice > 0:
                            g.open_price = latest_order.m_dLimitPrice
                        else:
                            # 如果无法获取价格，尝试使用get_option_price
                            g.open_price = get_option_price(k)
                        
                        g.hold_price = g.open_price
                        
                        print(f"[{current_time}][同步] ✅ 从交易明细恢复持仓: {g.curr_hold}, 方向:{g.hold}, 数量:{g.remaining_hands}手, 开仓价:{g.open_price}")
                        has_position = True
            except Exception as e:
                print(f"[{current_time}][警告] 获取交易明细异常: {e}")
        
        # ===== 方法4: 使用现有持仓状态 =====
        if not has_position and g.hold != 0 and g.curr_hold and g.remaining_hands > 0:
            print(f"[{current_time}][同步] 方法4: 使用现有持仓状态: {g.curr_hold}, 方向:{g.hold}, 数量:{g.remaining_hands}手")
            
            # 如果有持仓但没有开仓价，尝试获取
            if g.open_price <= 0 and g.curr_hold:
                # 尝试多种方式获取价格
                price = get_option_price(g.curr_hold)
                if price > 0:
                    g.open_price = price
                    g.hold_price = price
                    print(f"[{current_time}][同步] ✅ 更新开仓价格为: {g.open_price}")
            
            has_position = True
        
        # ===== 状态一致性检查 =====
        # 如果没有找到持仓但全局状态显示有持仓，则重置状态
        if not has_position and g.hold != 0:
            print(f"[{current_time}][同步] ⚠️ 未找到持仓但全局状态显示有持仓，重置状态")
            g.hold = 0
            g.curr_hold = None
            g.open_price = 0
            g.hold_price = 0
            g.remaining_hands = 0
            g.profit_taken_1 = False
            g.profit_taken_2 = False
            g.profit_taken_3 = False
            g.profit_taken_1_short = False
            g.profit_taken_2_short = False
            g.profit_taken_3_short = False
        
        # 如果持仓状态有变化，打印日志
        if (g.hold != old_hold or g.curr_hold != old_code or 
            g.open_price != old_price or g.remaining_hands != old_hands):
            print(f"[{current_time}][同步] 持仓状态已更新:")
            print(f"  方向: {old_hold} -> {g.hold}")
            print(f"  代码: {old_code} -> {g.curr_hold}")
            print(f"  价格: {old_price} -> {g.open_price}")
            print(f"  手数: {old_hands} -> {g.remaining_hands}")
        else:
            print(f"[{current_time}][同步] 持仓状态无变化")
        
        # 执行持仓状态一致性检查
        print(f"[{current_time}][同步] 执行持仓状态一致性检查...")
        check_position_consistency()
        print(f"[{current_time}][同步] 持仓状态一致性检查完成")
        
        return has_position
        
    except Exception as e:
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}][错误] 同步持仓状态异常: {e}")
        import traceback
        traceback.print_exc()
        return False
```

## 4. 优化get_option_price函数

增加多种获取期权价格的方式，确保能正确获取价格：

```python
def get_option_price(option_code):
    """
    获取期权当前市场价格 - 增强版
    提供多种备选方式获取期权价格，确保能获取到有效价格
    如果所有方法都失败，返回默认值0.05
    """
    if not option_code:
        print(f"[警告] 期权代码为空，无法获取价格")
        return 0.05
        
    try:
        # 方法1: 使用xtquant.xtdata.get_full_tick获取最新行情
        try:
            from xtquant.xtdata import get_full_tick
            tick_data = get_full_tick([option_code])
            if option_code in tick_data and 'lastPrice' in tick_data[option_code]:
                price = tick_data[option_code]['lastPrice']
                if price > 0:
                    print(f"通过get_full_tick获取期权{option_code}价格成功: {price}")
                    return price
        except Exception as e1:
            print(f"通过get_full_tick获取期权价格失败: {e1}")
        
        # 方法2: 使用交易上下文获取价格
        try:
            from xtquant import xtconsole
            ctx = xtconsole.get_trading_context()
            price = ctx.get_last_price(option_code)
            if price > 0:
                print(f"通过交易上下文获取期权{option_code}价格成功: {price}")
                return price
        except Exception as e2:
            print(f"通过交易上下文获取期权价格失败: {e2}")
        
        # 方法3: 尝试使用ContextInfo.get_full_tick获取
        if hasattr(g, 'context') and g.context:
            try:
                full = g.context.get_full_tick([option_code])
                if option_code in full and 'lastPrice' in full[option_code]:
                    price = full[option_code]['lastPrice']
                    if price > 0:
                        print(f"通过ContextInfo.get_full_tick获取期权{option_code}价格成功: {price}")
                        return price
            except Exception as e3:
                print(f"通过ContextInfo.get_full_tick获取期权价格失败: {e3}")
        
        # 方法4: 尝试从最新K线获取价格
        if hasattr(g, 'context') and g.context:
            try:
                klines = g.context.get_market_data_ex(['close'], [option_code], period='1m', count=1)
                if option_code in klines and len(klines[option_code]) > 0:
                    price = klines[option_code]['close'].iloc[-1]
                    if price > 0:
                        print(f"通过K线获取期权{option_code}价格成功: {price}")
                        return price
            except Exception as e4:
                print(f"通过K线获取期权价格失败: {e4}")
        
        # 方法5: 尝试从交易明细中获取价格
        try:
            if 'account' in globals() and 'get_trade_detail_data' in globals():
                orders = get_trade_detail_data(account, 'STOCK', 'STOCK_OPTION')
                if orders:
                    for order in orders:
                        if hasattr(order, 'm_strInstrumentID') and option_code.startswith(order.m_strInstrumentID):
                            if hasattr(order, 'm_dTradedPrice') and order.m_dTradedPrice > 0:
                                print(f"通过交易明细获取期权{option_code}价格成功: {order.m_dTradedPrice}")
                                return order.m_dTradedPrice
        except Exception as e5:
            print(f"通过交易明细获取期权价格失败: {e5}")
        
        # 如果以上方法都失败，返回默认值
        print(f"[警告] 无法获取期权{option_code}价格，使用默认价格0.05")
        return 0.05
    except Exception as e:
        print(f"获取期权价格失败: {e}")
        return 0.05
```

## 5. 修复全局变量声明

确保在handlebar函数开始处正确声明全局变量：

```python
def handlebar(ContextInfo):
    """
    处理市场数据更新的主函数
    """
    try:
        # 声明使用全局变量
        global market_condition, stoploss_ratio
        
        # 检查和修复状态
        check_and_fix_state()
        
        # 同步持仓状态 - 每30秒同步一次，避免频繁查询
        current_time = time.time()
        if current_time - g.last_sync_time > 30:
            check_and_sync_positions(ContextInfo)
            g.last_sync_time = current_time
```

## 修复步骤总结

1. 修复变量名不一致问题：将`STOPLOSS_RATIO`替换为`stoploss_ratio`
2. 优化deal_callback函数：确保成交后正确更新持仓信息和开仓价格
3. 优化check_and_sync_positions函数：增强持仓同步机制
4. 优化get_option_price函数：增加多种获取期权价格的方式
5. 修复全局变量声明：在handlebar函数中添加必要的全局变量声明

实施这些修改后，策略应该能够正确地跟踪持仓状态，并执行止盈止损操作。 