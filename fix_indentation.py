#!/usr/bin/env python
# coding: utf-8

"""
修复Python文件缩进，将所有制表符(\t)转换为4个空格
"""

import re
import sys
import os

def fix_indentation(filename):
    """
    将文件中的制表符替换为4个空格
    """
    print(f"正在处理文件: {filename}")
    
    # 读取文件内容
    with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # 替换制表符为4个空格
    fixed_content = content.replace('\t', '    ')
    
    # 写回文件
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"文件修复完成: {filename}")

def convert_file_encoding(input_file, output_file):
    try:
        # 尝试用不同的编码读取文件
        encodings = ['utf-8', 'gbk', 'cp936', 'gb2312', 'gb18030']
        
        for encoding in encodings:
            try:
                with open(input_file, 'r', encoding=encoding) as f:
                    content = f.read()
                
                # 成功读取，写入utf-8编码的文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"文件转换成功：{input_file} -> {output_file}，原编码：{encoding}")
                return True
            except UnicodeDecodeError:
                continue
        
        print(f"无法转换文件：{input_file}，尝试了所有可能的编码")
        return False
    except Exception as e:
        print(f"转换文件时出错：{e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("用法: python fix_indentation.py [文件名]")
        sys.exit(1)
    
    filename = sys.argv[1]
    fix_indentation(filename)

    input_file = "多腿期权.py"
    output_file = "multileg_options.py"
    
    if convert_file_encoding(input_file, output_file):
        # 尝试运行转换后的文件
        print("尝试运行转换后的文件...")
        try:
            os.system(f"python {output_file}")
        except Exception as e:
            print(f"运行文件时出错：{e}")

    # 读取文件内容
    try:
        with open('股票策略.py', 'r', encoding='gbk') as f:
            lines = f.readlines()
        print("成功使用GBK编码读取文件")
    except:
        try:
            with open('股票策略.py', 'r', encoding='gb2312') as f:
                lines = f.readlines()
            print("成功使用GB2312编码读取文件")
        except:
            try:
                with open('股票策略.py', 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                print("成功使用UTF-8编码读取文件")
            except Exception as e:
                print(f"无法读取文件: {e}")
                exit(1)

    # 修复第一个问题：删除重复的价格检查
    i = 0
    while i < len(lines):
        if "# 检查价格是否低于40元" in lines[i]:
            # 删除这一行和接下来的两行
            del lines[i:i+3]
            break
        i += 1

    # 修复第二个问题：修正缩进
    i = 0
    while i < len(lines):
        if "print(f\"二次筛选结果 - 选择评分最高的3只股票:\")" in lines[i] and lines[i].startswith("                "):
            # 修复这一行和接下来几行的缩进
            lines[i] = lines[i].replace("                ", "            ")
            
            # 修复接下来的几行
            j = i + 1
            while j < len(lines) and j < i + 15:  # 检查接下来的15行
                if "for i, (s, score, price)" in lines[j] or "stocks = top_stocks" in lines[j]:
                    lines[j] = lines[j].replace("                ", "            ")
                elif "try:" in lines[j] or "except:" in lines[j]:
                    lines[j] = lines[j].replace("                    ", "                ")
                elif "stock_name =" in lines[j] or "signal_count =" in lines[j] or "print(f\"  {i+1}" in lines[j]:
                    lines[j] = lines[j].replace("                        ", "                    ")
                
                # 如果找到了stocks = top_stocks，说明这部分处理完成
                if "stocks = top_stocks" in lines[j]:
                    break
                j += 1
            break
        i += 1

    # 保存修改后的文件
    try:
        with open('股票策略_fixed.py', 'w', encoding='gbk') as f:
            f.writelines(lines)
        print("成功保存修复后的文件为 股票策略_fixed.py")
    except Exception as e:
        print(f"保存文件时出错: {e}")
        try:
            with open('股票策略_fixed.py', 'w', encoding='utf-8') as f:
                f.writelines(lines)
            print("成功使用UTF-8编码保存修复后的文件")
        except Exception as e:
            print(f"保存文件时出错: {e}")
            exit(1)

    print("完成修复，请尝试运行 股票策略_fixed.py") 