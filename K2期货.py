#encoding:gbk

"""
期货策略 - 红白线系统多空双向交易
策略逻辑：
1. 做多：突破5分钟白线或回调到红线支撑位
2. 做空：跌破5分钟红线或回调到白线压力位
3. 止盈止损：5分钟红白线止损，动态止盈
4. 时间过滤：避开收盘前和夜盘收盘前的交易
"""

# ====== 可调整的策略参数 ======
class Config:
    # 开仓参数
    OPEN_LONG_MAX = 3      # 最大开多仓位数
    OPEN_SHORT_MAX = 3     # 最大开空仓位数
    TRADE_HANDS = 1        # 每次交易手数
    SLEEP_TIME = 10        # 开仓间隔时间(分钟)
    
    # 分批止盈设置
    TAKE_PROFIT_1 = 0.2   # 第一档止盈比例% (从1.0改为0.25)
    TAKE_PROFIT_2 = 0.4    # 第二档止盈比例% (从1.5改为0.5)
    TAKE_PROFIT_3 = 0.6    # 第三档止盈比例% (从2.0改为1.0)
    PROFIT_PCT_1 = 0.3     # 第一档止盈比例
    PROFIT_PCT_2 = 0.3     # 第二档止盈比例
    PROFIT_PCT_3 = 0.4     # 第三档止盈比例
    
    # 成交量参数
    VOLUME_THRESHOLD_LONG = 1.2  # 做多成交量阈值
    
    # 添加与55均线距离阈值参数
    MA55_DISTANCE_THRESHOLD = 0.005  # 价格与55均线的最大偏离百分比，默认0.5%
    
    # 尾盘清仓时间设置
    DAY_CLOSE_HOUR = 14      # 日盘收盘小时
    DAY_CLOSE_MINUTE = 56    # 日盘清仓分钟
    NIGHT_CLOSE_HOUR = 22    # 夜盘收盘小时
    NIGHT_CLOSE_MINUTE = 56  # 夜盘清仓分钟
    
    # 开盘禁止交易时间设置（分钟）
    DAY_OPEN_NO_TRADE_MINUTES = 5    # 日盘开盘后禁止交易分钟数
    NIGHT_OPEN_NO_TRADE_MINUTES = 5  # 夜盘开盘后禁止交易分钟数
    

import math
import time
import pandas as pd
import numpy as np
import datetime

def log_trade(action, price, profit=None):
    """记录交易信息"""
    timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
    if profit is not None:
        print(f"[{timestamp}] {action}: 价格={price:.2f}, 收益={profit:.2f}%")
    else:
        print(f"[{timestamp}] {action}: 价格={price:.2f}")

def calculate_profit(current_price, open_price, is_long=True):
    """
    计算收益率
    
    参数:
    current_price: 当前价格
    open_price: 开仓价格
    is_long: 是否为多头持仓
    
    返回:
    收益率(%)
    """
    if is_long:
        return (current_price/open_price - 1) * 100
    return (open_price/current_price - 1) * 100

class G():
    def __init__(self):
        # 持仓状态
        self.position = "none"  # "none", "long", "short"
        self.hold_price = 0     # 持仓最高/最低价
        self.open_price = 0     # 开仓价格
        
        # 交易计数
        self.buy_long = 0       # 做多次数
        self.buy_short = 0      # 做空次数
        
        # 做多止盈标记
        self.profit_taken_1 = False
        self.profit_taken_2 = False  
        self.profit_taken_3 = False
        
        # 做空止盈标记
        self.profit_taken_1_short = False
        self.profit_taken_2_short = False
        self.profit_taken_3_short = False
        
        # 其他状态
        self.trace_time_long = 0  # 上次做多交易时间
        self.trace_time_short = 0  # 上次做空交易时间
        self.opened_t = []        # 已开仓的时间点
        self.hold_code = None     # 持仓代码
        self.code = None          # 当前代码
        self.position_size = 0    # 持仓数量记录
        self.sysid = {}           # 订单系统ID
        self.cover = 1            # 尾盘平仓标记
        self.info = None          # 合约信息
        

# 实例化全局变量g
g = G()

def init(ContextInfo):
    """
    初始化函数 - 在策略启动时执行一次
    
    参数:
    ContextInfo: 上下文信息
    """
    global g  # 声明使用全局变量g
    
    # 设置交易账户
    ContextInfo.set_account(account)
    
    # 获取市场代码
    market = ContextInfo.market
    market = market.replace("SHFE",'SF').replace("CZCE",'ZF').replace("DCE",'DF').replace("CFFEX",'IF')
    
    # 获取合约代码
    code = ContextInfo.stockcode + '.' + ContextInfo.market
    
    # 自动获取主力合约
    g.code = ContextInfo.get_main_contract(code) + '.' + market
    g.market = market
    
    # 获取合约信息
    g.info = ContextInfo.get_instrumentdetail(g.code)
    
    # 初始化尾盘清仓标志
    g.cover = 0
    
    # 初始化上次交易时间，设置为当前时间减去开仓间隔时间，确保策略启动后不会立即开仓
    current_time = time.time()
    g.trace_time_long = current_time - Config.SLEEP_TIME * 30  # 设置为较早的时间，但不会立即触发
    g.trace_time_short = current_time - Config.SLEEP_TIME * 30
    
    # 初始化其他变量
    g.position = "none"  # 当前持仓方向
    g.position_size = 0  # 当前持仓手数
    g.buy_long = 0  # 开多次数
    g.buy_short = 0  # 开空次数
    g.hold_price = 0  # 持仓价格
    g.open_price = 0  # 开仓价格
    g.hold_code = ""  # 持仓合约
    g.opened_t = []  # 已开仓时间
    g.sysid = {}  # 系统ID
    
    # 初始化止盈标记
    g.profit_taken_1 = False
    g.profit_taken_2 = False
    g.profit_taken_3 = False
    g.profit_taken_1_short = False
    g.profit_taken_2_short = False
    g.profit_taken_3_short = False
    
    # 根据品种调整参数
    if "MA" in g.code:  # 甲醇
        g.volume_threshold = Config.VOLUME_THRESHOLD_LONG
        g.ma_period = 55
        pass  # 甲醇品种，无需单独日志
    elif "p" in g.code:  # 棕榈
        g.volume_threshold = Config.VOLUME_THRESHOLD_LONG * 1.2  # 棕榈品种成交量阈值调高20%
        g.ma_period = 55
        pass  # 棕榈品种，无需单独日志
    else:
        g.volume_threshold = Config.VOLUME_THRESHOLD_LONG
        g.ma_period = 55
        pass  # 其它品种，无需单独日志
    # 输出初始化参数
    print(f"策略初始化完成: 品种={g.code}, 成交量阈值={g.volume_threshold}, MA周期={g.ma_period}")
    # ====== 新增：补充历史数据下载，默认近一年 ======
    today = datetime.datetime.now().strftime('%Y%m%d')
    last_year = (datetime.datetime.now() - datetime.timedelta(days=365)).strftime('%Y%m%d')
    try:
        if hasattr(ContextInfo, "download_history_data"):
            ContextInfo.download_history_data([g.code], period='5m', start_date=last_year, end_date=today)
            ContextInfo.download_history_data([g.code], period='30m', start_date=last_year, end_date=today)
            ContextInfo.download_history_data([g.code], period='1d', start_date=last_year, end_date=today)
    except Exception as e:
        print(f"历史数据下载异常: {e}")

def calculate_lines(price_series):
    """
    计算红白线
    
    参数:
    price_series: 价格序列
    
    返回:
    (white_line, red_line): 白线和红线序列
    """
    try:
        # 原有的计算逻辑
        length = len(price_series)
        white_line = pd.Series([None] * length, index=price_series.index)
        red_line = pd.Series([None] * length, index=price_series.index)
        
        last_white = None
        last_red = None
        
        for i in range(length):
            # 如果既没有白线也没有红线，初始化
            if last_white is None and last_red is None:
                # 根据第一个价格的位置确定初始线
                if i > 0:
                    if price_series.iloc[i] > price_series.iloc[i-1]:
                        last_red = price_series.iloc[i-1]  # 上涨，设置红线
                        red_line.iloc[i] = last_red
                        white_line.iloc[i] = None
                    else:
                        last_white = price_series.iloc[i-1]  # 下跌，设置白线
                        white_line.iloc[i] = last_white
                        red_line.iloc[i] = None
                else:
                    # 第一个价格，默认设置为白线
                    last_white = price_series.iloc[i]
                    white_line.iloc[i] = last_white
                    red_line.iloc[i] = None
            
            # 如果有白线，检查是否突破
            elif last_white is not None:
                if price_series.iloc[i] > last_white:  # 突破白线
                    white_line.iloc[i] = None
                    red_line.iloc[i] = last_white  # 白线转为红线
                    last_red = last_white
                    last_white = None
                else:  # 未突破，保持白线
                    white_line.iloc[i] = last_white
                    red_line.iloc[i] = None
                    
            # 如果有红线，检查是否跌破
            elif last_red is not None:
                if price_series.iloc[i] < last_red:  # 跌破红线
                    red_line.iloc[i] = None
                    white_line.iloc[i] = last_red  # 红线转为白线
                    last_white = last_red
                    last_red = None
                else:  # 未跌破，保持红线
                    red_line.iloc[i] = last_red
                    white_line.iloc[i] = None
        
        return white_line, red_line
    except Exception as e:
        print(f"计算红白线出错: {e}")
        return pd.Series([None] * len(price_series)), pd.Series([None] * len(price_series))

def calculate_ma(close, volume, period):
    """
    计算成交量加权均线
    
    参数:
    close: 收盘价序列
    volume: 成交量序列
    period: 周期
    
    返回:
    ma: 成交量加权均线
    """
    weighted_price = close * volume
    ma = weighted_price.rolling(window=period).sum() / volume.rolling(window=period).sum()
    return ma



def handlebar(ContextInfo):
    """主策略函数 - 每个K线周期执行一次"""
    global g
    price_5m = None  # 先声明，防止未定义NameError
    # 如果不是最后一根K线，直接返回
    if not ContextInfo.is_last_bar():
        return
    # 同步持仓状态
    sync_position_status(ContextInfo)
    # 获取时间和价格数据
    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    try:
        t = time.strftime("%H%M%S", time.localtime(timetag))
    except (TypeError, ValueError, OSError):
        t = "000000"
    hour = int(t[:2])
    minute = int(t[2:4])
    is_day_open_no_trade = hour == 9 and minute < Config.DAY_OPEN_NO_TRADE_MINUTES
    is_night_open_no_trade = hour == 21 and minute < Config.NIGHT_OPEN_NO_TRADE_MINUTES
    b1 = not ((hour == Config.DAY_CLOSE_HOUR and minute >= Config.DAY_CLOSE_MINUTE) or 
              (hour == Config.NIGHT_CLOSE_HOUR and minute >= Config.NIGHT_CLOSE_MINUTE) or
              is_day_open_no_trade or 
              is_night_open_no_trade)
    # 检查是否需要尾盘清仓
    if ((hour == Config.DAY_CLOSE_HOUR and minute >= Config.DAY_CLOSE_MINUTE) or 
        (hour == Config.NIGHT_CLOSE_HOUR and minute >= Config.NIGHT_CLOSE_MINUTE)):
        g.cover = 1
        if g.position != "none" and g.position_size > 0:
            if g.position == "long":
                passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '尾盘平多', ContextInfo)
                log_trade("尾盘平多", 0)
            elif g.position == "short":
                passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '尾盘平空', ContextInfo)
                log_trade("尾盘平空", 0)
            g.position = "none"
            g.position_size = 0
            return
    else:
        g.cover = 0
    try:
        price_5m = ContextInfo.get_market_data_ex(['open', 'high', 'low', 'close', 'volume'], 
                                               [g.code], 
                                               period='5m',
                                               count=60)
        if price_5m is None or 'close' not in price_5m or len(price_5m['close']) == 0:
            print("K线数据为空或无close字段，跳过本周期")
            return
        current_price = price_5m['close'].iloc[-1]
        white_line_5m, red_line_5m, k2_5m = calculate_red_white_lines_exact(price_5m, return_k2=True)
        ma55_5m = price_5m['close'].rolling(window=55).mean()
        MA_Line = calculate_ma(price_5m['close'], price_5m['volume'], 55)
        
        # 首次运行初始化，避免在初始化时误触发信号
        if not hasattr(g, 'first_run'):
            g.first_run = True
            g.prev_k2_5m = k2_5m.iloc[-1] if len(k2_5m) > 0 else 0
            print("首次运行，初始化K2前值，跳过信号检测")
            return  # 首次运行直接返回，等待下次数据更新
        
        wenhua_buy_signal = False
        wenhua_sell_signal = False
        if len(k2_5m) > 1 and len(ma55_5m) > 0:
            # 严格按照截图逻辑：K2从0变为1时，开始显示白色线，应该买入认购（看涨）
            if g.prev_k2_5m == 0 and k2_5m.iloc[-1] == 1 and price_5m['close'].iloc[-1] >= ma55_5m.iloc[-1]:
                wenhua_buy_signal = True
                print(f"文华买信号触发: K2从{g.prev_k2_5m}变为{k2_5m.iloc[-1]}, 收盘价={price_5m['close'].iloc[-1]}, MA55={ma55_5m.iloc[-1]}")
            # 严格按照截图逻辑：K2从0变为-3时，开始显示红色线，应该买入认沽（看跌）
            if g.prev_k2_5m == 0 and k2_5m.iloc[-1] == -3 and price_5m['close'].iloc[-1] <= ma55_5m.iloc[-1]:
                wenhua_sell_signal = True
                print(f"文华卖信号触发: K2从{g.prev_k2_5m}变为{k2_5m.iloc[-1]}, 收盘价={price_5m['close'].iloc[-1]}, MA55={ma55_5m.iloc[-1]}")
        
        # 更新前一个K2值
        g.prev_k2_5m = k2_5m.iloc[-1] if len(k2_5m) > 0 else 0
        
        kaichang = wenhua_buy_signal
        kaikong = wenhua_sell_signal
        
        # 初始化交易信号变量
        # break_white_long = False    # 突破白线做多
        # break_red_short = False     # 跌破红线做空
        # pullback_long_good = False  # 回调到红线做多
        # pullback_short_good = False # 反弹到白线做空
        # red_support_long = False    # 红线支撑做多
        
        # 检查是否跌破红线
        # break_red = check_break_red_line(price_5m, 
        #                                red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None, 
        #                                red_line_5m.iloc[-1])
        
        # 检查是否突破白线
        # if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
        #     if current_price > white_line_5m.iloc[-1]:
        #         break_white_long = True
        #         print(f"检测到突破白线: 价格{current_price} > 白线{white_line_5m.iloc[-1]}")
        
        # 检查是否回调到红线做多
        # if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
        #     if price_5m['low'].iloc[-1] <= red_line_5m.iloc[-1] and current_price > red_line_5m.iloc[-1]:
        #         pullback_long_good = True
        #         print(f"检测到回调到红线做多: 最低价{price_5m['low'].iloc[-1]} <= 红线{red_line_5m.iloc[-1]}, 收盘价{current_price} > 红线{red_line_5m.iloc[-1]}")
        
        # 检查是否反弹到白线做空
        # if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
        #     if price_5m['high'].iloc[-1] >= white_line_5m.iloc[-1] and current_price < white_line_5m.iloc[-1]:
        #         pullback_short_good = True
        #         print(f"检测到反弹到白线做空: 最高价{price_5m['high'].iloc[-1]} >= 白线{white_line_5m.iloc[-1]}, 收盘价{current_price} < 白线{white_line_5m.iloc[-1]}")
        
        # 修改红线支撑做多条件，增加转换期保护
        # if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
        #     # 检查是否处于转换期（前一根是白线，当前是红线）
        #     is_transition_period = (white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]) and
        #                           white_line_5m.iloc[-1] is None and
        #                           red_line_5m.iloc[-1] is not None)
        #     
        #     if current_price > red_line_5m.iloc[-1] and check_volume_increase(price_5m['volume']):
        #         if not is_transition_period:
        #             # 只有在非转换期才考虑红线支撑做多
        #             red_support_long = True
        #             print(f"检测到红线支撑做多: 价格{current_price} > 红线{red_line_5m.iloc[-1]}")
        #         else:
        #             print("处于白线转红线转换期，暂不考虑红线支撑做多")
        
        # 检测红白线转换
        # transition_signal = detect_line_transition(
        #     white_line_5m.iloc[-2] if len(white_line_5m) > 1 else None,
        #     white_line_5m.iloc[-1],
        #     red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None,
        #     red_line_5m.iloc[-1]
        # )
        
        # 计算趋势
        # current_trend = check_trend(price_5m, MA_Line.iloc[-1])
        
        # 获取转换信号的交易建议
        # transition_long, transition_short = should_trade_with_transition(
        #     transition_signal,
        #     current_price,
        #     MA_Line.iloc[-1],
        #     current_trend,
        #     price_5m['volume'],
        #     white_line_5m.iloc[-1],
        #     red_line_5m.iloc[-1],
        #     b1,  # 新增参数
        #     MA_Line.iloc[-1]  # 新增参数
        # )
        
        # 修改开仓条件，增加转换期保护
        # kaichang = ((break_white_long or pullback_long_good or 
        #            (red_support_long and not transition_signal == 1) or  # 红线支撑做多，但不在白转红期间
        #            transition_long) and 
        #            current_price > MA_Line.iloc[-1] and 
        #            g.buy_long < Config.OPEN_LONG_MAX and 
        #            b1 and 
        #            check_trend(price_5m, MA_Line.iloc[-1]) == 1)
        # 
        # kaikong = ((break_red_short or pullback_short_good or transition_short) and 
        #           current_price < MA_Line.iloc[-1] and 
        #           g.buy_short < Config.OPEN_SHORT_MAX and 
        #           b1 and 
        #           check_trend(price_5m, MA_Line.iloc[-1]) == -1 and
        #           (red_line_5m.iloc[-1] is None or current_price < red_line_5m.iloc[-1]))  # 新增条件
        
        # 是否可以开仓
        can_open_position = True  # 默认可以开仓
        
        # 开仓逻辑
        if g.position == "none":
            # 获取当前K线时间戳，用于计算开仓间隔
            current_bar_time = timetag if isinstance(timetag, (int, float)) else time.time()
            
            # 开多仓 - 添加开盘禁止交易条件
            if kaichang and can_open_position and not is_day_open_no_trade and not is_night_open_no_trade and (current_bar_time - g.trace_time_long) > Config.SLEEP_TIME*60 and t not in g.opened_t:
                # 确定仓位级别
                g.position_level = determine_position_level(ContextInfo, g.code, current_price)
                
                passorder(0, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '期货策略开多', ContextInfo)
                g.position = "long"
                g.buy_long += 1
                g.trace_time_long = current_bar_time
                g.hold_price = current_price
                g.open_price = current_price
                g.opened_t.append(t)
                g.hold_code = g.code
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                
                # 重置止盈标记
                g.profit_taken_1 = False
                g.profit_taken_2 = False
                g.profit_taken_3 = False
                
                log_trade("开多", current_price)
            
            # 开空仓 - 添加开盘禁止交易条件
            elif kaikong and can_open_position and not is_day_open_no_trade and not is_night_open_no_trade and (current_bar_time - g.trace_time_short) > Config.SLEEP_TIME*60 and t not in g.opened_t:
                # 确定仓位级别
                g.position_level = determine_position_level(ContextInfo, g.code, current_price)
                
                passorder(3, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '期货策略开空', ContextInfo)
                g.position = "short"
                g.buy_short += 1
                g.trace_time_short = current_bar_time
                g.hold_price = current_price
                g.open_price = current_price
                g.opened_t.append(t)
                g.hold_code = g.code
                g.position_size = Config.TRADE_HANDS
                g.sysid = {}
                
                # 重置止盈标记
                g.profit_taken_1_short = False
                g.profit_taken_2_short = False
                g.profit_taken_3_short = False
                
                log_trade("开空", current_price)
        
        # 持仓管理
        elif g.position == "long":
            # 文华信号反向止损：出现文华卖信号立即平多
            if wenhua_sell_signal:
                passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '文华信号反向止损平多', ContextInfo)
                log_trade("文华信号反向止损平多", current_price)
                g.position = "none"
                g.position_size = 0
                g.last_close_time = time.time()
            else:
                # 止盈逻辑
                executed_take_profit = execute_take_profit(ContextInfo, current_price)
                # 如果没有执行止盈，检查红白线止损条件
                if not executed_take_profit:
                    # 检查是否跌破红线
                    if break_red:
                        passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '红白线止损平多', ContextInfo)
                        log_trade("红白线止损平多", current_price)
                        g.position = "none"
                        g.position_size = 0
                        g.last_close_time = time.time()
                        # 反向交易逻辑保留
                        should_reverse, reverse_type, confidence = should_reverse_trade(ContextInfo, "long", current_price)
                        if should_reverse and reverse_type == "short" and confidence >= 60:
                            print(f"止损后反向开空，信心分数: {confidence}")
                            passorder(3, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '止损反向开空', ContextInfo)
                            g.position = "short"
                            g.buy_short += 1
                            g.trace_time_short = time.time()
                            g.hold_price = current_price
                            g.open_price = current_price
                            g.hold_code = g.code
                            g.position_size = Config.TRADE_HANDS
                            g.sysid = {}
                            g.profit_taken_1_short = False
                            g.profit_taken_2_short = False
                            g.profit_taken_3_short = False
                            log_trade("止损反向开空", current_price)
        elif g.position == "short":
            # 文华信号反向止损：出现文华买信号立即平空
            if wenhua_buy_signal:
                passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '文华信号反向止损平空', ContextInfo)
                log_trade("文华信号反向止损平空", current_price)
                g.position = "none"
                g.position_size = 0
                g.last_close_time = time.time()
            else:
                # 止盈逻辑
                executed_take_profit = execute_take_profit(ContextInfo, current_price)
                # 如果没有执行止盈，检查红白线止损条件
                if not executed_take_profit:
                    # 检查是否突破白线
                    if first_break_white:
                        passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '红白线止损平空', ContextInfo)
                        log_trade("红白线止损平空", current_price)
                        g.position = "none"
                        g.position_size = 0
                        g.last_close_time = time.time()
                        # 反向交易逻辑保留
                        should_reverse, reverse_type, confidence = should_reverse_trade(ContextInfo, "short", current_price)
                        if should_reverse and reverse_type == "long" and confidence >= 60:
                            print(f"止损后反向开多，信心分数: {confidence}")
                            passorder(0, 1101, account, g.code, 14, 0, Config.TRADE_HANDS, '', 1, '止损反向开多', ContextInfo)
                            g.position = "long"
                            g.buy_long += 1
                            g.trace_time_long = time.time()
                            g.hold_price = current_price
                            g.open_price = current_price
                            g.hold_code = g.code
                            g.position_size = Config.TRADE_HANDS
                            g.sysid = {}
                            g.profit_taken_1 = False
                            g.profit_taken_2 = False
                            g.profit_taken_3 = False
                            log_trade("止损反向开多", current_price)
    
    except Exception as e:
        print(f"处理K线数据出错: {e}")
        return

def check_stop_loss(ContextInfo, current_price):
    """检查是否触发止损 - 简化版，仅使用红白线系统"""
    try:
        timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
        try:
            t = time.strftime("%H%M%S", time.localtime(timetag))
        except (TypeError, ValueError, OSError):
            if isinstance(timetag, str):
                t = timetag[-6:] if len(timetag) >= 6 else "000000"
            else:
                t = time.strftime("%H%M%S")
        # 获取5分钟K线数据
        price_5m = ContextInfo.get_market_data_ex(['open', 'high', 'low', 'close', 'volume'], 
                                               [g.code], 
                                               period='5m',
                                               count=10)
        if price_5m is None or 'close' not in price_5m or len(price_5m['close']) == 0:
            print("K线数据为空或无close字段，跳过止损检查")
            return False
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        if g.position == "long":
            red_line_broken = False
            if red_line_5m.iloc[-2] is not None and not pd.isna(red_line_5m.iloc[-2]) and (red_line_5m.iloc[-1] is None or pd.isna(red_line_5m.iloc[-1])):
                if price_5m['close'].iloc[-1] < red_line_5m.iloc[-2]:
                    red_line_broken = True
                    print("多单止损信号：检测到跌破红线支撑（红线消失）")
            elif red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
                if price_5m['close'].iloc[-1] < red_line_5m.iloc[-1]:
                    red_line_broken = True
                    print(f"多单止损信号：检测到跌破红线支撑（价格{price_5m['close'].iloc[-1]}低于红线{red_line_5m.iloc[-1]}）")
            if red_line_broken:
                profit_pct = calculate_profit(current_price, g.open_price, True)
                passorder(7, 1101, account, g.code, 14, 0, g.position_size, '', 1, '多单红线止损', ContextInfo)
                log_trade("多单红线止损", current_price, profit_pct)
                g.position = "none"
                g.position_size = 0
                return True
        elif g.position == "short":
            white_line_broken = False
            if white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]) and (white_line_5m.iloc[-1] is None or pd.isna(white_line_5m.iloc[-1])):
                if price_5m['close'].iloc[-1] > white_line_5m.iloc[-2]:
                    white_line_broken = True
                    print("空单止损信号：检测到突破白线压力（白线消失）")
            elif white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]):
                if price_5m['close'].iloc[-1] > white_line_5m.iloc[-1]:
                    white_line_broken = True
                    print(f"空单止损信号：检测到突破白线压力（价格{price_5m['close'].iloc[-1]}高于白线{white_line_5m.iloc[-1]}）")
            if white_line_broken:
                profit_pct = calculate_profit(current_price, g.open_price, False)
                passorder(9, 1101, account, g.code, 14, 0, g.position_size, '', 1, '空单白线止损', ContextInfo)
                log_trade("空单白线止损", current_price, profit_pct)
                g.position = "none"
                g.position_size = 0
                return True
        return False
    except Exception as e:
        print(f"止损K线数据处理出错: {e}")
        return False

def execute_take_profit(ContextInfo, current_price):
    try:
        # 获取5分钟K线数据（如有需要）
        # price_5m = ContextInfo.get_market_data(['open', 'high', 'low', 'close', 'volume'], [g.code], period='5m', count=10)
        # ...原有止盈逻辑...
        if g.position == "long":
            profit_pct = calculate_profit(current_price, g.open_price, True)
            total_hands = Config.TRADE_HANDS
            if not g.profit_taken_1 and profit_pct >= Config.TAKE_PROFIT_1:
                first_batch = max(1, int(total_hands * Config.PROFIT_PCT_1))
                first_batch = min(first_batch, g.position_size)
                if first_batch > 0:
                    passorder(7, 1101, account, g.code, 14, 0, first_batch, '', 1, '多单首批止盈', ContextInfo)
                    g.profit_taken_1 = True
                    g.position_size -= first_batch
                    log_trade("多单首批止盈(30%)", current_price, profit_pct)
                    return True
            elif g.profit_taken_1 and not g.profit_taken_2 and profit_pct >= Config.TAKE_PROFIT_2:
                second_batch = max(1, int(total_hands * Config.PROFIT_PCT_2))
                second_batch = min(second_batch, g.position_size)
                if second_batch > 0:
                    passorder(7, 1101, account, g.code, 14, 0, second_batch, '', 1, '多单第二批止盈', ContextInfo)
                    g.profit_taken_2 = True
                    g.position_size -= second_batch
                    log_trade("多单第二批止盈(30%)", current_price, profit_pct)
                    return True
            elif g.profit_taken_1 and g.profit_taken_2 and not g.profit_taken_3 and profit_pct >= Config.TAKE_PROFIT_3:
                third_batch = g.position_size
                if third_batch > 0:
                    passorder(7, 1101, account, g.code, 14, 0, third_batch, '', 1, '多单第三批止盈', ContextInfo)
                    g.profit_taken_3 = True
                    g.position_size = 0
                    g.position = "none"
                    log_trade("多单第三批止盈(40%)", current_price, profit_pct)
                    return True
        elif g.position == "short":
            profit_pct = calculate_profit(current_price, g.open_price, False)
            total_hands = Config.TRADE_HANDS
            if not g.profit_taken_1_short and profit_pct >= Config.TAKE_PROFIT_1:
                first_batch = max(1, int(total_hands * Config.PROFIT_PCT_1))
                first_batch = min(first_batch, g.position_size)
                if first_batch > 0:
                    passorder(9, 1101, account, g.code, 14, 0, first_batch, '', 1, '空单首批止盈', ContextInfo)
                    g.profit_taken_1_short = True
                    g.position_size -= first_batch
                    log_trade("空单首批止盈(30%)", current_price, profit_pct)
                    return True
            elif g.profit_taken_1_short and not g.profit_taken_2_short and profit_pct >= Config.TAKE_PROFIT_2:
                second_batch = max(1, int(total_hands * Config.PROFIT_PCT_2))
                second_batch = min(second_batch, g.position_size)
                if second_batch > 0:
                    passorder(9, 1101, account, g.code, 14, 0, second_batch, '', 1, '空单第二批止盈', ContextInfo)
                    g.profit_taken_2_short = True
                    g.position_size -= second_batch
                    log_trade("空单第二批止盈(30%)", current_price, profit_pct)
                    return True
            elif g.profit_taken_1_short and g.profit_taken_2_short and not g.profit_taken_3_short and profit_pct >= Config.TAKE_PROFIT_3:
                third_batch = g.position_size
                if third_batch > 0:
                    passorder(9, 1101, account, g.code, 14, 0, third_batch, '', 1, '空单第三批止盈', ContextInfo)
                    g.profit_taken_3_short = True
                    g.position_size = 0
                    g.position = "none"
                    log_trade("空单第三批止盈(40%)", current_price, profit_pct)
                    return True
        return False
    except Exception as e:
        print(f"止盈K线数据处理出错: {e}")
        return False

def check_volume_increase(volume_data, threshold_base=1.2):
    """
    检查成交量是否增加
    
    参数:
    volume_data: 成交量数据
    threshold_base: 基础阈值倍数
    
    返回:
    True: 成交量增加
    False: 成交量未增加
    """
    try:
        # 使用品种特定的阈值
        threshold = g.volume_threshold if hasattr(g, 'volume_threshold') else threshold_base
        
        # 确保数据足够
        if len(volume_data) < 2:
            return False
            
        # 计算当前成交量与前一根K线成交量的比值
        volume_ratio = volume_data.iloc[-1] / volume_data.iloc[-2]
        
        # 返回是否超过阈值
        return volume_ratio > threshold
    except Exception as e:
        print(f"检查成交量增加出错: {e}")
        return False

def check_trend(price_data, ma_value, lookback=5):
    """
    检查价格趋势 - 优化版
    
    参数:
    price_data: 价格数据
    ma_value: 均线值
    lookback: 回溯周期
    
    返回:
    1: 上升趋势
    0: 无明显趋势
    -1: 下降趋势
    """
    # 计算短期趋势
    short_trend = price_data['close'].iloc[-1] - price_data['close'].iloc[-3]
    
    # 计算价格相对均线位置
    price_vs_ma = price_data['close'].iloc[-1] - ma_value
    
    # 判断趋势
    if short_trend > 0 and price_vs_ma > 0:
        return 1  # 上升趋势
    elif short_trend < 0 and price_vs_ma < 0:
        return -1  # 下降趋势
    
    # 如果短期趋势和均线位置不一致，以短期趋势为准
    if abs(short_trend) > abs(price_vs_ma) * 0.01:  # 短期趋势明显
        return 1 if short_trend > 0 else -1
        
    return 0  # 无明显趋势

def should_reverse_trade(ContextInfo, last_position_type, current_price):
    try:
        if not hasattr(g, 'last_close_time'):
            g.last_close_time = 0
        time_since_close = time.time() - g.last_close_time
        min_wait_seconds = 5 * 60
        max_wait_seconds = 30 * 60
        if time_since_close < min_wait_seconds:
            print(f"反向交易评估: 距离上次平仓时间不足5分钟，暂不考虑反向")
            return (False, None, 0)
        if time_since_close > max_wait_seconds:
            print(f"反向交易评估: 距离上次平仓时间超过30分钟，不再考虑反向")
            return (False, None, 0)
        price_5m = ContextInfo.get_market_data_ex(['close','high','low','open','volume'], 
                                               [g.code], 
                                               period='5m',
                                               count=60)
        if price_5m is None or 'close' not in price_5m or len(price_5m['close']) == 0:
            print("K线数据为空或无close字段，跳过反向交易判断")
            return (False, None, 0)
        MA_Line = calculate_ma(price_5m['close'], price_5m['volume'], 55)
        white_line_5m, red_line_5m = calculate_red_white_lines_exact(price_5m)
        delta = price_5m['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(window=9).mean()
        avg_loss = loss.rolling(window=9).mean()
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        current_rsi = rsi.iloc[-1]
        confidence_score = 0
        if last_position_type == "long":
            reverse_type = "short"
            if current_price < MA_Line.iloc[-1]:
                confidence_score += 30
                print(f"反向交易评估(空): 价格在MA线下方")
            if white_line_5m.iloc[-1] is not None and not pd.isna(white_line_5m.iloc[-1]) and current_price < white_line_5m.iloc[-1]:
                confidence_score += 30
                print("反向交易评估(空): 白线压力确认")
            if current_rsi > 70:
                confidence_score += 20
                print(f"反向交易评估(空): RSI超买 ({current_rsi:.1f})")
            first_break_red = False
            if len(red_line_5m) > 1 and red_line_5m.iloc[-2] is not None and not pd.isna(red_line_5m.iloc[-2]):
                if (red_line_5m.iloc[-1] is None or pd.isna(red_line_5m.iloc[-1])) and price_5m['close'].iloc[-1] < red_line_5m.iloc[-2]:
                    first_break_red = True
                    print(f"检测到标准跌破红线信号: 价格{price_5m['close'].iloc[-1]}跌破红线{red_line_5m.iloc[-2]}")
                    confidence_score += 20
                elif red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]):
                    price_below_red = (red_line_5m.iloc[-1] - price_5m['close'].iloc[-1]) / red_line_5m.iloc[-1] > 0.01
                    if price_below_red:
                        first_break_red = True
                        print(f"检测到强力跌破红线信号: 价格{price_5m['close'].iloc[-1]}大幅低于红线{red_line_5m.iloc[-1]}")
                        confidence_score += 20
        elif last_position_type == "short":
            reverse_type = "long"
            if current_price > MA_Line.iloc[-1]:
                confidence_score += 30
                print(f"反向交易评估(多): 价格在MA线上方")
            if red_line_5m.iloc[-1] is not None and not pd.isna(red_line_5m.iloc[-1]) and current_price > red_line_5m.iloc[-1]:
                confidence_score += 30
                print("反向交易评估(多): 红线支撑确认")
            if current_rsi < 30:
                confidence_score += 20
                print(f"反向交易评估(多): RSI超卖 ({current_rsi:.1f})")
            first_break_white = False
            if len(white_line_5m) > 1 and white_line_5m.iloc[-2] is not None and not pd.isna(white_line_5m.iloc[-2]):
                if (white_line_5m.iloc[-1] is None or pd.isna(white_line_5m.iloc[-1])) and current_price > white_line_5m.iloc[-2]:
                    first_break_white = True
                    confidence_score += 20
                    print("反向交易评估(多): 突破白线")
        else:
            return (False, None, 0)
        price_30m = ContextInfo.get_market_data_ex(['close','high','low','open','volume'], 
                                                [g.code], 
                                                period='30m',
                                                count=60)
        if price_30m is None or 'close' not in price_30m or len(price_30m['close']) == 0:
            print("30分钟K线数据为空或无close字段，跳过反向交易判断")
            return (False, None, 0)
        trend_30m = check_trend(price_30m, MA_Line.iloc[-1])
        if reverse_type == "long" and trend_30m == 1:
            confidence_score += 20
            print("反向交易评估(多): 30分钟趋势向上")
        elif reverse_type == "short" and trend_30m == -1:
            confidence_score += 20
            print("反向交易评估(空): 30分钟趋势向下")
        should_reverse = confidence_score >= 60
        print(f"反向交易评估: 最终信心分数 {confidence_score}/100, {'执行' if should_reverse else '不执行'}反向{reverse_type}交易")
        return (should_reverse, reverse_type, confidence_score)
    except Exception as e:
        print(f"反向交易K线数据处理出错: {e}")
        return (False, None, 0)

def get_future_positions(ContextInfo, accountid):
    """
    获取期货持仓信息
    
    参数:
    ContextInfo: 上下文信息
    accountid: 账户ID
    
    返回:
    持仓字典，格式为 {(合约代码, 方向): 持仓量}
    """
    positions = get_trade_detail_data(accountid, 'FUTURE', 'POSITION')
    hold_dict = {}
    for p in positions:
        market = p.m_strExchangeID
        market = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(p.m_strExchangeID, p.m_strExchangeID)

        code = p.m_strInstrumentID +'.'+market
        direction = p.m_nDirection
        volume = p.m_nVolume
        key = (code, direction)
        hold_dict[key] = hold_dict.get(key, 0)+volume
    return hold_dict
    

def order_callback(ContextInfo, orderInfo):
    """
    订单状态回调函数 - 处理订单状态变化
    
    参数:
    ContextInfo: 上下文信息
    orderInfo: 订单信息
    """
    print(orderInfo.m_strRemark, orderInfo.m_nOrderStatus, orderInfo.m_nOffsetFlag, orderInfo.m_dTradedPrice)
    if orderInfo.m_strRemark not in ['期货策略开多','期货策略开空']:
        return
    
    market = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(orderInfo.m_strExchangeID, orderInfo.m_strExchangeID)
    k = orderInfo.m_strInstrumentID+'.'+market
    if k != g.code:
        return
    
    # 处理成交订单
    if orderInfo.m_nOrderStatus==56 and orderInfo.m_nOffsetFlag == 48 and orderInfo.m_dTradedPrice>0:
        g.open_price = round(orderInfo.m_dTradedPrice, 1)
        g.hold_price = round(orderInfo.m_dTradedPrice, 1)
        if not g.sysid:  # 如果是order_callback先收到
            g.sysid[orderInfo.m_strOrderSysID] = []
        else:
            if orderInfo.m_strOrderSysID in g.sysid:  # 如果是deal_callback先收到
                g.open_price, g.hold_price = g.sysid[orderInfo.m_strOrderSysID]
    
    # 更新开多次数
    if orderInfo.m_nOrderStatus==56 and orderInfo.m_nOffsetFlag == 48 and orderInfo.m_nDirection == 48:
        g.buy_long += 1
        g.position = "long"
        g.position_size = Config.TRADE_HANDS
        print(f"{g.code} 开多次数+1 {g.buy_long}")
    
    # 更新开空次数
    if orderInfo.m_nOrderStatus==56 and orderInfo.m_nOffsetFlag == 48 and orderInfo.m_nDirection == 49:
        g.buy_short += 1
        g.position = "short"
        g.position_size = Config.TRADE_HANDS
        print(f"{g.code} 开空次数+1 {g.buy_short}")

    # 处理已撤单
    if orderInfo.m_nOrderStatus==57 and orderInfo.m_nOffsetFlag == 48:
        g.hold = 0
        print("order_callback set 0")


def orderError_callback(ContextInfo, passOrderInfo, msg):
    """
    订单错误回调函数 - 处理订单错误
    
    参数:
    ContextInfo: 上下文信息
    passOrderInfo: 订单信息
    msg: 错误信息
    """
    if '期货策略' not in passOrderInfo.strategyName:
        return
        
    g.hold = 0
    print("orderError_callback set 0", msg)
    
    if '期货策略开空' in passOrderInfo.strategyName:
        g.buy_short += 1
        print(f"{g.code} 开空次数+1 {g.buy_short}")
    
    if '期货策略开多' in passOrderInfo.strategyName:
        g.buy_long += 1
        print(f"{g.code} 开多次数+1 {g.buy_long}")


def deal_callback(ContextInfo, dealInfo):
    """
    成交回报回调函数 - 处理成交信息
    
    参数:
    ContextInfo: 上下文信息
    dealInfo: 成交信息
    """
    # 处理订单系统ID
    if g.sysid:
        if dealInfo.m_strOrderSysID in g.sysid:  # 如果是order_callback先收到
            g.open_price, g.hold_price = round(dealInfo.m_dPrice, 1), round(dealInfo.m_dPrice, 1)
        else:
            g.sysid[dealInfo.m_strOrderSysID] = [round(dealInfo.m_dPrice, 1), round(dealInfo.m_dPrice, 1)]
    else:  # 如果是deal_callback先收到
        g.sysid[dealInfo.m_strOrderSysID] = [round(dealInfo.m_dPrice, 1), round(dealInfo.m_dPrice, 1)]

    print(f"deal callback m_nOffsetFlag:[{dealInfo.m_nOffsetFlag}] m_strRemark:[{dealInfo.m_strRemark}], [{dealInfo.m_strInstrumentID}] [{dealInfo.m_strExchangeID}] [{dealInfo.m_dPrice}]")
    
    if dealInfo.m_strRemark not in ['期货策略开多','期货策略开空']:
        return
        
    market = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
    k = dealInfo.m_strInstrumentID+'.'+market
    
    if dealInfo.m_nOffsetFlag == 48 and g.code.find(dealInfo.m_strInstrumentID)>=0:
        print("deal callback", dealInfo.m_dPrice)
        g.open_price = round(dealInfo.m_dPrice, 1)
        g.hold_price = round(dealInfo.m_dPrice, 1)
        
        # 更新持仓状态
        if dealInfo.m_strRemark == '期货策略开多':
            g.position = "long"
            g.position_size = Config.TRADE_HANDS
            # 重置止盈标记
            g.profit_taken_1 = False
            g.profit_taken_2 = False
            g.profit_taken_3 = False
            
        elif dealInfo.m_strRemark == '期货策略开空':
            g.position = "short"
            g.position_size = Config.TRADE_HANDS
            # 重置止盈标记
            g.profit_taken_1_short = False
            g.profit_taken_2_short = False
            g.profit_taken_3_short = False

# ====== 技术指标计算函数 ======
def REF(S, N=1):
    """
    对序列整体下移动N,返回序列(shift后会产生NAN)
    
    参数:
    S: 输入序列
    N: 移动周期数
    
    返回:
    移动后的序列
    """
    return pd.Series(S).shift(N).values

def SMA(S, N, M=1):
    """
    中国式的SMA,至少需要120周期才精确 (雪球180周期)
    
    参数:
    S: 输入序列
    N: 周期数
    M: 权重因子
    
    返回:
    SMA序列
    """
    alpha = 1/(1+N-M/M)  # 等价于 alpha=M/N
    return pd.Series(S).ewm(alpha=M/N, adjust=False).mean().values

def SUM(S, N):
    """
    对序列求N天累计和，返回序列
    N=0对序列所有依次求和
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    累计和序列
    """
    return pd.Series(S).rolling(N).sum().values if N>0 else pd.Series(S).cumsum().values

def HHV(S, N):
    """
    求N周期内的最高值
    例如: HHV(C, 5) 最近5天收盘最高价
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最高值序列
    """
    return pd.Series(S).rolling(N).max().values

def LLV(S, N):
    """
    求N周期内的最低值
    例如: LLV(C, 5) 最近5天收盘最低价
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最低值序列
    """
    return pd.Series(S).rolling(N).min().values

def HHVBARS(S, N):
    """
    求N周期内S最高值到当前周期数, 返回序列
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最高值位置序列
    """
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]), raw=True).values

def LLVBARS(S, N):
    """
    求N周期内S最低值到当前周期数, 返回序列
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    最低值位置序列
    """
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]), raw=True).values

def MA(S, N):
    """
    求序列的N日简单移动平均值，返回序列
    
    参数:
    S: 输入序列
    N: 周期数
    
    返回:
    移动平均序列
    """
    return pd.Series(S).rolling(N).mean().values

def EMA(source, N:int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    
    参数:
    source: 输入序列
    N: 周期数
    result_type: 返回类型，'np'表示numpy数组
    
    返回:
    EMA序列
    """
    M = 2
    if N<M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N-1
    M = 2
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M*x+d*temp)/(N+1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
    """
    Python实现的LLV函数，适用于pandas Series
    
    参数:
    S: 输入Series
    N: 周期数
    
    返回:
    最低值Series
    """
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i+1-N):i+1]
        result_list.append(min(l))
    return pd.Series(result_list, index=index)

def PyHHV(S, N):
    """
    Python实现的HHV函数，适用于pandas Series
    
    参数:
    S: 输入Series
    N: 周期数
    
    返回:
    最高值Series
    """
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i+1-N):i+1]
        result_list.append(max(l))
    return pd.Series(result_list, index=index)

def after_init(ContextInfo):
    """
    在初始化后执行，用于获取交易日历信息
    
    参数:
    ContextInfo: 上下文信息
    
    返回:
    交易日期列表
    """
    return ContextInfo.get_trading_dates(g.market,'','********',count=2, period='1d')

def sync_position_status(ContextInfo):
    """同步持仓状态，确保g.position与实际持仓一致"""
    positions = get_future_positions(ContextInfo, account)
    
    # 检查是否有多头持仓
    has_long_position = False
    long_volume = 0
    for (code, direction), volume in positions.items():
        if code == g.code and direction == 48 and volume > 0:
            has_long_position = True
            long_volume = volume
            break
    
    # 检查是否有空头持仓
    has_short_position = False
    short_volume = 0
    for (code, direction), volume in positions.items():
        if code == g.code and direction == 49 and volume > 0:
            has_short_position = True
            short_volume = volume
            break
    
    # 更新系统状态
    if has_long_position:
        if g.position != "long":
            log_trade("同步状态：检测到多头持仓但系统状态不一致，已更新", 0)
        g.position = "long"
        g.position_size = long_volume
    elif has_short_position:
        if g.position != "short":
            log_trade("同步状态：检测到空头持仓但系统状态不一致，已更新", 0)
        g.position = "short"
        g.position_size = short_volume
    else:
        if g.position != "none":
            log_trade("同步状态：没有检测到持仓但系统状态不一致，已更新", 0)
        g.position = "none"
        g.position_size = 0

def determine_position_level(ContextInfo, code, current_price):
    """
    根据当前价格确定仓位级别
    
    参数:
    ContextInfo: 上下文信息
    code: 合约代码
    current_price: 当前价格
    
    返回:
    仓位级别
    """
    # 实现仓位级别判断逻辑
    # 这里可以根据实际需求实现不同的判断逻辑
    # 例如，可以根据价格波动幅度、成交量等因素来确定仓位级别
    return 1  # 临时返回值，需要根据实际逻辑实现



def calculate_atr(ContextInfo, period=10):  # 从14改为10，使其更敏感
    price_data = ContextInfo.get_market_data_ex(['high', 'low', 'close'], 
                                         [g.code], 
                                         period='1d',
                                         count=period+10)
    if price_data is None or 'close' not in price_data or len(price_data['close']) == 0:
        print("日线K线数据为空或无close字段，ATR计算跳过")
        return None
    
    # 计算真实波动幅度
    tr1 = abs(price_data['high'] - price_data['low'])
    tr2 = abs(price_data['high'] - price_data['close'].shift(1))
    tr3 = abs(price_data['low'] - price_data['close'].shift(1))
    
    # 取三者最大值
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    
    # 计算ATR
    atr = tr.rolling(window=period).mean().iloc[-1]
    return atr

def check_pullback_volume(volume):
    """
    专门针对回调买入的成交量检查
    
    参数:
    volume: 成交量序列
    
    返回:
    True: 成交量符合回调买入要求
    False: 成交量不符合回调买入要求
    """
    # 回调阶段（前几根K线）成交量应该减少
    pullback_volume_decrease = volume.iloc[-3:-1].mean() < volume.iloc[-6:-3].mean()
    
    # 当前K线（反弹确认）成交量应该增加
    current_volume_increase = volume.iloc[-1] > volume.iloc[-2]
    
    # 综合判断
    return pullback_volume_decrease and current_volume_increase

def check_downtrend_volume(volume, close):
    """
    专门针对下跌行情的成交量检查
    
    参数:
    volume: 成交量序列
    close: 收盘价序列
    
    返回:
    True: 成交量符合下跌行情特征
    False: 成交量不符合下跌行情特征
    """
    # 计算价格变化
    price_change = close.pct_change()
    
    # 下跌确认
    is_declining = close.iloc[-1] < close.iloc[-3]
    
    # 情况1: 放量下跌 - 传统意义上的看空信号
    volume_increase = volume.iloc[-1] > volume.iloc[-2] * 1.2
    
    # 情况2: 缩量下跌 - 买盘撤离导致的下跌
    volume_decrease = volume.iloc[-1] < volume.iloc[-5:].mean()
    price_decline = price_change.iloc[-1] < -0.002  # 价格下跌超过0.2%
    
    # 情况3: 连续下跌 - 持续的下跌趋势
    continuous_decline = all(price_change.iloc[-3:] < 0)
    
    # 综合判断
    return is_declining and (volume_increase or (volume_decrease and price_decline) or continuous_decline)

def detect_line_transition(prev_white, curr_white, prev_red, curr_red):
    """
    检测红白线转换
    
    参数:
    prev_white: 前一根K线的白线
    curr_white: 当前K线的白线
    prev_red: 前一根K线的红线
    curr_red: 当前K线的红线
    
    返回:
    1: 白线转红线
    -1: 红线转白线
    0: 无转换
    """
    try:
        print("\n=== 红白线转换检测 ===")
        print(f"前一根K线白线: {prev_white}")
        print(f"当前K线白线: {curr_white}")
        print(f"前一根K线红线: {prev_red}")
        print(f"当前K线红线: {curr_red}")
        
        print(f"转换检测详情: 前白={prev_white}, 当白={curr_white}, 前红={prev_red}, 当红={curr_red}")
        
        # 白线转红线
        if (prev_white is not None and not pd.isna(prev_white) and 
            (curr_red is not None and not pd.isna(curr_red)) and 
            (curr_white is None or pd.isna(curr_white))):
            print(f"检测到标准白线转红线: {prev_white} -> {curr_red}")
            return 1
            
        # 红线转白线
        if (prev_red is not None and not pd.isna(prev_red) and 
            (curr_white is not None and not pd.isna(curr_white)) and 
            (curr_red is None or pd.isna(curr_red))):
            print(f"检测到标准红线转白线: {prev_red} -> {curr_white}")
            return -1
            
        return 0
    except Exception as e:
        print(f"检测红白线转换出错: {e}")
        return 0

# 在全局作用域中定义函数
def check_historical_transition(white_line, red_line):
    """
    检查历史数据中是否有白线转红线
    
    参数:
    white_line: 白线序列
    red_line: 红线序列
    
    返回:
    (has_transition, bars_ago): 是否有转换，以及发生在多少根K线之前
    """
    try:
        # 确保数据足够
        if white_line is None or red_line is None or len(white_line) < 5 or len(red_line) < 5:
            return False, 0
            
        # 从最近的数据开始向前查找
        for i in range(len(white_line)-2, 0, -1):
            # 检查是否有白线转红线
            if (not pd.isna(white_line.iloc[i]) and white_line.iloc[i] is not None and 
                pd.isna(white_line.iloc[i+1]) and 
                not pd.isna(red_line.iloc[i+1]) and red_line.iloc[i+1] is not None):
                
                # 计算发生在多少根K线之前
                bars_ago = len(white_line) - 1 - i
                print(f"检测到白线转红线发生在{bars_ago}根K线之前")
                return True, bars_ago
                
        return False, 0
    except Exception as e:
        print(f"检查历史转换出错: {e}")
        return False, 0

# 在handlebar函数中，在使用can_open_position之前添加定义

# 其他可能的限制条件
# ...

# 修改红白线检查函数
def check_break_red_line(price_data, red_line_prev, red_line_curr):
    """
    检查是否跌破红线
    
    参数:
    price_data: 价格数据
    red_line_prev: 前一根K线的红线
    red_line_curr: 当前K线的红线
    
    返回:
    是否跌破红线
    """
    try:
        print("=== 跌破红线详细检查 ===")
        print(f"前一根K线红线: {red_line_prev}")
        print(f"当前K线红线: {red_line_curr}")
        
        if price_data is None or price_data.empty:
            print("价格数据为空")
            return False
            
        current_price = price_data['close'].iloc[-1]
        print(f"当前收盘价: {current_price}")
        
        # 添加空值检查
        if red_line_prev is not None and not pd.isna(red_line_prev) and current_price is not None:
            price_prev_pct = (current_price - red_line_prev) / red_line_prev * 100
            print(f"价格与前一根红线比较: {price_prev_pct:.2f}%")
        else:
            print("前一根红线不存在或为空，无法计算百分比")
            price_prev_pct = None
            
        if red_line_curr is not None and not pd.isna(red_line_curr) and current_price is not None:
            price_curr_pct = (current_price - red_line_curr) / red_line_curr * 100
            print(f"价格与当前红线比较: {price_curr_pct:.2f}%")
        else:
            print("当前红线不存在或为空，无法计算百分比")
            price_curr_pct = None
        
        # 判断是否跌破红线
        if red_line_prev is not None and not pd.isna(red_line_prev) and current_price < red_line_prev:
            return True
        if red_line_curr is not None and not pd.isna(red_line_curr) and current_price < red_line_curr:
            return True
            
        return False
    except Exception as e:
        print(f"检查红白线出错: {e}")
        return False

# 修改打印红白线详情函数
def print_line_details(price_data, white_line, red_line):
    """
    打印红白线详细信息
    
    参数:
    price_data: 价格数据
    white_line: 白线值
    red_line: 红线值
    """
    try:
        print("\n=== 红白线详细信息 ===")
        
        if price_data is None or price_data.empty:
            print("价格数据为空")
            return
            
        current_price = price_data['close'].iloc[-1]
        print(f"当前价格: {current_price}")
        print(f"白线价格: {white_line}")
        print(f"红线价格: {red_line}")
        
        # 检查是否突破白线
        if white_line is not None and not pd.isna(white_line) and current_price > white_line:
            print(f"突破白线: 价格{current_price} > 白线{white_line}")
        else:
            print("无白线突破")
            
        # 检查白线是否存在
        if white_line is None or pd.isna(white_line):
            print("无白线")
            
        # 计算价格与红线距离
        if red_line is not None and not pd.isna(red_line) and current_price is not None:
            distance_pct = (current_price - red_line) / red_line * 100
            print(f"价格与红线距离: {distance_pct:.2f}%")
            
        # 打印最高价和最低价
        if 'high' in price_data.columns and 'low' in price_data.columns:
            print(f"最高价: {price_data['high'].iloc[-1]}")
            print(f"最低价: {price_data['low'].iloc[-1]}")
            
    except Exception as e:
        print(f"打印红白线详情出错: {e}")

# 添加新的红白线计算函数
def calculate_red_white_lines(price_data):
    """
    根据原始公式计算红白线指标
    
    参数:
    price_data: 包含open, high, low, close的DataFrame
    
    返回:
    white_line: 白线序列
    red_line: 红线序列
    """
    try:
        # 确保数据足够
        if len(price_data) < 10:
            return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data))
        
        # 计算高低点
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        
        # 计算2根K线的最高价和最低价
        hx = high.rolling(window=2).max()
        lx = low.rolling(window=2).min()
        
        # 初始化结果序列
        white_line = pd.Series([None] * len(price_data), index=price_data.index)
        red_line = pd.Series([None] * len(price_data), index=price_data.index)
        k2 = pd.Series([0] * len(price_data), index=price_data.index)
        
        # 计算H1和L1
        for i in range(5, len(price_data)):
            # 白线条件
            if (lx[i] > lx[i-1] and lx[i] > lx[i-3] and lx[i] > lx[i-5] and 
                hx[i] > hx[i-1] and hx[i] > hx[i-2] and hx[i] > hx[i-4] and 
                open_price[i] < close[i] and 
                (close[i] - open_price.iloc[:i+1].min()) > 0):
                
                # 设置白线值为前4根K线的最低价
                white_line[i] = lx[i-4]
                k2[i] = 1
            
            # 红线条件
            elif (hx[i] < hx[i-1] and hx[i] < hx[i-2] and hx[i] < hx[i-4] and 
                  lx[i] < lx[i-1] and lx[i] < lx[i-3] and lx[i] < lx[i-5] and 
                  open_price[i] > close[i] and 
                  (open_price.iloc[:i+1].max() - close[i]) > 0):
                
                # 设置红线值为前4根K线的最高价
                red_line[i] = hx[i-4]
                k2[i] = -3
        
        # 填充空值 - 使用前一个有效值
        for i in range(1, len(price_data)):
            if k2[i] == 0:
                k2[i] = k2[i-1]
            
            if k2[i] == 1 and pd.isna(white_line[i]):
                # 找到前一个白线值
                for j in range(i-1, -1, -1):
                    if not pd.isna(white_line[j]):
                        white_line[i] = white_line[j]
                        break
            
            elif k2[i] == -3 and pd.isna(red_line[i]):
                # 找到前一个红线值
                for j in range(i-1, -1, -1):
                    if not pd.isna(red_line[j]):
                        red_line[i] = red_line[j]
                        break
        
        print(f"红白线计算完成: 数据长度={len(price_data)}, 白线非空值={white_line.notna().sum()}, 红线非空值={red_line.notna().sum()}")
        return white_line, red_line
    
    except Exception as e:
        print(f"计算红白线出错: {e}")
        # 返回空序列
        return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)

def calculate_red_white_lines_exact(price_data, return_k2=False):
    """
    严格按照文华指标源代码计算红白线
    参数:
    price_data: 包含open, high, low, close的DataFrame
    return_k2: 是否返回K2序列
    返回:
    white_line: 白线序列
    red_line: 红线序列
    k2: K2序列（可选）
    """
    try:
        # 确保数据足够
        if len(price_data) < 10:
            if return_k2:
                return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data)), pd.Series([0] * len(price_data))
            else:
                return pd.Series([None] * len(price_data)), pd.Series([None] * len(price_data))
        # 计算HX和LX
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        # HX:=HHV(HIGH,2);
        hx = high.rolling(window=2).max()
        # LX:=LLV(LOW,2);
        lx = low.rolling(window=2).min()
        # 初始化H1, L1, K1, K2, G
        h1 = pd.Series([0] * len(price_data), index=price_data.index)
        l1 = pd.Series([0] * len(price_data), index=price_data.index)
        k1 = pd.Series([0] * len(price_data), index=price_data.index)
        k2 = pd.Series([0] * len(price_data), index=price_data.index)
        g = pd.Series([None] * len(price_data), index=price_data.index)
        # 计算H1和L1 - 严格按照文华指标源代码
        for i in range(5, len(price_data)):
            # H1条件：HX<REF(HX,1)&&HX<REF(HX,2)&&HX<REF(HX,4)&&LX<REF(LX,1)&&LX<REF(LX,3)&&LX<REF(LX,5)&&OPEN>CLOSE&&(HHV(OPEN,0)-CLOSE)>0
            if (hx[i] < hx[i-1] and hx[i] < hx[i-2] and hx[i] < hx[i-4] and 
                lx[i] < lx[i-1] and lx[i] < lx[i-3] and lx[i] < lx[i-5] and 
                open_price[i] > close[i] and 
                (open_price.iloc[:i+1].max() - close[i]) > 0):
                h1[i] = hx[i-4]  # REF(HX,4)
            else:
                h1[i] = 0
                
            # L1条件：LX>REF(LX,1)&&LX>REF(LX,3)&&LX>REF(LX,5)&&HX>REF(HX,1)&&HX>REF(HX,2)&&HX>REF(HX,4)&&OPEN<CLOSE&&(CLOSE-LLV(OPEN,0))>0
            if (lx[i] > lx[i-1] and lx[i] > lx[i-3] and lx[i] > lx[i-5] and 
                hx[i] > hx[i-1] and hx[i] > hx[i-2] and hx[i] > hx[i-4] and 
                open_price[i] < close[i] and 
                (close[i] - open_price.iloc[:i+1].min()) > 0):
                l1[i] = lx[i-4]  # REF(LX,4)
            else:
                l1[i] = 0
        # H2:=VALUEWHEN(H1>0,H1);
        h2 = pd.Series([None] * len(price_data), index=price_data.index)
        last_valid_h1 = None
        for i in range(len(price_data)):
            if h1[i] > 0:
                last_valid_h1 = h1[i]
            h2[i] = last_valid_h1
        # L2:=VALUEWHEN(L1>0,L1);
        l2 = pd.Series([None] * len(price_data), index=price_data.index)
        last_valid_l1 = None
        for i in range(len(price_data)):
            if l1[i] > 0:
                last_valid_l1 = l1[i]
            l2[i] = last_valid_l1
        # K1:=IFELSE(CLOSE>H2,-3,IFELSE(CLOSE<L2,1,0));
        for i in range(len(price_data)):
            if h2[i] is not None and close[i] > h2[i]:
                k1[i] = -3
            elif l2[i] is not None and close[i] < l2[i]:
                k1[i] = 1
            else:
                k1[i] = 0
        # K2:=VALUEWHEN(K1<>0,K1);
        last_valid_k1 = 0
        for i in range(len(price_data)):
            if k1[i] != 0:
                last_valid_k1 = k1[i]
            k2[i] = last_valid_k1
        # G:=IFELSE(K2=1,H2,L2);
        for i in range(len(price_data)):
            if k2[i] == 1:
                g[i] = h2[i]
            else:
                g[i] = l2[i]
        # 转换为红白线
        white_line = pd.Series([None] * len(price_data), index=price_data.index)
        red_line = pd.Series([None] * len(price_data), index=price_data.index)
        for i in range(len(price_data)):
            if k2[i] == 1:  # 白线
                white_line[i] = g[i]
                red_line[i] = None
            elif k2[i] == -3:  # 红线
                red_line[i] = g[i]
                white_line[i] = None
        if return_k2:
            return white_line, red_line, k2
        else:
            return white_line, red_line
    except Exception as e:
        print(f"计算文华指标红白线出错: {e}")
        if return_k2:
            return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index), pd.Series([0] * len(price_data), index=price_data.index)
        else:
            return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)

def should_trade_with_transition(transition_signal, current_price, MA_Line, trend, volume_data, 
                               white_line=None, red_line=None, time_filter=True, ma55_value=None):
    # 解析转换信号
    transition_long = transition_signal == "WHITE_TO_RED"  # 白线转红线，做多信号
    transition_short = transition_signal == "RED_TO_WHITE"  # 红线转白线，做空信号
    
    # 价格与MA的关系
    price_above_ma = current_price > MA_Line
    
    # 趋势方向
    trend_up = trend == "UP"
    trend_down = trend == "DOWN"
    
    # 成交量确认
    volume_confirmed = check_volume_increase(volume_data, Config.VOLUME_THRESHOLD_LONG)
    
    # 检查与55均线的距离
    ma55_distance_ok = True
    ma55_distance_pct = 0
    
    if ma55_value is not None:
        ma55_distance_pct = abs(current_price - ma55_value) / ma55_value
        ma55_distance_ok = ma55_distance_pct <= Config.MA55_DISTANCE_THRESHOLD
    
    # 判断做多条件
    can_long = (transition_long and  # 白线转红线
                price_above_ma and   # 价格在MA上方
                trend_up and         # 趋势向上
                volume_confirmed and # 成交量确认
                ma55_distance_ok and # 与55均线距离适中
                time_filter)         # 时间过滤
    
    # 判断做空条件
    can_short = (transition_short and  # 红线转白线
                 not price_above_ma and # 价格在MA下方
                 trend_down and        # 趋势向下
                 volume_confirmed and  # 成交量确认
                 ma55_distance_ok and  # 与55均线距离适中
                 time_filter)          # 时间过滤
    
    # 输出交易信号信息
    if transition_long or transition_short:
        print("\n=== 转换信号交易条件检查 ===")
        print(f"转换信号: {'白转红' if transition_long else '红转白' if transition_short else '无'}")
        print(f"价格位置: {'MA上方' if price_above_ma else 'MA下方'}")
        print(f"趋势方向: {'向上' if trend_up else '向下' if trend_down else '无明显趋势'}")
        print(f"成交量确认: {'是' if volume_confirmed else '否'}")
        print(f"时间过滤: {'是' if time_filter else '否'}")
        
        # 添加55均线距离信息
        if ma55_value is not None:
            print(f"与55均线距离: {ma55_distance_pct*100:.2f}% {'(符合要求)' if ma55_distance_ok else '(超出阈值)'}")
        
        if red_line:
            print(f"价格与红线关系: {'价格在红线上方' if current_price > red_line else '价格在红线下方'}")
        if white_line:
            print(f"价格与白线关系: {'价格在白线上方' if current_price > white_line else '价格在白线下方'}")
        print(f"可以做多: {can_long}")
        print(f"可以做空: {can_short}")
        
        # 添加警告信息
        if transition_long and current_price < red_line:
            print("警告：白线转红线但价格在红线下方，建议观望")
        if transition_short and current_price > white_line:
            print("警告：红线转白线但价格在白线上方，建议观望")
        if not ma55_distance_ok:
            print(f"警告：价格偏离55均线过远({ma55_distance_pct*100:.2f}%)，建议等待回归后入场")
    
    return can_long, can_short

