# -*- coding: gbk -*-
import pandas as pd
import numpy as np
import traceback
import time
from 期货策略 import *

"""
期货策略测试文件
用于测试期货策略的运行效果
"""

# 创建模拟上下文类
class ContextInfoMock:
    def __init__(self):
        self.barpos = 0
            
    def get_market_data_ex(self, fields, codes, period='5m', count=10, **kwargs):
        # 模拟日线数据
        if period == '1d':
            data = {
                'bu2506.SF': pd.DataFrame({
                    'close': [3521.0], 
                    'open': [3505.0]
                }, index=['20250514'])
            }
            return data
                
        # 模拟5分钟数据
        if period == '5m':
            data = {
                'bu2506.SF': pd.DataFrame({
                    'open': [3492.0, 3492.0, 3484.0, 3483.0, 3480.0, 3476.0, 3476.0, 3480.0, 3480.0, 3479.0, 3478.0, 3477.0, 3477.0, 3475.0, 3473.0],
                    'high': [3503.0, 3495.0, 3489.0, 3488.0, 3485.0, 3480.0, 3479.0, 3484.0, 3481.0, 3482.0, 3481.0, 3479.0, 3480.0, 3478.0, 3475.0],
                    'low': [3488.0, 3482.0, 3480.0, 3477.0, 3474.0, 3475.0, 3474.0, 3479.0, 3478.0, 3476.0, 3475.0, 3475.0, 3474.0, 3472.0, 3470.0],
                    'close': [3492.0, 3483.0, 3487.0, 3480.0, 3476.0, 3476.0, 3476.0, 3480.0, 3480.0, 3479.0, 3478.0, 3476.0, 3475.0, 3473.0, 3472.0],
                    'volume': [9925, 6178, 5474, 4300, 3950, 3880, 3600, 3700, 3850, 3800, 3700, 3650, 3600, 3580, 3550]
                }, index=['20250515090500', '20250515091000', '20250515091500', 
                         '20250515092000', '20250515092500', '20250515093000',
                         '20250515093500', '20250515094000', '20250515094500',
                         '20250515095000', '20250515095500', '20250515100000',
                         '20250515100500', '20250515101000', '20250515101500'])
            }
            return data
                
        # 模拟1分钟数据
        if period == '1m':
            data = {
                'bu2506.SF': pd.DataFrame({
                    'open': [3478.0, 3479.0, 3480.0],
                    'high': [3482.0, 3483.0, 3482.0],
                    'low': [3478.0, 3478.0, 3479.0],
                    'close': [3480.0, 3480.0, 3480.0],
                    'volume': [900, 950, 1000]
                }, index=['20250515094700', '20250515094800', '20250515094900'])
            }
            return data
                
        return {}

# 模拟下单函数
def passorder(order_type, market, acct, code, price_type, price, volume, stop_price, order_flag, note, context):
    print(f"下单: {note}, 类型={order_type}, 价格={price}, 手数={volume}")

# 运行测试
if __name__ == "__main__":
    print("【" + time.strftime("%Y-%m-%d %H:%M:%S") + "】 [trade]开始模拟测试")
    
    # 初始化全局变量
    g = G()
    g.code = "bu2506.SF"  # 期货合约代码
    g.market = "SF"  # 市场代码
    g.position = "none"  # 当前持仓方向: none, long, short
    g.position_size = 0  # 持仓手数
    g.trace_time_long = 0  # 最近一次开多仓的时间
    g.trace_time_short = 0  # 最近一次开空仓的时间
    g.open_price = 0  # 开仓价格
    g.last_close_time = 0  # 最近一次平仓时间
    g.volume_threshold = Config.VOLUME_THRESHOLD_LONG  # 成交量阈值
    g.ma_period = 55  # MA周期
    
    # 模拟账户
    account = "simulation"  
    
    # 初始化模拟上下文
    context = ContextInfoMock()
    
    # 输出初始化信息
    print(f"  {g.code} 红白线系统止损策略初始化完成")
    print(f"策略参数: 成交量阈值={g.volume_threshold}, MA周期={g.ma_period}\n")
    
    try:
        # 运行策略主函数
        handlebar(context)
        print("\n策略测试执行完成")
    except Exception as e:
        print(f"策略执行异常: {str(e)}")
        traceback.print_exc() 