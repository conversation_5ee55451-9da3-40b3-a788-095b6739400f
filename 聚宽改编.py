#encoding:gbk

import math
import time
import numpy as np
import pandas as pd

class G():
    pass

g = G()
g.state = 'empty'
g.price = {}
g.sell = []
g.last_signal = None

def init(ContextInfo):
    # 固定两只股票（可根据需要修改）
    g.security1 = '600887.SH'
    g.security2 = '600036.SH'
    g.regression_ratio = 1.0
    g.p = 0.5
    g.q = 0.5
    g.test_days = 120
    g.state = 'empty'
    g.sell = []
    g.price = {}
    g.account = None
    # 获取账户信息
    accounts = get_trade_detail_data(account, accountType, 'account')
    if accounts:
        g.account = accounts[0]
    g.stock_list = [g.security1, g.security2]
    # ContextInfo.subscribe(g.stock_list)
    print('协整搬砖策略初始化完成')
    ContextInfo.run_time("myHandlebar", "3nSecond", "2019-10-14 13:20:00")

def myHandlebar(ContextInfo):
    now = time.strftime('%H%M%S')
    if now >= '150000':
        return
    # 获取持仓
    positions = get_trade_detail_data(account, accountType, 'POSITION')
    hold_vol = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_nCanUseVolume for p in positions if p.m_nCanUseVolume>0}
    profit_ratio = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_dProfitRate for p in positions}
    # 极端风控：仅当总浮亏超过10%时才止损
    if hold_vol:
        if sum([profit_ratio[s] for s in hold_vol]) < -0.10:
            for s in hold_vol:
                if s not in g.sell:
                    print(s, '极端止损：持仓股票的总亏损大于10%，全部平仓')
                    passorder(24, 1101, account, s, 8, 0, hold_vol[s], '', 2, '', ContextInfo)
                    g.sell.append(s)
            return  # 止损后本周期不再操作
    # 获取历史数据
    if not g.price:
        g.price = ContextInfo.get_market_data_ex_ori(['close'], g.stock_list, period='1d', fill_data=False, subscribe=False)
    # 协整信号判断
    z_score = z_test(ContextInfo)
    new_state = get_signal(z_score)
    if new_state != g.last_signal:
        change_positions(new_state, ContextInfo)
        g.last_signal = new_state

def z_test(ContextInfo):
    # 获取历史收盘价
    prices1 = g.price[g.security1]['close'][-g.test_days:]
    prices2 = g.price[g.security2]['close'][-g.test_days:]
    prices1 = np.array(prices1)
    prices2 = np.array(prices2)
    stable_series = prices2 - g.regression_ratio * prices1
    series_mean = np.mean(stable_series)
    sigma = np.std(stable_series)
    diff = stable_series[-1] - series_mean
    z = diff / sigma if sigma != 0 else 0

    # 打印详细协整信号计算日志
    print(f"[协整信号计算] {g.security2} - {g.regression_ratio}*{g.security1}")
    print(f"  当前价差: {stable_series[-1]:.4f}")
    print(f"  均值: {series_mean:.4f}  标准差: {sigma:.4f}")
    print(f"  当前z-score: {z:.4f}")
    print(f"  判据: z-score > 1 买入{g.security1}，z-score < -1 买入{g.security2}，否则观望/均衡")
    return z

def get_signal(z_score):
    if z_score > 1:
        print(f"[信号判定] z-score={z_score:.4f} > 1，买入{g.security1}，卖出{g.security2}")
        return 'buy1'
    if z_score < -1:
        print(f"[信号判定] z-score={z_score:.4f} < -1，买入{g.security2}，卖出{g.security1}")
        return 'buy2'
    if -1 <= z_score <= 1:
        if z_score >= 0:
            print(f"[信号判定] z-score={z_score:.4f}，在均值上方，观望/均衡")
            return 'side1'
        else:
            print(f"[信号判定] z-score={z_score:.4f}，在均值下方，观望/均衡")
            return 'side2'

def change_positions(new_state, ContextInfo):
    # 获取账户资金
    accounts = get_trade_detail_data(account, accountType, 'account')
    if not accounts:
        print('账号错误或账号掉线')
        return
    money = accounts[0].m_dBalance
    available = accounts[0].m_dAvailable
    # 获取持仓
    positions = get_trade_detail_data(account, accountType, 'POSITION')
    hold_vol = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_nCanUseVolume for p in positions if p.m_nCanUseVolume>0}
    now_time = time.strftime('%Y-%m-%d %H:%M:%S')
    # 获取最新价格
    full_tick = ContextInfo.get_full_tick([g.security1, g.security2])
    # 仓位调整
    if new_state == 'buy1':
        # 如果当前已是buy1状态且持有security1，避免重复下单
        if g.state == 'buy1' and g.security1 in hold_vol:
            print(f"[{now_time}] 已处于buy1状态且已持有{g.security1}，不重复买入")
            return
        # 卖出股票2，买入股票1
        if g.security2 in hold_vol:
            print(f"[{now_time}] 信号: {new_state} 卖出 {g.security2} 数量: {hold_vol[g.security2]}")
            passorder(24, 1101, account, g.security2, 8, 0, hold_vol[g.security2], '', 2, '', ContextInfo)
            print(f"[{now_time}] 已提交卖出 {g.security2}")
        # 判断可用资金是否足够买入100股
        last_price = full_tick[g.security1]['lastPrice']
        min_amount = last_price * 100
        if available < min_amount:
            print(f"[{now_time}] 可用资金{available}不足以买入{g.security1}的最小100股，当前股价{last_price}，所需{min_amount}")
            return
        order_money = min(money, available)
        # 只买整数手
        max_lots = int(order_money // min_amount)
        if max_lots == 0:
            print(f"[{now_time}] 可用资金{available}不足以买入{g.security1}的最小100股，当前股价{last_price}，所需{min_amount}")
            return
        buy_amount = max_lots * 100 * last_price
        print(f"[{now_time}] 信号: {new_state} 买入 {g.security1} 股数: {max_lots*100} 金额: {buy_amount}")
        passorder(23, 1102, account, g.security1, 2, 0, buy_amount, '', 2, '', ContextInfo)
        print(f"[{now_time}] 已提交买入 {g.security1}")
        g.state = 'buy1'
    elif new_state == 'buy2':
        # 如果当前已是buy2状态且持有security2，避免重复下单
        if g.state == 'buy2' and g.security2 in hold_vol:
            print(f"[{now_time}] 已处于buy2状态且已持有{g.security2}，不重复买入")
            return
        # 卖出股票1，买入股票2
        if g.security1 in hold_vol:
            print(f"[{now_time}] 信号: {new_state} 卖出 {g.security1} 数量: {hold_vol[g.security1]}")
            passorder(24, 1101, account, g.security1, 8, 0, hold_vol[g.security1], '', 2, '', ContextInfo)
            print(f"[{now_time}] 已提交卖出 {g.security1}")
        # 判断可用资金是否足够买入100股
        last_price = full_tick[g.security2]['lastPrice']
        min_amount = last_price * 100
        if available < min_amount:
            print(f"[{now_time}] 可用资金{available}不足以买入{g.security2}的最小100股，当前股价{last_price}，所需{min_amount}")
            return
        order_money = min(money, available)
        max_lots = int(order_money // min_amount)
        if max_lots == 0:
            print(f"[{now_time}] 可用资金{available}不足以买入{g.security2}的最小100股，当前股价{last_price}，所需{min_amount}")
            return
        buy_amount = max_lots * 100 * last_price
        print(f"[{now_time}] 信号: {new_state} 买入 {g.security2} 股数: {max_lots*100} 金额: {buy_amount}")
        passorder(23, 1102, account, g.security2, 2, 0, buy_amount, '', 2, '', ContextInfo)
        print(f"[{now_time}] 已提交买入 {g.security2}")
        g.state = 'buy2'
    elif (g.state == 'buy1' and new_state == 'side2') or (g.state == 'buy2' and new_state == 'side1'):
        # 按默认仓位调整
        # 以可用资金一半分别买入两只股票，且都需满足最小100股
        half_money = min(money * g.p, available)
        # security1
        last_price1 = full_tick[g.security1]['lastPrice']
        min_amount1 = last_price1 * 100
        max_lots1 = int(half_money // min_amount1)
        # security2
        last_price2 = full_tick[g.security2]['lastPrice']
        min_amount2 = last_price2 * 100
        max_lots2 = int(half_money // min_amount2)
        if max_lots1 == 0 or max_lots2 == 0:
            print(f"[{now_time}] 可用资金不足，无法调整为默认仓位，当前可用: {available}，{g.security1}需{min_amount1}，{g.security2}需{min_amount2}")
            return
        buy_amount1 = max_lots1 * 100 * last_price1
        buy_amount2 = max_lots2 * 100 * last_price2
        print(f"[{now_time}] 信号: {new_state} 调整为默认仓位 {g.security1} 股数: {max_lots1*100} 金额: {buy_amount1}, {g.security2} 股数: {max_lots2*100} 金额: {buy_amount2}")
        passorder(23, 1102, account, g.security1, 2, 0, buy_amount1, '', 2, '', ContextInfo)
        print(f"[{now_time}] 已提交调整 {g.security1} 默认仓位")
        passorder(23, 1102, account, g.security2, 2, 0, buy_amount2, '', 2, '', ContextInfo)
        print(f"[{now_time}] 已提交调整 {g.security2} 默认仓位")
        g.state = 'even'
# 其余QMT辅助函数如passorder、get_trade_detail_data、account、accountType等需在QMT环境下定义
