#encoding:gbk


open_long_num =1
open_short_num = 1
tj3_long_num = 1 # 条件3 开多次数限制
tj3_short_num = 1# 条件3 开空次数限制
fs_line = 20 #  FS即时价位—分时均线价位<0.1  # 条件1开仓判断时用

hand = 1  #交易手数

stoploss_ratio = -0.2 # % 止损比例% 盈亏比例低于该值时卖出
stopprofit_ratio = 0.3 #% 盈利比例% 盈亏比例高于该值时卖出



sleep_time = 5  # 分钟。平仓之后3分钟内不重复开

import math
import time
import pandas as pd
import numpy as np


class G():
    pass


g = G()
g.buy_long = 0
g.buy_short = 0
g.hold = 0
g.hold_price = 0
g.open_price = 0
g.trace_time_long = 0
g.trace_time_short = 0
g.opened_t = []
g.sysid = {}
g.cover = 1
g.tj3_long_time = 0
g.tj3_short_time = 0


def init(ContextInfo):
    ContextInfo.set_account(account)
    market = ContextInfo.market
    market = market.replace("SHFE",'SF').replace("CZCE",'ZF').replace("DCE",'DF').replace("CFFEX",'IF')
    code = ContextInfo.stockcode+'.'+ContextInfo.market
    g.code =ContextInfo.get_main_contract(code) + '.' + market
    # download_history_data(g.code, '1m','********','********')
    g.market = market
    info = ContextInfo.get_instrumentdetail(g.code)
    
    print(g.code, "止盈止损点", stoploss_ratio, stopprofit_ratio)
    g.info = info


def after_init(ContextInfo):
    return ContextInfo.get_trading_dates(g.market,'','********',count=2, period='1d')


def handlebar(ContextInfo):
    if not ContextInfo.is_last_bar():
        return
    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    bar_date = timetag_to_datetime(timetag, '%Y%m%d%H%M%S')
    start, end = after_init(ContextInfo)
    price = ContextInfo.get_market_data(['close','amount','volume','high','low'],[g.code],period='1m',
                    start_time=start+'200000',
                    end_time=bar_date,
                    )
    price_1d = ContextInfo.get_market_data(['close','open'],[g.code],period='1d',
                    start_time=start,
                    end_time=start,
                    count=1)
    print('price_1d',price_1d)
    #price.to_csv('d:/ma.csv')
    price_len = price.shape[0]
    if price_len<=21:
        return
    t = price.index[-1][-8:].replace(':','')
    
    CG,FL,MA_Line,FS,B1,B2 = cal_vba(ContextInfo, price)
    C = price['close']
    AMT = price['amount']
    VOL = price['volume']
    MA_Line = SUM(C*VOL,0)/SUM(VOL, 0)
    CG=MA(C,21)
    FL=HHV(CG,3)
    FS=CG-(FL-CG)
    pct = time.strftime("%H%M%S")
    #print(g.code,B1[-3:],B2[-3:])
    ContextInfo.paint('B1',B1[-1],-1,0,)
    ContextInfo.paint('B2',B2[-1],-1,0,)
    first, last_time = get_shape(CG,FL,MA_Line,FS,B1,B2)
    b1 = not ('235959'>=pct>='224900' or '150000'>=pct >='144900' )
    # print(B1,t)
    if any([math.isnan(CG[i]) for i in [-1,-2,-3,-4,-5]] ):
        return
    last_five_minute_long = all([CG[i]==FL[i]==FS[i] for i in (-1,-2,-3,-4,-5)])
    last_five_minute_short = all([CG[i]!=FL[i] and CG[i]!=FS[i] and FL[i]!=FS[i] for i in (-1,-2,-3,-4,-5)])
    # print(g.code, t, b1,g.buy_long<open_long_num , C[-1]>MA_Line[-1] , last_five_minute_long)
    #print(f"CG:{CG[-1]},FL:{FL[-1]},FS:{FS[-1]}")
    # 条件1 开多判断
    last_five_minute_long = all([CG[i]==FL[i]==FS[i] for i in (-1,)])
    tj_long = first==1 and last_time<=15 and last_five_minute_long and C[-1]>MA_Line[-1] and FS[-1] > MA_Line[-1] and B1[-1]-B2[-1]>0 and FS[-1]-MA_Line[-1]<=fs_line
    tj_short = first==1 and last_time<=15 and last_five_minute_short and C[-1]<MA_Line[-1] and max(FS[-1],CG[-1],FL[-1]) < MA_Line[-1] and B1[-1]-B2[-1]<0 and MA_Line[-1] - FS[-1]<=fs_line

    '''
    条件三： 
买多：B1与25线金叉 且最近一次B1-B2<0小于15分钟
买空：B2和80线死叉 且最近一次B1-B2>0小于15分钟
    '''
    b12 = list(B1<B2)
    b12kong = list(B1>B2)
    print('开多判断：',g.code,B1[-2:], t,last_five_minute_long,FS[-1]>MA_Line[-1],C[-1]>MA_Line[-1],not all(b12[-10:-1])) 
    print('开空判断：',g.code,B1[-2:], t,last_five_minute_short,FS[-1]<MA_Line[-1], C[-1]<MA_Line[-1], not all(b12kong[-10:-1])) 
    tj3_long = (B1[-1]>20 and B1[-2]<=20 and not all(b12[-10:-1]) and MA_Line[-1] - C[-1]>=fs_line)
    tj3_short= (B1[-1]<95 and B1[-2]>=95 and not all(b12kong[-10:-1]) and C[-1]-MA_Line[-1]>=fs_line)
    if g.hold==0 and b1 and g.buy_long<open_long_num and \
                    (tj3_long):
        buy_long = True
    else:
        buy_long = False
    if g.hold ==0 and b1 and g.buy_short<open_short_num and (
            tj3_short):
        buy_short = True
    else:
        buy_short = False
    if buy_long:
        print(bar_date, buy_long)
    if buy_short:
        print(bar_date, buy_short)
    buy_long = buy_long and C[-1]>(price_1d.iloc[0]['close']+price_1d.iloc[0]['open'])/2
    buy_short = buy_short  and C[-1]<(price_1d.iloc[0]['close']+price_1d.iloc[0]['open'])/2
    if buy_long and time.time()-g.trace_time_long>sleep_time*60 and t not in g.opened_t:
        if tj3_long and g.tj3_long_time<tj3_long_num:
            g.tj3_long_time +=1
            if g.hold == -1:
                passorder(9, 1101, account, g.code, 12, 0, hand, '',1,'策略平空',ContextInfo)
            g.sysid = {}
            passorder(0, 1101, account, g.code, 14,0, hand,'',1,'期货策略开多',ContextInfo)
            g.hold = 1
            g.trace_time_long = time.time()
            g.hold_price = 0
            g.open_price = 0
            g.opened_t.append(t)
            print(f'{t} {g.code} 开多{B1[-2:]} {b12[:20]} {FS[-1]-MA_Line[-1]},{tj_long}')
    if buy_short and time.time()-g.trace_time_short>sleep_time*60 and t not in g.opened_t:
        if tj3_short and g.tj3_short_time<tj3_short_num:
            g.tj3_short_time +=1
            if g.hold == 1:
                passorder(7, 1101, account, g.code, 12, 0, hand, '',1,'策略平多',ContextInfo)
            g.sysid = {}
            passorder(3, 1101, account, g.code, 14,0, hand,'',1,'期货策略开空',ContextInfo)
            print(f'{t} {g.code} 开空{B1[-2:]} {b12[:20]} {MA_Line[-1] - FS[-1]} {tj_short}')
            g.opened_t.append(t)
            g.hold_price = 9999999
            g.open_price = 0
            g.hold = -1
            g.trace_time_short = time.time()
    #full = ContextInfo.get_full_tick([g.code])[g.code]
    #c = full['lastPrice']
    c = C[-1]
    if g.open_price >0:
        if g.hold ==1:
            g.hold_price = max(g.hold_price, c)
            hold_ratio = round((c/g.open_price-1)*100,2)
            print(f'多 {g.code},方向:{g.hold} 成本价:{g.open_price} 持有最高价：{g.hold_price} 最新价:{c} 盈亏比例:{hold_ratio}%')

            if hold_ratio<stoploss_ratio or hold_ratio>stopprofit_ratio:
                # 7 平多,优先平昨
                passorder(7, 1101, account, g.code, 11, int(c-3*g.info['PriceTick']), hand, '',1,'策略平多',ContextInfo)
                print(f'{g.code} 平多')
                g.trace_time_long = time.time()
                g.hold =0
        if g.hold ==-1:
            g.hold_price = min(g.hold_price, c)
            hold_ratio = round((c/g.open_price-1)*100,2) * -1
            print(f'空 {g.code},方向:{g.hold} 成本价:{g.open_price} 持有最低价：{g.hold_price} 最新价:{c} 盈亏比例:{hold_ratio}%')

            if hold_ratio<stoploss_ratio or hold_ratio>stopprofit_ratio:# 9：平空,优先平昨
                passorder(9, 1101, account, g.code, 11, int(c+3*g.info['PriceTick']), hand, '',1,'策略平空',ContextInfo)
                g.hold =0
                g.trace_time_short = time.time()
                print(f'{g.code} 平空')
    if not b1 and g.cover==1:
        orders = get_trade_detail_data(account,accountType,'ORDER')
        for o in orders:
            if o.m_nOrderStatus in [50,55]: # 委托可撤时再撤单
                cancel(o.m_strOrderSysID, account, 'stock', ContextInfo)
        time.sleep(1)
        holds = get_future_positions(ContextInfo, account)
        for code, direction in holds:
            if direction == 49 and code == g.code:
                passorder(9, 1101, account, g.code, 12, 0, hand, '',1,'策略平空',ContextInfo) # 尾盘平空
                g.hold =0
                print("尾盘平空",g.code)
            if direction == 48 and code == g.code:
                passorder(7, 1101, account, g.code, 12, 0, hand, '',1,'策略平多',ContextInfo) # 尾盘平多
                g.hold =0
                print("尾盘平多",g.code)
        g.cover=0

def get_future_positions(ContextInfo,accountid):
    positions = get_trade_detail_data(accountid, 'FUTURE', 'POSITION')
    hold_dict = {}
    for p in positions:
        market = p.m_strExchangeID
        marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(p.m_strExchangeID, p.m_strExchangeID)

        code = p.m_strInstrumentID +'.'+market
        direction = p.m_nDirection
        volume = p.m_nVolume
        key = (code, direction)
        hold_dict[key] = hold_dict.get(key, 0)+volume
    return hold_dict
    

def order_callback(ContextInfo, orderInfo):
    print(orderInfo.m_strRemark, orderInfo.m_nOrderStatus, orderInfo.m_nOffsetFlag,orderInfo.m_dTradedPrice)
    if orderInfo.m_strRemark not in  ['期货策略开多','期货策略开空']:
        return
    marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(orderInfo.m_strExchangeID, orderInfo.m_strExchangeID)
    k = orderInfo.m_strInstrumentID+'.'+marekt
    if k != g.code:
        return
    if orderInfo.m_nOrderStatus==56 and orderInfo.m_nOffsetFlag == 48 and orderInfo.m_dTradedPrice>0:
        
        g.open_price = round(orderInfo.m_dTradedPrice,1)
        g.hold_price = round(orderInfo.m_dTradedPrice,1)
        if not g.sysid:# #若果是order_callback先收到
            g.sysid[orderInfo.m_strOrderSysID] = []
        else:
            if orderInfo.m_strOrderSysID in g.sysid:#若果是deal_callback先收到
                g.open_price,g.hold_price =g.sysid[orderInfo.m_strOrderSysID]
    if orderInfo.m_nOrderStatus==56 and orderInfo.m_nOffsetFlag == 48 and orderInfo.m_nDirection == 48:
        g.buy_long+=1
        print(f"{g.code} 开多次数+1 {g.buy_long}")
    if orderInfo.m_nOrderStatus==56 and orderInfo.m_nOffsetFlag == 48 and orderInfo.m_nDirection == 49:
        g.buy_short+=1
        print(f"{g.code} 开空次数+1 {g.buy_short}")

    if orderInfo.m_nOrderStatus==57 and orderInfo.m_nOffsetFlag == 48:
        g.hold =0
        print("order_callback set 0")

def orderError_callback(ContextInfo,passOrderInfo,msg):
    return
    if '期货策略' in passOrderInfo.strategyName:
        g.hold =0
        print("orderError_callback set 0", msg)
    if '期货策略开空' in passOrderInfo.strategyName:
        g.buy_short+=1
        print(f"{g.code} 开空次数+1 {g.buy_short}")
    if '期货策略开多' in passOrderInfo.strategyName:
        g.buy_long+=1
        print(f"{g.code} 开多次数+1 {g.buy_long}")


def deal_callback(ContextInfo, dealInfo):
    if g.sysid:
        if dealInfo.m_strOrderSysID in g.sysid:#若果是order_callback先收到
            g.open_price,g.hold_price = round(dealInfo.m_dPrice, 1),round(dealInfo.m_dPrice,1)
        else:
            g.sysid[dealInfo.m_strOrderSysID] = [round(dealInfo.m_dPrice, 1),round(dealInfo.m_dPrice,1)]
    if not g.sysid:# #若果是 deal_callback先收到
        g.sysid[dealInfo.m_strOrderSysID] = [round(dealInfo.m_dPrice, 1),round(dealInfo.m_dPrice,1)]

    print(f"deal callback m_nOffsetFlag:[{dealInfo.m_nOffsetFlag}] m_strRemark:[{dealInfo.m_strRemark}], [{dealInfo.m_strInstrumentID}] [{dealInfo.m_strExchangeID}]  [{dealInfo.m_dPrice}]",)
    if dealInfo.m_strRemark not in  ['期货策略开多','期货策略开空']:
        return
    marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
    k = dealInfo.m_strInstrumentID+'.'+marekt
    if dealInfo.m_nOffsetFlag == 48  and  g.code.find(dealInfo.m_strInstrumentID)>=0:
        print("deal callback", dealInfo.m_dPrice)
        g.open_price = round(dealInfo.m_dPrice, 1)
        g.hold_price = round(dealInfo.m_dPrice,1)


def get_shape(CG,FL,MA_Line,FS,B1,B2):
    # 2.首次出现三色线，且在分时均线上方持续时间<15分钟，再度变成单红色线
    count = 0
    record = []
    compare_ma = []
    for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
        if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
            continue
        record.append(cg==fl==fs)
        compare_ma.append(min(cg,fl,fs)>ma)
    pre=None
    record.reverse()
    compare_ma.reverse()
    i = 0
    if not record:
        return 0, 99
    if not record[0]:
        return 0, 99
    if not compare_ma[0]:
        return 0, 99
    # 获取大于均线的三条线
    uprecord = []
    for r, cpma in zip(record, compare_ma):
        if not cpma:
            break
        uprecord.append(r)
    # 去除重复连续
    drop_uprecord = []
    for i in range(len(uprecord)):
        if i == 0 or uprecord[i] != uprecord[i-1]:
            drop_uprecord.append(uprecord[i])
    if drop_uprecord.count(False)!=1:
        return 0, 99
    else:
        return 1, uprecord.count(False)

# 1.现价线和FS CG FL（三色线）在分时均线下方 且B1-B2<0
# 2.首次出现单条红色线，且在分时均线下方持续时间<15分钟，再度变成三色线
# 3.分时均线价位—FS即时价位<5
def get_shape_kong(CG,FL,MA_Line,FS,B1,B2):
    count = 0
    record = []
    compare_ma = []
    for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
        if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
            continue
        record.append(not cg==fl==fs)
        compare_ma.append(max(cg,fl,fs)<ma)
    # record True 三色线
    pre=None
    record.reverse()
    compare_ma.reverse()
    i = 0
    if not record:
        return 0, 99
    if not record[0]:
        return 0, 99
    if not compare_ma[0]:
        return 0, 99
    
    uprecord = []
    for r, cpma in zip(record, compare_ma):
        if not cpma:
            break
        uprecord.append(r)
    # 去除重复连续
    drop_uprecord = []
    for i in range(len(uprecord)):
        if i == 0 or uprecord[i] != uprecord[i-1]:
            drop_uprecord.append(uprecord[i])
    if drop_uprecord.count(False)!=1:
        return 0, 99
    else:
        return 1, uprecord.count(False)


def REF(S, N=1):          #对序列整体下移动N,返回序列(shift后会产生NAN)    
    return pd.Series(S).shift(N).values  
def SMA(S, N, M=1):       #中国式的SMA,至少需要120周期才精确 (雪球180周期)    alpha=1/(1+com)    
    return pd.Series(S).ewm(alpha=M/N,adjust=False).mean().values           #com=N-M/M
def SUM(S, N):            #对序列求N天累计和，返回序列    N=0对序列所有依次求和         
    return pd.Series(S).rolling(N).sum().values if N>0 else pd.Series(S).cumsum().values  
def HHV(S,N):             #HHV(C, 5) 最近5天收盘最高价        
    return pd.Series(S).rolling(N).max().values     

def LLV(S,N):             #LLV(C, 5) 最近5天收盘最低价     
    return pd.Series(S).rolling(N).min().values    
    
def HHVBARS(S,N):         #求N周期内S最高值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]),raw=True).values 

def LLVBARS(S,N):         #求N周期内S最低值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]),raw=True).values    
  
def MA(S,N):              #求序列的N日简单移动平均值，返回序列                    
    return pd.Series(S).rolling(N).mean().values 

# def EMA(S,N):             #指数移动平均,为了精度 S>4*N  EMA至少需要120周期     alpha=2/(span+1)    
    # return pd.Series(S).ewm(span=N, adjust=False).mean().values    
def EMA(source, N:int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    """
    # if not isinstance(source, np.ndarray):
    #     source = np.array(source)
    M = 2
    if N<M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N-1
    M = 2
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M*x+d*temp)/(N+1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i+1-34):i+1]
        result_list.append(min(l))
    return pd.Series(result_list, index=index)
    
    
def PyHHV(S, N):
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i+1-34):i+1]
        result_list.append(max(l))
    return pd.Series(result_list, index=index)

def cal_vba(ContextInfo, price):
    C = CLOSE = price['close']
    HIGH = price['close']
    LOW = price['close']
    AMT = price['amount']
    VOL = price['volume']
    MA_Line = SUM(C*VOL,0)/SUM(VOL, 0)
    CG=MA(C,21)
    FL=HHV(CG,3)
    FS=CG-(FL-CG)
    
    VA6=(2*CLOSE+HIGH+LOW)/4
    VA8=LLV(LOW,34)
    VARB=HHV(HIGH,34)
    VARC=EMA((VA6-VA8)/(VARB-VA8)*100,13)
    VARD=EMA(0.667*REF(VARC,1)+0.333*VARC,2)
    生命线:EMA(VARD,10)

    VAR1=HHV(HIGH,9)-LLV(LOW,9)
    VAR2=HHV(HIGH,9)-CLOSE
    VAR3=CLOSE-LLV(LOW,9)
    VAR4=((VAR2)/(VAR1))*(100)-70
    VAR5=((CLOSE-LLV(LOW,60))/(HHV(HIGH,60)-LLV(LOW,60)))*(100)


    VAR6=((2)*(CLOSE)+HIGH+LOW)/(4)
    index = VAR6.index
    VAR6 = pd.Series([round(v,0) for v in VAR6],index=index)
    VAR7=SMA(((VAR3)/(VAR1))*(100),3,1)
    VAR8=PyLLV(CLOSE,min(34, len(LOW)))
    VAR9=SMA(VAR7,3,1)-SMA(VAR4,9,1)
    VAR10 = pd.Series([v-100 if v>100 else 0 for v in VAR9])
    VAR11=PyHHV(CLOSE,min(34, len(HIGH)))
    vv = ((VAR6-VAR8)/(VAR11-VAR8))*(100)
    vv=vv.fillna(0)
    
    vv=vv.replace([np.inf, -np.inf], np.nan).fillna(0)
    B1=EMA(vv,8)
    B2=EMA(B1,5)
    return CG,FL,MA_Line,FS,B1,B2
 

