import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def test_dict_format_handling():
    """
    测试字典格式数据处理
    """
    # 模拟get_market_data_ex返回的字典格式数据
    mock_data = {
        'code1.SF': pd.DataFrame({
            'open': [100, 101, 102, 103, 104],
            'high': [105, 106, 107, 108, 109],
            'low': [95, 96, 97, 98, 99],
            'close': [102, 103, 104, 105, 106],
            'volume': [1000, 1200, 1100, 1300, 1500]
        }),
        'code2.SF': pd.DataFrame({
            'open': [200, 201, 202, 203, 204],
            'high': [205, 206, 207, 208, 209],
            'low': [195, 196, 197, 198, 199],
            'close': [202, 203, 204, 205, 206],
            'volume': [2000, 2200, 2100, 2300, 2500]
        })
    }
    
    # 测试字典格式数据访问
    code = 'code1.SF'
    
    # 正确的访问方式
    if code in mock_data:
        price_data = mock_data[code]
        print(f"DataFrame shape: {price_data.shape}")
        print(f"有效访问DataFrame: \n{price_data.head()}")
        
        # 访问特定列
        close_prices = price_data['close']
        print(f"\n收盘价: \n{close_prices}")
        
        # 访问最后一个收盘价
        latest_close = close_prices.iloc[-1]
        print(f"\n最新收盘价: {latest_close}")
    else:
        print(f"代码 {code} 不在字典中")
    
    # 错误的访问方式 - 尝试直接访问DataFrame属性
    try:
        shape = mock_data.shape
        print(f"Shape: {shape}")
    except AttributeError as e:
        print(f"\n错误的访问方式 - 尝试直接访问字典的shape属性: {e}")
    
    # 修复后的正确访问方式
    try:
        code = 'code1.SF'
        price_data = mock_data[code] if code in mock_data else pd.DataFrame()
        if not price_data.empty:
            shape = price_data.shape
            print(f"\n修复后正确访问: DataFrame shape: {shape}")
        else:
            print("\n无数据")
    except Exception as e:
        print(f"\n访问出错: {e}")

def test_red_white_line_calculation():
    """
    测试红白线计算函数是否能处理字典格式的数据
    """
    # 模拟calculate_red_white_lines_exact函数
    def calculate_red_white_lines_mock(df):
        if df.empty:
            return pd.Series(index=df.index), pd.Series(index=df.index)
        
        # 简单的红白线计算
        white_line = df['close'].rolling(window=5).mean()
        red_line = df['close'].rolling(window=10).mean()
        
        return white_line, red_line
    
    # 模拟get_market_data_ex返回的字典格式数据
    mock_data = {
        'code1.SF': pd.DataFrame({
            'open': np.random.rand(20) * 10 + 100,
            'high': np.random.rand(20) * 10 + 105,
            'low': np.random.rand(20) * 10 + 95,
            'close': np.random.rand(20) * 10 + 100,
            'volume': np.random.rand(20) * 1000 + 1000
        })
    }
    
    code = 'code1.SF'
    
    # 处理字典格式
    price_5m = mock_data[code] if code in mock_data else pd.DataFrame()
    
    # 验证数据是否有效
    if price_5m.empty:
        print("无法获取K线数据，跳过策略执行")
        return
    
    # 计算红白线
    white_line, red_line = calculate_red_white_lines_mock(price_5m)
    
    # 获取当前最新价格
    current_price = price_5m['close'].iloc[-1]
    
    # 获取最近的红白线值
    current_white_line = white_line.iloc[-1] if not pd.isna(white_line.iloc[-1]) else None
    current_red_line = red_line.iloc[-1] if not pd.isna(red_line.iloc[-1]) else None
    
    print(f"\n当前价格: {current_price:.2f}")
    print(f"当前白线值: {current_white_line:.2f}")
    print(f"当前红线值: {current_red_line:.2f}")
    
    # 绘制图表
    plt.figure(figsize=(12, 6))
    plt.plot(price_5m.index, price_5m['close'], label='Close Price')
    plt.plot(white_line.index, white_line, label='White Line', color='gray')
    plt.plot(red_line.index, red_line, label='Red Line', color='red')
    plt.legend()
    plt.title('Price and Red/White Lines')
    plt.savefig('red_white_lines.png')
    print("\n图表已保存为 red_white_lines.png")

def test_check_stop_loss():
    """
    测试止损函数是否能处理字典格式的数据
    """
    class MockContextInfo:
        def get_market_data_ex(self, fields, codes, period='5m', count=30):
            # 返回模拟的字典格式数据
            return {
                'code1.SF': pd.DataFrame({
                    'open': np.random.rand(count) * 10 + 100,
                    'high': np.random.rand(count) * 10 + 105,
                    'low': np.random.rand(count) * 10 + 95,
                    'close': np.random.rand(count) * 10 + 100,
                    'volume': np.random.rand(count) * 1000 + 1000
                })
            }
    
    # 模拟calculate_red_white_lines_exact函数
    def calculate_red_white_lines_mock(df):
        if df.empty:
            return pd.Series(index=df.index), pd.Series(index=df.index)
        
        # 简单的红白线计算
        white_line = df['close'].rolling(window=5).mean()
        red_line = df['close'].rolling(window=10).mean()
        
        return white_line, red_line
    
    # 模拟calculate_atr函数
    def calculate_atr_mock(df, period=14):
        if df.empty or len(df) < period:
            return 0
        
        # 简单的ATR计算
        tr1 = df['high'] - df['low']
        tr2 = abs(df['high'] - df['close'].shift(1))
        tr3 = abs(df['low'] - df['close'].shift(1))
        
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.rolling(window=period).mean().iloc[-1]
        
        return atr
    
    # 模拟止损检查函数
    def check_stop_loss_mock(context_info, current_price):
        # 获取5分钟K线数据
        price_5m_dict = context_info.get_market_data_ex(
            ['open', 'high', 'low', 'close', 'volume'], 
            ['code1.SF'], 
            period='5m',
            count=30
        )
        
        # 处理get_market_data_ex返回的字典格式
        price_5m = price_5m_dict['code1.SF'] if 'code1.SF' in price_5m_dict else pd.DataFrame()
        
        if price_5m.empty or len(price_5m) < 10:
            print("止损检查: 无法获取足够的5分钟K线数据，跳过止损检查")
            return False
        
        # 计算红白线
        white_line, red_line = calculate_red_white_lines_mock(price_5m)
        
        # 获取当前红白线
        current_white_line = white_line.iloc[-1] if not pd.isna(white_line.iloc[-1]) else None
        current_red_line = red_line.iloc[-1] if not pd.isna(red_line.iloc[-1]) else None
        
        # 计算ATR用于动态止损
        atr = calculate_atr_mock(price_5m, period=14)
        
        print(f"\n止损检查测试:")
        print(f"成功获取5分钟K线数据，共{len(price_5m)}条记录")
        print(f"当前价格: {current_price:.2f}")
        print(f"当前白线值: {current_white_line:.2f}")
        print(f"当前红线值: {current_red_line:.2f}")
        print(f"ATR值: {atr:.2f}")
        
        return True
    
    # 执行测试
    context = MockContextInfo()
    current_price = 105.0
    
    result = check_stop_loss_mock(context, current_price)
    print(f"止损检查测试结果: {'成功' if result else '失败'}")

if __name__ == "__main__":
    print("===== 测试字典格式数据处理 =====")
    test_dict_format_handling()
    
    print("\n\n===== 测试红白线计算 =====")
    test_red_white_line_calculation()
    
    print("\n\n===== 测试止损检查 =====")
    test_check_stop_loss() 