#encoding:gbk

# 全局参数配置
open_long_num = 2
open_short_num = 2
hand = 1  # 交易张数
moving_tick = 0.020  # 移动止损点位
fs_line = 0.010  # FS即时价位—分时均线价位<0.1
sleep_time = 5  # 分钟 1分钟内不连续开同方向仓位
stoploss_ratio = -20  # % 止损比例
stopprofit_ratio = 20  # % 盈利比例
expiry_days_threshold = 10  # 距离到期日前多少天自动切换合约，可调参数
hold_days_limit = 5  # 持仓最大天数，可调参数

enable_ma55_filter = False  # 是否启用MA55过滤条件 or True False

# 账户配置 - 已设置为实际账户
account = "***********"  # 实际账户ID

# 重要提示：以下函数在生产环境中需要替换为实际的交易平台函数
# 1. passorder - 下单函数
# 2. get_stock_list_in_sector - 获取板块股票列表
# 3. download_history_data - 下载历史数据（可选）

import math
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class G():
    pass

g = G()
g.buy_long = 0
g.buy_short = 0
g.hold = 0
g.hold_price = 0
g.open_price = 0
g.trace_time_long = 0
g.trace_time_short = 0
g.opened_t = []
g.hold_code = ''

# 模拟的获取板块股票列表函数
def get_stock_list_in_sector(sector_name):
    """
    模拟的获取板块股票列表函数
    在实际环境中，这个函数应该由交易平台提供
    """
    try:
        # 在实际环境中，这里应该调用平台提供的函数
        # 现在返回空列表，避免运行时错误
        print(f"警告: get_stock_list_in_sector函数为模拟版本，板块: {sector_name}")
        return []
    except Exception as e:
        print(f"获取板块股票列表时出错: {e}")
        return []

# 安全的下单函数包装器
def safe_passorder(order_type, market, acct, code, price_type, price, volume, stop_price, order_flag, note, context):
    """
    安全的下单函数包装器，包含错误处理
    """
    try:
        print(f"准备下单: {note}")
        print(f"订单参数: 类型={order_type}, 市场={market}, 账户={acct}, 合约={code}")
        print(f"价格类型={price_type}, 价格={price}, 数量={volume}")

        # 检查必要的参数
        if not code:
            print("错误: 合约代码为空")
            return None

        if not acct:
            print("错误: 账户为空")
            return None

        # 调用实际的下单函数
        try:
            # 尝试实际下单 - 生产环境
            try:
                order_id = passorder(order_type, market, acct, code, price_type, price, volume, stop_price, order_flag, note, context)
                print(f"实际下单成功: {note}, 订单ID: {order_id}")
                return order_id
            except NameError:
                # 如果passorder函数不存在，使用模拟模式
                print("警告: passorder函数未定义，使用模拟模式")
                print(f"模拟下单: {note}")
                order_id = f"MOCK_ORDER_{int(time.time())}"
                return order_id
            except Exception as e:
                print(f"实际下单失败: {e}")
                # 下单失败时不使用模拟，直接返回None
                return None

        except Exception as e:
            print(f"下单过程异常: {e}")
            return None

    except Exception as e:
        print(f"下单包装器异常: {e}")
        return None

def init(ContextInfo):
    g.remark = ContextInfo.request_id[-10:]
    g.call_one = None
    g.put_one = None
    ContextInfo.set_account(account)  # 设置账户ID，确保account已定义
    g.undl_code = g.code = g.stock = ContextInfo.stockcode + '.' + ContextInfo.market  # 设置标的代码和市场信息，确保g.undl_code已定义
    g.curr_hold = None

def after_init(ContextInfo):
    # 下载历史数据（如果需要的话）
    try:
        # 注释掉download_history_data，因为这个函数可能不存在
        # download_history_data(g.code, '1d', '********', '********')
        pass
    except Exception as e:
        print(f"下载历史数据时出错: {e}")

    # 返回交易日期信息
    try:
        return ContextInfo.get_trading_dates('SHO', '', '********', count=2, period='1d')
    except Exception as e:
        print(f"获取交易日期时出错: {e}")
        return None, None



def calculate_wenhua_lines(price_data):
    """严格按照文华指标计算红白线值"""
    if len(price_data) < 10:  # 需要足够的历史数据
        return 0, 0, 0
        
    # 获取数据
    high = price_data['high'].values
    low = price_data['low'].values
    open_price = price_data['open'].values
    close = price_data['close'].values
    
    # 初始化结果数组
    h1 = np.zeros(len(price_data))
    l1 = np.zeros(len(price_data))
    h2 = np.zeros(len(price_data))
    l2 = np.zeros(len(price_data))
    k1 = np.zeros(len(price_data))
    k2 = np.zeros(len(price_data))
    g = np.zeros(len(price_data))
    
    # 计算HX和LX
    hx = np.zeros(len(price_data))
    lx = np.zeros(len(price_data))
    
    for i in range(len(price_data)):
        if i >= 1:
            hx[i] = max(high[i-1:i+1])  # HHV(HIGH,2)
            lx[i] = min(low[i-1:i+1])   # LLV(LOW,2)
        else:
            hx[i] = high[i]
            lx[i] = low[i]
    
    # 计算H1和L1
    for i in range(5, len(price_data)):
        # H1条件: IFELSE(HX<REF(HX,1)&&HX<REF(HX,2)&&HX<REF(HX,4)&&LX<REF(LX,1)&&LX<REF(LX,3)&&LX<REF(LX,5)&&OPEN>CLOSE&&(HHV(OPEN,0)-CLOSE)>0,REF(HX,4),0)
        h1_condition = (
            hx[i] < hx[i-1] and 
            hx[i] < hx[i-2] and 
            hx[i] < hx[i-4] and 
            lx[i] < lx[i-1] and 
            lx[i] < lx[i-3] and 
            lx[i] < lx[i-5] and 
            open_price[i] > close[i] and 
            (max(open_price[:i+1]) - close[i]) > 0
        )
        
        if h1_condition:
            h1[i] = hx[i-4]  # REF(HX,4)
        
        # L1条件: IFELSE(LX>REF(LX,1)&&LX>REF(LX,3)&&LX>REF(LX,5)&&HX>REF(HX,1)&&HX>REF(HX,2)&&HX>REF(HX,4)&&OPEN<CLOSE&&(CLOSE-LLV(OPEN,0))>0,REF(LX,4),0)
        l1_condition = (
            lx[i] > lx[i-1] and 
            lx[i] > lx[i-3] and 
            lx[i] > lx[i-5] and 
            hx[i] > hx[i-1] and 
            hx[i] > hx[i-2] and 
            hx[i] > hx[i-4] and 
            open_price[i] < close[i] and 
            (close[i] - min(open_price[:i+1])) > 0
        )
        
        if l1_condition:
            l1[i] = lx[i-4]  # REF(LX,4)
    
    # 计算H2和L2 (VALUEWHEN函数)
    last_h1 = 0
    last_l1 = 0
    for i in range(len(price_data)):
        if h1[i] > 0:
            last_h1 = h1[i]
        h2[i] = last_h1
        
        if l1[i] > 0:
            last_l1 = l1[i]
        l2[i] = last_l1
    
    # 计算K1
    for i in range(len(price_data)):
        if h2[i] > 0 and close[i] > h2[i]:
            k1[i] = -3
        elif l2[i] > 0 and close[i] < l2[i]:
            k1[i] = 1
        else:
            k1[i] = 0
    
    # 计算K2 (VALUEWHEN函数)
    last_k1 = 0
    for i in range(len(price_data)):
        if k1[i] != 0:
            last_k1 = k1[i]
        k2[i] = last_k1
    
    # 计算G
    for i in range(len(price_data)):
        if k2[i] == 1:
            g[i] = h2[i]  # G:=IFELSE(K2=1,H2,L2)
        else:
            g[i] = l2[i]
    
    # 获取最新值
    latest_k2 = k2[-1]
    latest_g = g[-1]
    
    # 根据K2值确定显示的颜色
    white_line = latest_g if latest_k2 == 1 else None
    red_line = latest_g if latest_k2 == -3 else None
    
    # 打印计算过程
    print("\n文华指标计算过程:")
    print(f"1. 基础数据:")
    print(f"   最新开盘价: {open_price[-1]:.4f}")
    print(f"   最新收盘价: {close[-1]:.4f}")
    print(f"   2周期最高价HX: {hx[-1]:.4f}")
    print(f"   2周期最低价LX: {lx[-1]:.4f}")
    print(f"2. H1和L1值:")
    print(f"   H1: {h1[-1]:.4f}")
    print(f"   L1: {l1[-1]:.4f}")
    print(f"3. H2和L2值:")
    print(f"   H2: {h2[-1]:.4f}")
    print(f"   L2: {l2[-1]:.4f}")
    print(f"4. K1和K2值:")
    print(f"   K1: {k1[-1]}")
    print(f"   K2: {latest_k2}")
    print(f"5. G值: {latest_g:.4f}")
    print(f"6. 当前显示:")
    print(f"   白色线: {f'{white_line:.4f}' if white_line is not None else 'None'}")
    print(f"   红色线: {f'{red_line:.4f}' if red_line is not None else 'None'}")
    
    return white_line, red_line, latest_k2

def handlebar(ContextInfo):
    # 获取当前时间
    current_time = str(ContextInfo.barpos)  # 确保是字符串类型
    
    # 移除尾盘强制平仓相关逻辑，保留时间止损
    # 原有如下代码需删除：
    # if current_time >= '145400': ...
    # if current_time >= '145000': ...
    # if g.hold == 1 and current_time >= '145500': ...
    # if g.hold == -1 and current_time >= '145500': ...
    # 直接跳过这些时间段判断和强平逻辑

    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    bar_date = str(timetag)
    
    if not ContextInfo.is_last_bar():
        return

    current_date = time.strftime("%Y%m%d")  # 获取当前日期
    current_weekday = datetime.now().weekday()  # 获取当前星期几

    # 检查是否为交易日（假设周一到周五为交易日）
    if current_weekday >= 5:  # 0=周一, 1=周二, ..., 6=周日
        print("今天是周末，市场休市，无法生成信号。")
        return

    start, end = after_init(ContextInfo)
    
    while True:  # 循环等待数据
        try:
            # 获取当前时间
            current_time = time.strftime("%H%M%S")
            
            # 修改获取K线数据的方式，添加'open'字段
            yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y%m%d")
            price_5m = ContextInfo.get_market_data_ex(
                ['close', 'high', 'low', 'open'],  # 添加'open'字段
                [g.code], 
                period='5m',
                start_time=yesterday + '090000',
                end_time=current_date + '150000'
            )[g.code]
            
            # 获取30分钟K线数据时也添加'open'字段
            last_day_price_30m = ContextInfo.get_market_data_ex(
                ['close', 'high', 'low', 'open'],  # 添加'open'字段
                [g.code], 
                period='30m',
                start_time=yesterday + '140000',
                end_time=yesterday + '150000'
            )[g.code]

            today_price_30m = ContextInfo.get_market_data_ex(
                ['close', 'high', 'low', 'open'],  # 添加'open'字段
                [g.code], 
                period='30m',
                start_time=current_date + '090000',
                end_time=current_date + '150000'
            )[g.code]

            # 合并昨天和今天的30分钟K线数据
            if not last_day_price_30m.empty:
                price_30m = pd.concat([last_day_price_30m.iloc[-1:], today_price_30m])
                print(f"成功获取昨日最后一根30分钟K线，时间: {last_day_price_30m.index[-1]}")
            else:
                price_30m = today_price_30m
                print("未能获取昨日30分钟K线数据")

            # 打印获取到的数据信息
            print(f"当前时间: {current_time}")
            print(f"5分钟数据行数: {len(price_5m) if not price_5m.empty else 0}")
            print(f"30分钟数据行数: {len(price_30m) if not price_30m.empty else 0}")

            # 根据时间段来判断是否需要检查K线数量
            if current_time >= '100000':  # 10:00以后才检查30分钟K线数量
                if len(price_5m) < 2:  # 至少需要2根5分钟K线
                    print("5分钟数据不足，等待更多数据")
                    time.sleep(120)  # 等待120秒后重试
                    continue  # 继续循环，重新获取数据
                    
                if len(price_30m) < 2:  # 至少需要2根30分钟K线
                    print("30分钟数据不足，等待更多数据")
                    time.sleep(120)  # 等待120秒后重试
                    continue  # 继续循环，重新获取数据
            else:
                # 开盘初期，只检查5分钟K线
                if len(price_5m) < 2:
                    print("等待更多5分钟K线数据...")
                    time.sleep(60)  # 等待60秒后重试
                    continue
                # 开盘初期，如果30分钟数据不足，只使用5分钟信号
                if len(price_30m) < 2:
                    print("开盘初期，30分钟数据不足，仅使用5分钟K线信号")
                else:
                    print("开盘初期，使用5分钟和30分钟K线数据")

            break  # 数据足够，退出循环

        except Exception as e:
            print(f"获取K线数据时发生错误: {str(e)}")
            return

    # 检查数据是否为空
    if price_5m.empty:
        print("5分钟数据为空，无法生成信号")
    else:
        print("成功获取5分钟数据")
        print(f"5分钟数据时间范围: {price_5m.index[0]} 到 {price_5m.index[-1]}")

    if price_30m.empty:
        print("30分钟数据为空，无法生成信号")
    else:
        print("成功获取30分钟数据")
        print(f"30分钟数据时间范围: {price_30m.index[0]} 到 {price_30m.index[-1]}")

    if price_5m.empty or price_30m.empty:
        print("5分钟或30分钟数据为空，无法生成信号")
        return

    # 计算文华指标红白线值
    white_line_5m, red_line_5m, k2_5m = calculate_wenhua_lines(price_5m)
    white_line_30m, red_line_30m, k2_30m = calculate_wenhua_lines(price_30m)

    # 计算55日均线
    MA55 = MA(price_5m['close'], 55)
    latest_ma55 = MA55[-1] if len(MA55) > 0 else 0

    # 获取最新收盘价
    latest_close_5m = price_5m['close'][-1]
    latest_close_30m = price_30m['close'][-1]

    # 计算K2序列，严格用前一根K2和当前K2判断信号
    def get_k2_series(price_data):
        """计算完整的K2序列"""
        if len(price_data) < 10:
            return []
        
        # 获取数据
        high = price_data['high'].values
        low = price_data['low'].values
        open_price = price_data['open'].values
        close = price_data['close'].values
        
        # 初始化结果数组
        h1 = np.zeros(len(price_data))
        l1 = np.zeros(len(price_data))
        h2 = np.zeros(len(price_data))
        l2 = np.zeros(len(price_data))
        k1 = np.zeros(len(price_data))
        k2 = np.zeros(len(price_data))
        
        # 计算HX和LX
        hx = np.zeros(len(price_data))
        lx = np.zeros(len(price_data))
        
        for i in range(len(price_data)):
            if i >= 1:
                hx[i] = max(high[i-1:i+1])  # HHV(HIGH,2)
                lx[i] = min(low[i-1:i+1])   # LLV(LOW,2)
            else:
                hx[i] = high[i]
                lx[i] = low[i]
        
        # 计算H1和L1
        for i in range(5, len(price_data)):
            # H1条件
            h1_condition = (
                hx[i] < hx[i-1] and 
                hx[i] < hx[i-2] and 
                hx[i] < hx[i-4] and 
                lx[i] < lx[i-1] and 
                lx[i] < lx[i-3] and 
                lx[i] < lx[i-5] and 
                open_price[i] > close[i] and 
                (max(open_price[:i+1]) - close[i]) > 0
            )
            
            if h1_condition:
                h1[i] = hx[i-4]  # REF(HX,4)
            
            # L1条件
            l1_condition = (
                lx[i] > lx[i-1] and 
                lx[i] > lx[i-3] and 
                lx[i] > lx[i-5] and 
                hx[i] > hx[i-1] and 
                hx[i] > hx[i-2] and 
                hx[i] > hx[i-4] and 
                open_price[i] < close[i] and 
                (close[i] - min(open_price[:i+1])) > 0
            )
            
            if l1_condition:
                l1[i] = lx[i-4]  # REF(LX,4)
        
        # 计算H2和L2 (VALUEWHEN函数)
        last_h1 = 0
        last_l1 = 0
        for i in range(len(price_data)):
            if h1[i] > 0:
                last_h1 = h1[i]
            h2[i] = last_h1
            
            if l1[i] > 0:
                last_l1 = l1[i]
            l2[i] = last_l1
        
        # 计算K1
        for i in range(len(price_data)):
            if h2[i] > 0 and close[i] > h2[i]:
                k1[i] = -3
            elif l2[i] > 0 and close[i] < l2[i]:
                k1[i] = 1
            else:
                k1[i] = 0
        
        # 计算K2 (VALUEWHEN函数)
        last_k1 = 0
        for i in range(len(price_data)):
            if k1[i] != 0:
                last_k1 = k1[i]
            k2[i] = last_k1
        
        return k2.tolist()

    k2_series_5m = get_k2_series(price_5m)
    k2_series_30m = get_k2_series(price_30m) if len(price_30m) >= 10 else []

    prev_k2_5m = k2_series_5m[-2] if len(k2_series_5m) > 1 else 0
    k2_5m = k2_series_5m[-1] if k2_series_5m else 0
    prev_k2_30m = k2_series_30m[-2] if len(k2_series_30m) > 1 else 0
    k2_30m = k2_series_30m[-1] if k2_series_30m else 0

    # 买信号：K2从0变为1，且收盘价>=MA55
    if enable_ma55_filter:
        k2_buy_signal_5m = (prev_k2_5m == 0 and k2_5m == 1 and latest_close_5m >= latest_ma55)
        k2_sell_signal_5m = (prev_k2_5m == 0 and k2_5m == -3 and latest_close_5m <= latest_ma55)
        k2_buy_signal_30m = (prev_k2_30m == 0 and k2_30m == 1 and latest_close_30m >= latest_ma55) if len(price_30m) >= 10 else False
        k2_sell_signal_30m = (prev_k2_30m == 0 and k2_30m == -3 and latest_close_30m <= latest_ma55) if len(price_30m) >= 10 else False
    else:
        k2_buy_signal_5m = (prev_k2_5m == 0 and k2_5m == 1)
        k2_sell_signal_5m = (prev_k2_5m == 0 and k2_5m == -3)
        k2_buy_signal_30m = (prev_k2_30m == 0 and k2_30m == 1) if len(price_30m) >= 10 else False
        k2_sell_signal_30m = (prev_k2_30m == 0 and k2_30m == -3) if len(price_30m) >= 10 else False
    
    # 最终信号判断
    buy_signal = k2_buy_signal_5m or k2_buy_signal_30m
    sell_signal = k2_sell_signal_5m or k2_sell_signal_30m

    # 打印最终信号
    print(f"最终买入信号: {buy_signal}")
    print(f"最终卖出信号: {sell_signal}")

    # 打印K2跳变、收盘价、MA55的值，便于分析信号被过滤的原因
    print(f"K2跳变检测: prev_k2_5m={prev_k2_5m}, k2_5m={k2_5m}, latest_close_5m={latest_close_5m:.4f}, latest_ma55={latest_ma55:.4f}")
    print(f"K2跳变检测: prev_k2_30m={prev_k2_30m}, k2_30m={k2_30m}, latest_close_30m={latest_close_30m:.4f}, latest_ma55={latest_ma55:.4f}")

    # 更新prev_k2
    g.prev_k2_5m = k2_5m
    g.prev_k2_30m = k2_30m

    # 打印K2信号检测结果
    print(f"K2值变化检测: 5分钟前值={g.prev_k2_5m}, 当前值={k2_5m}, 30分钟前值={g.prev_k2_30m}, 当前值={k2_30m}")
    print(f"30分钟数据充足性: {len(price_30m) >= 10}")
    if k2_buy_signal_5m or k2_buy_signal_30m:
        print(f"K2看涨信号触发: 5分钟={k2_buy_signal_5m}, 30分钟={k2_buy_signal_30m}")
    if k2_sell_signal_5m or k2_sell_signal_30m:
        print(f"K2看跌信号触发: 5分钟={k2_sell_signal_5m}, 30分钟={k2_sell_signal_30m}")
    
    if buy_signal:
        print(f"\n触发买入认购期权信号! (K2看涨信号)")
        # 开多逻辑 - 只有在没有持仓时才开仓
        time_since_last_long = time.time() - g.trace_time_long
        if g.hold == 0 and time_since_last_long > sleep_time * 60:
            call_one, put_one = get_option_real_one(ContextInfo)
            if call_one is None:
                print("警告：无法获取认购期权合约，跳过开仓")
                return
            g.call_one = call_one
            safe_passorder(50, 1101, account, call_one, 12, 0, hand, '', 1, '期权策略开多' + g.remark, ContextInfo)
            g.curr_hold = call_one
            g.hold = 1
            g.trace_time_long = time.time()
            g.opened_t.append(bar_date)
            g.hold_price = latest_close_5m
            print(f'{call_one} 买入认购期权，行权价: {ContextInfo.get_option_detail_data(call_one)["OptExercisePrice"]}')
        else:
            if g.hold != 0:
                print(f"当前持仓状态: {g.hold}，无法开仓")
            else:
                print(f"距离上次开多时间不足{sleep_time}分钟，剩余{int(sleep_time * 60 - time_since_last_long)}秒")
            return

    if sell_signal:
        print(f"\n触发买入认沽期权信号! (K2看跌信号)")
        # 开空逻辑 - 只有在没有持仓时才开仓
        time_since_last_short = time.time() - g.trace_time_short
        if g.hold == 0 and time_since_last_short > sleep_time * 60:
            call_one, put_one = get_option_real_one(ContextInfo)
            if put_one is None:
                print("警告：无法获取认沽期权合约，跳过开仓")
                return
            g.put_one = put_one
            safe_passorder(50, 1101, account, put_one, 12, 0, hand, '', 1, '期权策略开空' + g.remark, ContextInfo)
            g.curr_hold = put_one
            g.hold = -1
            g.trace_time_short = time.time()
            g.opened_t.append(bar_date)
            g.hold_price = latest_close_5m
            print(f'{put_one} 买入认沽期权，行权价: {ContextInfo.get_option_detail_data(put_one)["OptExercisePrice"]}')
        else:
            if g.hold != 0:
                print(f"当前持仓状态: {g.hold}，无法开仓")
            else:
                print(f"距离上次开空时间不足{sleep_time}分钟，剩余{int(sleep_time * 60 - time_since_last_short)}秒")
            return

    # 检查合约到期日并自动切换
    if g.hold != 0:
        # 每次运行时都检查当前持仓合约的到期日
        try:
            current_contract_detail = ContextInfo.get_option_detail_data(g.curr_hold)
            current_end_date = current_contract_detail['EndDelivDate']
            current_end_date_str = str(current_end_date)
            
            # 计算距离到期日的天数
            end_date_obj = datetime.strptime(current_end_date_str, '%Y%m%d')
            current_date_obj = datetime.now()
            days_to_expiry = (end_date_obj - current_date_obj).days
            
            print(f"合约到期检查: {g.curr_hold}, 到期日: {current_end_date_str}, 距离到期: {days_to_expiry}天")
            
            # 如果距离到期日小于等于设定天数，切换到下个月合约
            if days_to_expiry <= expiry_days_threshold:
                print(f"距离到期日不足{expiry_days_threshold}天，切换到下个月合约")
                # 平仓当前持仓
                if g.hold == 1:  # 持有多头
                    safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, '到期前平仓认购' + g.remark, ContextInfo)
                    print(f"平仓认购期权: {g.curr_hold}")
                elif g.hold == -1:  # 持有空头
                    safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, '到期前平仓认沽' + g.remark, ContextInfo)
                    print(f"平仓认沽期权: {g.curr_hold}")
                
                # 重置持仓状态
                g.hold = 0
                g.curr_hold = None
                g.hold_price = 0
                return  # 平仓后直接返回，等待下次信号
                
        except Exception as e:
            print(f"检查合约到期日时出错: {e}")

    # ====== 最佳止盈止损方案实现开始 ======
    if g.hold != 0 and g.curr_hold is not None and g.hold_price > 0:
        # 1. 固定止损
        if g.hold == 1:
            profit_ratio = (latest_close_5m - g.hold_price) / g.hold_price * 100
        else:
            profit_ratio = (g.hold_price - latest_close_5m) / g.hold_price * 100

        # 固定止损参数
        best_stoploss_ratio = -30  # -30%
        # 浮盈追踪止盈参数
        best_trail_start = 50      # +50%启动追踪
        best_trail_drawdown = 20   # 回撤20%止盈
        # 时间止损参数
        best_max_hold_days = hold_days_limit

        # 记录持仓开始时间
        if not hasattr(g, 'open_time') or g.open_time is None:
            g.open_time = datetime.now()
        hold_days = (datetime.now() - g.open_time).days

        # 固定止损
        if profit_ratio <= best_stoploss_ratio:
            print(f'固定止损触发，平仓，盈亏比: {profit_ratio:.2f}%')
            safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, '最佳止损平仓' + g.remark, ContextInfo)
            g.hold = 0
            g.curr_hold = None
            g.hold_price = 0
            g.open_time = None
            g.max_profit_ratio = None
            return

        # 浮盈追踪止盈
        if not hasattr(g, 'max_profit_ratio') or g.max_profit_ratio is None:
            g.max_profit_ratio = profit_ratio
        else:
            g.max_profit_ratio = max(g.max_profit_ratio, profit_ratio)
        if g.max_profit_ratio >= best_trail_start and profit_ratio <= g.max_profit_ratio - best_trail_drawdown:
            print(f'浮盈追踪止盈触发，平仓，最高浮盈: {g.max_profit_ratio:.2f}%，当前盈亏: {profit_ratio:.2f}%')
            safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, '最佳追踪止盈平仓' + g.remark, ContextInfo)
            g.hold = 0
            g.curr_hold = None
            g.hold_price = 0
            g.open_time = None
            g.max_profit_ratio = None
            return

        # 时间止损
        if hold_days >= best_max_hold_days:
            print(f'时间止损触发，平仓，持仓天数: {hold_days}天')
            safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, '最佳时间止损平仓' + g.remark, ContextInfo)
            g.hold = 0
            g.curr_hold = None
            g.hold_price = 0
            g.open_time = None
            g.max_profit_ratio = None
            return
    # ====== 最佳止盈止损方案实现结束 ======

    # 基于K2信号变化的止盈止损逻辑
    if g.hold != 0 and g.curr_hold is not None:
        # 检查是否需要平仓并反向开仓
        if g.hold == 1:  # 持有多头（认购期权）
            # 如果出现"卖"字信号（K2从0变为-3），平仓认购并开仓认沽
            if k2_sell_signal_5m or (len(price_30m) >= 10 and k2_sell_signal_30m):
                print(f"\nK2信号变化止盈止损 - 平仓认购期权: {g.curr_hold}")
                safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, 'K2信号变化平仓认购' + g.remark, ContextInfo)
                g.hold = 0
                g.curr_hold = None
                g.hold_price = 0
                
                # 立即开仓认沽期权
                print(f"K2信号变化 - 开仓认沽期权")
                call_one, put_one = get_option_real_one(ContextInfo)
                if put_one is None:
                    print("警告：无法获取认沽期权合约，跳过反向开仓")
                    return
                g.put_one = put_one
                safe_passorder(50, 1101, account, put_one, 12, 0, hand, '', 1, 'K2信号变化开仓认沽' + g.remark, ContextInfo)
                g.curr_hold = put_one
                g.hold = -1
                g.trace_time_short = time.time()
                g.opened_t.append(bar_date)
                g.hold_price = latest_close_5m
                print(f'{put_one} 买入认沽期权，行权价: {ContextInfo.get_option_detail_data(put_one)["OptExercisePrice"]}')
                
        elif g.hold == -1:  # 持有空头（认沽期权）
            # 如果出现"买"字信号（K2从0变为1），平仓认沽并开仓认购
            if k2_buy_signal_5m or (len(price_30m) >= 10 and k2_buy_signal_30m):
                print(f"\nK2信号变化止盈止损 - 平仓认沽期权: {g.curr_hold}")
                safe_passorder(51, 1101, account, g.curr_hold, 12, 0, hand, '', 1, 'K2信号变化平仓认沽' + g.remark, ContextInfo)
                g.hold = 0
                g.curr_hold = None
                g.hold_price = 0
                
                # 立即开仓认购期权
                print(f"K2信号变化 - 开仓认购期权")
                call_one, put_one = get_option_real_one(ContextInfo)
                if call_one is None:
                    print("警告：无法获取认购期权合约，跳过反向开仓")
                    return
                g.call_one = call_one
                safe_passorder(50, 1101, account, call_one, 12, 0, hand, '', 1, 'K2信号变化开仓认购' + g.remark, ContextInfo)
                g.curr_hold = call_one
                g.hold = 1
                g.trace_time_long = time.time()
                g.opened_t.append(bar_date)
                g.hold_price = latest_close_5m
                print(f'{call_one} 买入认购期权，行权价: {ContextInfo.get_option_detail_data(call_one)["OptExercisePrice"]}')



# 辅助函数
def get_shape(CG, FL, MA_Line, FS, B1, B2):
    count = 0
    record = []
    compare_ma = []
    for cg, fl, ma, fs, b1, b2 in zip(CG, FL, MA_Line, FS, B1, B2):
        if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
            continue
        record.append(cg == fl == fs)
        compare_ma.append(min(cg, fl, fs) > ma)
    pre = None
    record.reverse()
    compare_ma.reverse()
    i = 0
    if not record:
        return 0, 99
    if not record[0]:
        return 0, 99
    if not compare_ma[0]:
        return 0, 99
    uprecord = []
    for r, cpma in zip(record, compare_ma):
        if not cpma:
            break
        uprecord.append(r)
    drop_uprecord = []
    for i in range(len(uprecord)):
        if i == 0 or uprecord[i] != uprecord[i - 1]:
            drop_uprecord.append(uprecord[i])
    if drop_uprecord.count(False) != 1:
        return 0, 99
    else:
        return 1, uprecord.count(False)

def get_shape_kong(CG, FL, MA_Line, FS, B1, B2):
    count = 0
    record = []
    compare_ma = []
    for cg, fl, ma, fs, b1, b2 in zip(CG, FL, MA_Line, FS, B1, B2):
        if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
            continue
        record.append(not cg == fl == fs)
        compare_ma.append(min(cg, fl, fs) < ma)
    pre = None
    for pos, b in enumerate(record):
        if pre is None:
            pre = b
            continue
        if b and pre == False:
            count += 1
            pre = b
            continue
        if not b and pos == len(record) - 1:
            count += 1
            pre = b
            continue
        pre = b
    return count, sum([1 for r, cp in zip(record, compare_ma) if not r and cp])

def order_callback(ContextInfo, orderInfo):
    print(orderInfo.m_strRemark, orderInfo.m_nOrderStatus, orderInfo.m_nOffsetFlag, orderInfo.m_dTradedPrice)
    if orderInfo.m_strRemark not in ['期权策略开多' + g.remark, '期权策略开空' + g.remark, 
                                   'K2信号变化开仓认购' + g.remark, 'K2信号变化开仓认沽' + g.remark,
                                   '到期前平仓认购' + g.remark, '到期前平仓认沽' + g.remark]:
        return
    marekt = {"SHFE": 'SF', "CZCE": 'ZF', "DCE": 'DF', "CFFEX": 'IF'}.get(orderInfo.m_strExchangeID, orderInfo.m_strExchangeID)
    k = orderInfo.m_strInstrumentID + '.' + marekt
    if k not in [g.call_one, g.put_one]:
        return
    if orderInfo.m_nOrderStatus == 56 and (orderInfo.m_strRemark.startswith('期权策略开多' + g.remark) or 
                                          orderInfo.m_strRemark.startswith('K2信号变化开仓认购' + g.remark)):
        g.buy_long += 1
        print(f"{g.code} 开多次数+1 {g.buy_long}")
    if orderInfo.m_nOrderStatus == 56 and (orderInfo.m_strRemark.startswith('期权策略开空' + g.remark) or 
                                          orderInfo.m_strRemark.startswith('K2信号变化开仓认沽' + g.remark)):
        g.buy_short += 1
        print(f"{g.code} 开空次数+1 {g.buy_short}")

    if orderInfo.m_nOrderStatus == 57 and orderInfo.m_nOffsetFlag == 48:
        g.hold = 0
        print("order_callback set 0")

def orderError_callback(ContextInfo, passOrderInfo, msg):
    if '期权策略' + g.remark in passOrderInfo.strategyName:
        g.hold = 0
        print("orderError_callback set 0", msg)
    if '期权策略开空' + g.remark in passOrderInfo.strategyName:
        g.buy_short += 1
        print(f"{g.code} 开空次数+1 {g.buy_short}")
    if '期权策略开多' + g.remark in passOrderInfo.strategyName:
        g.buy_long += 1
        print(f"{g.code} 开多次数+1 {g.buy_long}")

def deal_callback(ContextInfo, dealInfo):
    print(f"deal callback m_nOffsetFlag:[{dealInfo.m_nOffsetFlag}] m_strRemark:[{dealInfo.m_strRemark}], [{dealInfo.m_strInstrumentID}] [{dealInfo.m_strExchangeID}]  [{dealInfo.m_dPrice}]")
    if dealInfo.m_strRemark not in ['期权策略开多' + g.remark, '期权策略开空' + g.remark,
                                   'K2信号变化开仓认购' + g.remark, 'K2信号变化开仓认沽' + g.remark,
                                   '到期前平仓认购' + g.remark, '到期前平仓认沽' + g.remark]:
        return
    
    marekt = {"SHFE": 'SF', "CZCE": 'ZF', "DCE": 'DF', "CFFEX": 'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
    k = dealInfo.m_strInstrumentID + '.' + marekt
    if k not in [g.call_one, g.put_one]:
        return
    if dealInfo.m_nOffsetFlag == 48:
        print("deal callback", dealInfo.m_dPrice)
        g.open_price = round(dealInfo.m_dPrice, 4)
        g.hold_price = round(dealInfo.m_dPrice, 4)

def REF(S, N=1):  # 对序列整体下移动N,返回序列(shift后会产生NAN)    
    return pd.Series(S).shift(N).values  

def SMA(S, N, M=1):  # 中国式的SMA,至少需要120周期才精确 (雪球180周期)    
    return pd.Series(S).ewm(alpha=M/N, adjust=False).mean().values  

def SUM(S, N):  # 对序列求N天累计和，返回序列    
    return pd.Series(S).rolling(N).sum().values if N > 0 else pd.Series(S).cumsum().values  

def HHV(S, N):  # HHV(C, 5) 最近5天收盘最高价        
    return pd.Series(S).rolling(N).max().values     

def LLV(S, N):  # LLV(C, 5) 最近5天收盘最低价     
    return pd.Series(S).rolling(N).min().values    

def HHVBARS(S, N):  # 求N周期内S最高值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]), raw=True).values 

def LLVBARS(S, N):  # 求N周期内S最低值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]), raw=True).values    

def MA(S, N):  # 求序列的N日简单移动平均值，返回序列                    
    return pd.Series(S).rolling(N).mean().values  

def EMA(S, N):  # 指数移动平均,为了精度 S>4*N  EMA至少需要120周期     
    return pd.Series(S).ewm(span=N, adjust=False).mean().values     

def EMA(source, N: int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    """
    M = 2
    if N < M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N - 1
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M * x + d * temp) / (N + 1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i + 1 - 34):i + 1]
        result_list.append(min(l))
    return pd.Series(result_list, index=index)

def PyHHV(S, N):
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i + 1 - 34):i + 1]
        result_list.append(max(l))
    return pd.Series(result_list, index=index)

def get_all_available_options(ContextInfo, undl_code, option_type):
    """
    获取所有可用的期权合约（所有月份）

    参数:
    ContextInfo: 上下文信息
    undl_code: 标的代码
    option_type: 期权类型，'CALL'或'PUT'

    返回:
    list: 期权合约代码列表
    """
    try:
        # 解析标的代码
        marketcodeList = undl_code.split('.')
        if len(marketcodeList) != 2:
            print(f"错误的标的代码格式: {undl_code}")
            return []

        undlCode = marketcodeList[0]
        undlMarket = marketcodeList[1]

        # 确定市场
        market = ""
        if undlMarket == "SH":
            if undlCode in ["000016", "000300", "000852", "000905"]:
                market = 'IF'
            else:
                market = "SHO"
        elif undlMarket == "SZ":
            market = "SZO"

        # 获取期权列表
        optList = []
        try:
            if market == 'SHO':
                optList += get_stock_list_in_sector('上证期权')
            elif market == 'SZO':
                optList += get_stock_list_in_sector('深证期权')
            elif market == 'IF':
                optList += get_stock_list_in_sector('中金所')
        except Exception as e:
            print(f"获取期权列表出错: {e}")
            return []

        # 过滤期权
        result = []
        now = time.strftime("%Y%m%d")

        for opt in optList:
            try:
                if opt.find(market) < 0:
                    continue

                inst = ContextInfo.get_option_detail_data(opt)
                if 'optType' not in inst:
                    continue

                # 检查期权类型
                if option_type.upper() != inst["optType"]:
                    continue

                # 检查到期日
                endDate = inst['EndDelivDate']
                if str(endDate) <= now:
                    continue

                # 检查开始交易日期
                createDate = inst.get('OpenDate', 0)
                openDate = inst.get('OpenDate', 0)
                if createDate >= 1:
                    openDate = min(openDate, createDate)
                if openDate < 20150101 or str(openDate) > now:
                    continue

                # 检查标的代码匹配
                if inst['ProductID'].find(undlCode) > 0 or inst.get('OptUndlCode', '') == undlCode:
                    result.append(opt)

            except Exception as e:
                print(f"处理期权合约 {opt} 时出错: {e}")
                continue

        print(f"找到 {len(result)} 个 {option_type} 期权合约")
        return result

    except Exception as e:
        print(f"获取期权合约列表时出错: {e}")
        return []

def get_option_real_one(ContextInfo):
    call_one = put_one = None
    now = time.strftime("%Y%m%d")
    expiry_days_threshold = 10  # 与全局参数保持一致

    # 获取所有可用认购合约（所有月份）
    call_list = get_all_available_options(ContextInfo, g.undl_code, 'CALL')
    undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']

    # 按到期月份分组
    call_by_month = {}
    for call in call_list:
        detail = ContextInfo.get_option_detail_data(call)
        end_date = str(detail['EndDelivDate'])
        month = end_date[:6]
        if month not in call_by_month:
            call_by_month[month] = []
        call_by_month[month].append(call)

    months = sorted(call_by_month.keys())
    use_month = None
    for i, month in enumerate(months):
        detail = ContextInfo.get_option_detail_data(call_by_month[month][0])
        end_date = str(detail['EndDelivDate'])
        days_to_expiry = (datetime.strptime(end_date, '%Y%m%d') - datetime.now()).days
        if days_to_expiry > expiry_days_threshold or i == len(months) - 1:
            use_month = month
            break

    if use_month:
        call_candidates = call_by_month[use_month]
        call_dict = {call: ContextInfo.get_option_detail_data(call)['OptExercisePrice'] for call in call_candidates}
        real_list = [code for code in call_dict if call_dict[code] < undl_price]
        real_list = sorted(real_list, key=lambda code: call_dict[code], reverse=True)
        if real_list:
            call_one = real_list[0]

    # 认沽同理
    put_list = get_all_available_options(ContextInfo, g.undl_code, 'PUT')
    put_by_month = {}
    for put in put_list:
        detail = ContextInfo.get_option_detail_data(put)
        end_date = str(detail['EndDelivDate'])
        month = end_date[:6]
        if month not in put_by_month:
            put_by_month[month] = []
        put_by_month[month].append(put)

    months = sorted(put_by_month.keys())
    use_month = None
    for i, month in enumerate(months):
        detail = ContextInfo.get_option_detail_data(put_by_month[month][0])
        end_date = str(detail['EndDelivDate'])
        days_to_expiry = (datetime.strptime(end_date, '%Y%m%d') - datetime.now()).days
        if days_to_expiry > expiry_days_threshold or i == len(months) - 1:
            use_month = month
            break

    if use_month:
        put_candidates = put_by_month[use_month]
        put_dict = {put: ContextInfo.get_option_detail_data(put)['OptExercisePrice'] for put in put_candidates}
        real_list = [code for code in put_dict if put_dict[code] > undl_price]
        real_list = sorted(real_list, key=lambda code: put_dict[code], reverse=False)
        if real_list:
            put_one = real_list[0]

    return call_one, put_one

def get_current_month_option(ContextInfo, object, dedate, opttype=""):
    # dedate 日期 %Y%m%d 
    # 获取截止到ddate这天还未到行权日的当月期权合约
    isavailavle = True
    result = []
    opt_by_month = {}
    undlMarket = ""
    undlCode = ""
    marketcodeList = object.split('.')
    if len(marketcodeList) != 2:
        return []
    undlCode = marketcodeList[0]
    undlMarket = marketcodeList[1]
    market = ""
    if undlMarket == "SH":
        if undlCode in ["000016", "000300", "000852", "000905"]:
            market = 'IF'
        else:
            market = "SHO"
    elif undlMarket == "SZ":
        market = "SZO"
    if opttype.upper() == "C":
        opttype = "CALL"
    elif opttype.upper() == "P":
        opttype = "PUT"
    optList = []
    if market == 'SHO':
        optList += get_stock_list_in_sector('上证期权')
    elif market == 'SZO':
        optList += get_stock_list_in_sector('深证期权')
    elif market == 'IF':
        optList += get_stock_list_in_sector('中金所')
    for opt in optList:
        if opt.find(market) < 0:
            continue
        inst = ContextInfo.get_option_detail_data(opt)
        if 'optType' not in inst:
            continue
        endDate = inst['EndDelivDate']
        if isavailavle and str(endDate) <= dedate:
            continue
        if opttype.upper() != "" and opttype.upper() != inst["optType"]:
            continue
        if 1:  # option is trade, guosen demand
            createDate = inst['OpenDate']
            openDate = inst['OpenDate']
            if createDate >= 1:
                openDate = min(openDate, createDate)
            if openDate < 20150101 or str(openDate) > dedate:
                continue
        if inst['ProductID'].find(undlCode) > 0 or inst['OptUndlCode'] == undlCode:
            result.append(opt)
            month = str(endDate)[:6]
            if month not in opt_by_month:
                opt_by_month[month] = [opt]
            else:
                opt_by_month[month].append(opt)
    opt_list = sorted(opt_by_month, reverse=False)
    print(opt_by_month.keys())
    for opt in opt_by_month:
        if opt_list:
            b = datetime.strptime(opt_list[0], '%Y%m')
            a = datetime.now()
            if (b - a).days < 15:
                return opt_by_month[opt_list[0]]
            else:
                print(object, '未找到期权', opt_list[0], opt_by_month)
                return []
    else:
        return []

def calculate_lines(price_data):
    """计算红色横线值"""
    if price_data.empty:
        return 0, 0
        
    # 打印用于计算的价格数据
    print(f"用于计算红色横线的价格数据: {price_data['high']}")
    
    # 修改计算逻辑，使用移动平均或其他方法
    if len(price_data) >= 2:
        # 使用最近两根K线的最高价的平均值
        RED_LINE = price_data['high'].tail(2).mean()
    else:
        RED_LINE = price_data['high'].iloc[-1]
    
    print(f"计算过程: 使用最近{len(price_data)}根K线计算得到红色横线值: {RED_LINE:.4f}")
    
    return RED_LINE, None

