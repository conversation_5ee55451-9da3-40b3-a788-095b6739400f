#encoding:gbk
交易北交所='是' #否

# 策略关键参数设置
选股开始时间 = '093000'  # 格式为'HHMMSS'，改为9:30开盘后立即选股
选股检查间隔 = ['00', '10', '20', '30', '40', '50']  # 每10分钟检查一次
尾盘选股时间 = '145000'  # 尾盘额外选股时间点
使用趋势判断 = False  # 是否将趋势判断作为筛选条件，False=不使用，True=使用
检查均线距离 = False  # 是否将价格与均线距离作为筛选条件，False=不使用，True=使用
检查成交量放大 = False  # 是否检查成交量放大条件，False=不使用，True=使用
检查价格均线关系 = True  # 是否要求价格必须高于均线，False=不要求，True=要求
使用红线止损 = True  # 是否使用红线支撑止损，False=不使用，True=使用
使用动态红线止损 = True  # 是否使用动态红线止损阈值，False=使用固定阈值，True=使用动态阈值
使用均线止损 = True  # 是否使用均线支撑止损，False=不使用，True=使用
使用总体止损 = True  # 是否使用总体亏损止损，False=不使用，True=使用
使用个股止损 = True  # 是否使用个股亏损止损，False=不使用，True=使用
使用回撤止损 = True  # 是否使用最高点回撤止损，False=不使用，True=使用

import math
import time
import traceback
import numpy as np
import pandas as pd
import datetime
import os

# 账户设置 - 确保这些变量在全局范围内定义
account = "**********"  # 请替换为您的实际账户ID
accountType = "STOCK"  # 股票账户类型

# 获取股票价格的函数，定义在全局范围内
def get_price(C, s):
    try:
        full = C.get_full_tick([s])
        if s in full and 'lastPrice' in full[s]:
            return full[s]['lastPrice']*1.01
        else:
            print(f"警告: 无法获取{s}的实时价格")
            return 0
    except Exception as e:
        print(f"获取股票{s}价格时出错: {str(e)}")
        return 0

# 禁用DataFrame切片赋值警告
pd.options.mode.chained_assignment = None

class G():
    pass


g = G()
g.price = {}
g.select= False
g.sell = []
g.max_profit = {}
g.last_trading_day = ""  # 记录上一个交易日
g.bought_stocks = set()  # 记录当天已买入的股票
g.log_file = "trading_log.txt"  # 日志文件名
g.holding_days = {}  # 记录每只股票的持有天数
g.entry_prices = {}  # 记录每只股票的买入价格
g.stock_volatility = {}  # 记录每只股票的波动率
g.next_day_orders = []  # 存储次日需要执行的订单
g.signal_counts = {}  # 记录每只股票的连续买入信号次数
g.position_stage = {}  # 记录每只股票的建仓阶段 (1=第一批, 2=第二批, 3=第三批)
g.last_signal_time = {}  # 记录每只股票最近一次信号时间

# 添加配置类
class Config:
    # 红白线与价格距离阈值参数
    RED_LINE_DISTANCE_THRESHOLD = 0.05  # 做多时价格与红线最大偏离百分比，从0.55%调整为5%
    WHITE_LINE_DISTANCE_THRESHOLD = 0.0055  # 做空时价格与白线最大偏离百分比，默认0.55%
    
    # 均线参数(可调整)
    TREND_MA_PERIOD = 18  # 趋势判断均线周期，默认18日
    MA_DISTANCE_THRESHOLD = 0.005  # 价格与均线的最大偏离百分比，默认0.5%
    
    # 添加红白线转换强度参数
    PRICE_STRENGTH_THRESHOLD = 0.005  # 降低到0.5%，原来是0.8%
    VOLUME_STRENGTH_THRESHOLD = 1.2   # 成交量强度阈值（相对于前3根K线均值），从1.3降低到1.2
    CANDLE_BODY_RATIO = 0.5          # K线实体占比阈值，从0.6降低到0.5
    
    # 成交量参数
    VOLUME_THRESHOLD_LONG = 1.0  # 做多成交量阈值，从1.2调整为1.0
    
    # 止损参数
    RED_LINE_STOP_LOSS_THRESHOLD = 0.01  # 价格跌破红线的百分比阈值，默认1%
    MA_STOP_LOSS_THRESHOLD = 0.01  # 价格跌破均线的百分比阈值，默认1%
    TOTAL_STOP_LOSS_THRESHOLD = 0.07  # 总体亏损止损阈值，默认7%
    STOCK_STOP_LOSS_THRESHOLD = 0.08  # 个股亏损止损阈值，默认8%
    MAX_DRAWDOWN_THRESHOLD = 0.05  # 从最高点回撤止损阈值，默认5%
    
    # 新增时间止损参数
    MAX_HOLDING_DAYS = 12  # 最大持有天数，超过则考虑清仓
    MAX_HOLDING_DAYS_NO_PROFIT = 8  # 无盈利情况下的最大持有天数
    TIME_STOP_LOSS_FIRST_BATCH = 0.5  # 时间止损第一批卖出比例
    
    # 新增动态回撤止损参数
    VOLATILITY_LOOKBACK = 20  # 计算波动率的回看天数
    VOLATILITY_ADJUSTMENT_FACTOR = 1.5  # 波动率调整因子
    MIN_DRAWDOWN_THRESHOLD = 0.03  # 最小回撤止损阈值
    MAX_DRAWDOWN_THRESHOLD = 0.08  # 最大回撤止损阈值
    
    # 止盈参数
    FIRST_TAKE_PROFIT_THRESHOLD = 0.01  # 第一批止盈阈值，修改为3%
    FIRST_TAKE_PROFIT_RATIO = 0.33  # 第一批止盈比例，默认1/3
    SECOND_TAKE_PROFIT_THRESHOLD = 0.05  # 第二批止盈阈值，修改为5%
    SECOND_TAKE_PROFIT_RATIO = 0.5  # 第二批止盈比例，默认一半
    THIRD_TAKE_PROFIT_THRESHOLD = 0.10  # 第三批止盈阈值，修改为10%
    
    # 分批建仓参数
    FIRST_POSITION_RATIO = 0.3  # 第一批建仓比例，默认30%
    SECOND_POSITION_RATIO = 0.3  # 第二批建仓比例，默认30%
    THIRD_POSITION_RATIO = 0.4  # 第三批建仓比例，默认40%
    POSITION_UPGRADE_DAYS = 2  # 连续信号天数触发加仓，默认2天
    SIGNAL_EXPIRE_HOURS = 24  # 信号有效期(小时)，超过这个时间不计入连续信号
    
    # 动态红线止损参数
    DYNAMIC_RED_LINE_VOLATILITY_DAYS = 10  # 计算股票波动率的天数
    DYNAMIC_RED_LINE_VOLATILITY_DIVISOR = 2.0  # 波动率除数，影响波动率因子
    DYNAMIC_RED_LINE_VOLATILITY_MIN = 0.8  # 波动率因子最小值
    DYNAMIC_RED_LINE_VOLATILITY_MAX = 3.0  # 波动率因子最大值
    
    DYNAMIC_RED_LINE_TIME_BASE = 0.8  # 时间因子基础值
    DYNAMIC_RED_LINE_TIME_INCREMENT = 0.05  # 每天增加的时间因子
    DYNAMIC_RED_LINE_TIME_MAX = 1.5  # 时间因子最大值
    
    DYNAMIC_RED_LINE_MARKET_HIGH_VOLATILITY = 2.0  # 高波动市场阈值
    DYNAMIC_RED_LINE_MARKET_LOW_VOLATILITY = 1.0  # 低波动市场阈值
    DYNAMIC_RED_LINE_MARKET_DIVISOR = 1.5  # 市场波动率除数
    DYNAMIC_RED_LINE_MARKET_HIGH_FACTOR = 1.5  # 高波动市场最大因子
    DYNAMIC_RED_LINE_MARKET_LOW_FACTOR = 0.9  # 低波动市场因子
    
    DYNAMIC_RED_LINE_PROFIT_HIGH = 0.05  # 高盈利阈值(5%)
    DYNAMIC_RED_LINE_LOSS_HIGH = -0.03  # 高亏损阈值(-3%)
    DYNAMIC_RED_LINE_PROFIT_FACTOR = 1.2  # 高盈利时的因子
    DYNAMIC_RED_LINE_LOSS_FACTOR = 0.8  # 高亏损时的因子
    
    DYNAMIC_RED_LINE_MIN_THRESHOLD = 0.005  # 最小红线止损阈值(0.5%)
    DYNAMIC_RED_LINE_MAX_THRESHOLD = 0.03  # 最大红线止损阈值(3%)
    
    # 添加大盘股排除配置
    EXCLUDE_LARGE_CAP_STOCKS = True  # 是否排除大盘股
    LARGE_CAP_KEYWORDS = ["中国石化", "中石化", "工商银行", "建设银行", "中国银行", "农业银行", "中国石油", "中石油", "中国移动", "中国电信", "中国联通", "中国平安", "中国人寿", "中国太保", "中国神华"]  # 大盘股关键字列表
    MIN_DAILY_CHANGE = 0.02  # 新增：当天涨幅下限，默认3%
    # 新增ATR止损倍数参数
    ATR_STOP_MULTIPLIER = 1.5  # ATR止损倍数，默认1.5

# 定义日志和工具函数
def log_message(message):
    """记录日志消息到文件和控制台"""
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)
    
    try:
        with open(g.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except Exception as e:
        print(f"写入日志文件出错: {str(e)}")

def update_stock_signals(stock_code):
    """更新单个股票的信号状态"""
    global stock_signals
    
    try:
        # 获取股票数据
        price_data = g.price.get(stock_code)
        if price_data is None or len(price_data) < 30:  # 需要足够的数据计算均线和趋势
            return 0  # 返回0表示无信号
        
        # 从价格字典获取成交量数据
        volume_data = None
        if 'volume' in price_data:
            volume_data = price_data['volume']
        
        # 如果成交量数据不存在或为空，尝试从估算数据中获取
        if volume_data is None or len(volume_data) == 0:
            if stock_code in g.estimated_volume_data:
                # 使用之前估算的成交量数据
                estimated_volume = g.estimated_volume_data[stock_code]
                # 创建一个与价格数据等长的成交量数据数组
                volume_data = [estimated_volume] * len(price_data['close'])
                # 添加一些随机波动以模拟真实成交量变化
                import random
                for i in range(1, len(volume_data)):
                    volume_data[i] = int(volume_data[i-1] * (0.9 + 0.2 * random.random()))
            else:
                # 尝试估算成交量数据
                estimated_volume = handle_missing_volume_data(stock_code, ContextInfo)
                if estimated_volume is not None:
                    # 存储估算的成交量数据以备后用
                    g.estimated_volume_data[stock_code] = estimated_volume
                    # 创建一个与价格数据等长的成交量数据数组
                    volume_data = [estimated_volume] * len(price_data['close'])
                    # 添加一些随机波动以模拟真实成交量变化
                    import random
                    for i in range(1, len(volume_data)):
                        volume_data[i] = int(volume_data[i-1] * (0.9 + 0.2 * random.random()))
                else:
                    # 如果无法估算成交量，使用一个默认值
                    volume_data = [10000000] * len(price_data['close'])  # 默认1000万成交量
        
        # 计算红白线
        white_line, red_line = calculate_red_white_lines_exact(price_data)
        
        # 计算18日均线
        ma18 = MA(price_data['close'], Config.TREND_MA_PERIOD)
        
        # 检查趋势
        trend = check_trend(price_data, ma18[-1] if len(ma18) > 0 else None)
        
        # 判断当前是否有信号
        current_price = price_data['close'][-1]
        
        # 更新信号计数
        last_signal_time = g.last_signal_time.get(stock_code)
        current_time = time.time()
        
        # 检查是否满足买入信号条件
        is_buy_signal = False
        
        # 检查是否有红线存在
        has_red = red_line is not None and len(red_line) > 0 and not pd.isna(red_line.iloc[-1])
        
        # 检查是否为白线转红线 (简单判断，实际逻辑在do_select更复杂)
        prev_white_exists = white_line is not None and len(white_line) > 1 and not pd.isna(white_line.iloc[-2])
        red_exists = red_line is not None and len(red_line) > 0 and not pd.isna(red_line.iloc[-1])
        is_white_to_red = prev_white_exists and red_exists
        
        # 当满足基本条件时视为买入信号
        if has_red and is_white_to_red:
            is_buy_signal = True
        
        # 更新信号计数
        if is_buy_signal:
            # 如果上次信号时间存在且未过期，增加计数
            if last_signal_time is not None and (current_time - last_signal_time) / 3600 < Config.SIGNAL_EXPIRE_HOURS:
                g.signal_counts[stock_code] = g.signal_counts.get(stock_code, 0) + 1
            else:
                # 否则重置为1
                g.signal_counts[stock_code] = 1
            
            # 更新信号时间
            g.last_signal_time[stock_code] = current_time
        else:
            # 如果超过有效期，重置信号计数
            if last_signal_time is None or (current_time - last_signal_time) / 3600 >= Config.SIGNAL_EXPIRE_HOURS:
                g.signal_counts[stock_code] = 0
        
        # 获取当前信号计数
        signal_count = g.signal_counts.get(stock_code, 0)
        
        # 添加或更新信号记录
        signal_status = {
            'price': current_price,
            'red_line': red_line.iloc[-1] if red_line is not None and len(red_line) > 0 and not pd.isna(red_line.iloc[-1]) else None,
            'white_line': white_line.iloc[-1] if white_line is not None and len(white_line) > 0 and not pd.isna(white_line.iloc[-1]) else None,
            'prev_white_line': white_line.iloc[-2] if white_line is not None and len(white_line) > 1 and not pd.isna(white_line.iloc[-2]) else None,
            'ma18': ma18[-1] if len(ma18) > 0 else None,
            'trend': trend,
            'volume': volume_data[-1] if volume_data is not None and len(volume_data) > 0 else None,
            'volume_history': volume_data[-4:-1] if volume_data is not None and len(volume_data) > 3 else None,
            'updated_time': current_time,
            'signal_count': signal_count,
            'is_buy_signal': is_buy_signal
        }
        
        # 存储信号状态
        stock_signals[stock_code] = signal_status
        
        return signal_count
        
    except Exception as e:
        if g.show_volume_warnings:
            log_message(f"更新股票 {stock_code} 信号出错: {str(e)}")
        return 0

def print_bought_stocks():
    """打印当天已买入的股票列表"""
    if g.bought_stocks:
        print("\n====== 当天已买入的股票 ======")
        for stock in g.bought_stocks:
            print(f"  {stock}")
    else:
        print("当天尚未买入任何股票")

def is_stock_in_position(ContextInfo, stock_code):
    try:
        positions = get_trade_detail_data(account, accountType, 'POSITION')
        print(f"持仓数据获取结果: {positions}")  # 添加日志，查看返回的持仓数据
        
        if not positions:
            print("持仓数据为空")
            return False
            
        for p in positions:
            position_code = p.m_strInstrumentID + '.' + p.m_strExchangeID
            print(f"检查持仓: {position_code}, 总量: {p.m_nVolume}, 可用量: {p.m_nCanUseVolume}")
            
            # 修改判断条件，使用总持仓量判断而不是可用量
            if position_code == stock_code and p.m_nVolume > 0:  # 使用总量判断
                return True
        return False
    except Exception as e:
        print(f"检查持仓时出错: {str(e)}")
        return False

def init(ContextInfo):
    # 初始化日志
    log_message("策略启动初始化")
    
    # 添加标志位控制是否显示成交量数据缺失警告
    g.show_volume_warnings = False  # 设置为False可以关闭"无法获取成交量数据"的警告
    
    # 添加存储估算成交量数据的字典
    g.estimated_volume_data = {}
    
    # 改为获取沪深300成分股
    g.stock_list = get_stock_list_in_sector('沪深300') 
    print(f"初始股票池数量(沪深300): {len(g.stock_list)}")
    
    # 添加过滤条件
    # 1. 剔除ST、退市等 - 添加错误处理
    stock_name = {}
    valid_stocks = []
    
    for s in g.stock_list:
        try:
            detail = ContextInfo.get_instrumentdetail(s)
            if detail is None or 'InstrumentName' not in detail or detail['InstrumentName'] is None:
                print(f"无法获取{s}的股票名称信息")
                continue
                
            name = detail['InstrumentName']
            stock_name[s] = name
            
            # 检查是否ST或退市
            if 'st' in name.lower() or name.endswith('退'):
                print(f"过滤ST/退市股票: {s} {name}")
                continue
                
            valid_stocks.append(s)
        except Exception as e:
            print(f"处理{s}的股票信息时出错: {str(e)}")
            continue
    
    g.stock_list = valid_stocks
    print(f"过滤ST后股票池数量: {len(g.stock_list)}")
    
    # 2. 可以添加流动性过滤，但不再将无法获取成交量数据的股票排除
    # 获取近20日平均成交量，添加错误处理
    volume_data = {}
    stocks_with_volume = []
    
    # 统计成交量数据缺失的股票数量
    missing_volume_count = 0
    estimated_volume_count = 0
    missing_volume_stocks = []  # 记录缺失成交量的股票
    
    for s in g.stock_list:
        try:
            data = ContextInfo.get_market_data_ex_ori(['volume'], [s], period='1d', count=20)
            if 'volume' in data and len(data['volume']) > 0:
                volume_data[s] = data['volume'].mean()
                # 只有成功获取到成交量的股票才加入
                if volume_data[s] > 5000000:  # 500万成交量阈值
                    stocks_with_volume.append(s)
            else:
                # 尝试估算成交量数据
                estimated_volume = handle_missing_volume_data(s, ContextInfo)
                if estimated_volume is not None:
                    # 存储估算的成交量数据
                    g.estimated_volume_data[s] = estimated_volume
                    estimated_volume_count += 1
                    
                    # 将股票添加到具有成交量的股票列表
                    stocks_with_volume.append(s)
                
                # 记录缺失成交量的股票
                missing_volume_count += 1
                missing_volume_stocks.append(s)
                
                # 即使无法获取成交量数据，也将股票保留在池中
                if s not in stocks_with_volume:
                    stocks_with_volume.append(s)
                    
        except Exception as e:
            if g.show_volume_warnings:
                print(f"处理{s}的成交量数据时出错: {str(e)}")
            # 出错时也将股票保留在池中
            stocks_with_volume.append(s)
    
    # 显示汇总信息而不是每个股票的警告
    if missing_volume_count > 0:
        print(f"共有{missing_volume_count}只股票无法获取成交量数据，已成功估算{estimated_volume_count}只")
        # 如果需要，可以显示部分缺失成交量的股票
        if g.show_volume_warnings and len(missing_volume_stocks) > 0:
            display_count = min(5, len(missing_volume_stocks))
            print(f"部分缺失成交量的股票示例: {', '.join(missing_volume_stocks[:display_count])}...")
    
    # 过滤掉成交量过小的股票，但保留无法获取成交量数据的股票
    g.stock_list = stocks_with_volume
    
    print(f"最终股票池数量: {len(g.stock_list)}")
    
    # 初始化全局变量
    g.price = {}
    g.select = False  # 每次策略启动时重置选股标志
    g.sell = []
    g.max_profit = {}
    g.last_trading_day = ""  # 重置上一个交易日记录
    g.bought_stocks = set()  # 重置当天已买入的股票集合
    g.holding_days = {}  # 重置持有天数记录
    g.entry_prices = {}  # 重置买入价格记录
    g.stock_volatility = {}  # 重置股票波动率记录
    g.signal_counts = {}  # 重置信号计数
    g.position_stage = {}  # 重置建仓阶段
    g.last_signal_time = {}  # 重置信号时间
    
    ContextInfo.run_time("myHandlebar","3nSecond","2019-10-14 13:20:00")

def myHandlebar(ContextInfo):
    if not g.price:
        # 获取日线历史数据，策略基于日线周期运行
        try:
            # 只有当股票池不为空时才尝试获取价格数据
            if len(g.stock_list) > 0:
                g.price = ContextInfo.get_market_data_ex_ori(['close', 'open', 'high', 'low', 'volume'],g.stock_list,period='1d',fill_data=False, subscribe=False)
                print(f"成功获取{len(g.price)}只股票的价格数据")
            else:
                print("警告: 股票池为空，无法获取价格数据")
                g.price = {}  # 确保g.price被初始化为空字典
        except Exception as e:
            print(f"获取价格数据时出错: {str(e)}")
            g.price = {}  # 错误时也确保g.price被初始化
    
    now = time.strftime('%H%M%S')
    today = time.strftime('%Y%m%d')
    print(f"\n【{time.strftime('%Y-%m-%d %H:%M:%S')}】 策略运行")
    
    # 打印当天已买入的股票
    print_bought_stocks()
    
    # 同步持仓状态，确保系统状态与实际持仓一致
    current_positions = sync_position_status(ContextInfo)
    
    # 检查是否是新的交易日，如果是则重置选股标志
    if today != g.last_trading_day:
        print(f"检测到新交易日: {today}, 上一交易日: {g.last_trading_day}")
        log_message(f"检测到新交易日: {today}, 上一交易日: {g.last_trading_day}")
        g.select = False
        
        # 新的交易日，更新持有天数
        if g.last_trading_day != "":  # 确保不是第一次运行
            for stock in list(g.holding_days.keys()):
                g.holding_days[stock] += 1
                
        g.last_trading_day = today
        g.bought_stocks = set()  # 重置当天已买入股票的集合
    
    # 修改交易模式为实盘交易模式
    is_simulation = False  # 默认为实盘模式
    
    # 只在实盘模式下检查交易时间
    if not is_simulation and not '093000'<= now<='150000':
        print("实盘模式：非交易时间")
        return
    
    # 优化选股检查触发条件
    is_check_time = False
    
    # 1. 开盘时刻检查 - 刚开盘就检查一次
    if 选股开始时间 <= now <= '093100':
        is_check_time = True
    
    # 2. 定时检查 - 按照设定的间隔时间检查
    elif now[-2:] in 选股检查间隔:
        is_check_time = True
    
    # 3. 尾盘检查 - 收盘前特别检查
    elif 尾盘选股时间 <= now <= '145100':
        is_check_time = True
    
    # 执行选股检查
    if is_check_time:
        print(f"\n【{time.strftime('%Y-%m-%d %H:%M:%S')}】 ====== 检查股票池开仓条件 ======")
        # 检查g.price是否为空
        if not g.price:
            print("价格数据为空，无法进行选股")
            stocks = []
        else:
            stocks, _ = do_select(ContextInfo, True)
        
        if stocks:
            print(f"符合条件的股票: {len(stocks)}只 - {stocks}")
        else:
            print("没有符合条件的股票")
    
    # 卖出：
    # 三、止盈止损
    try:
        positions = get_trade_detail_data(account, accountType, 'POSITION')
        print(f"获取到{len(positions)}个持仓项")
        
        for p in positions:
            position_code = p.m_strInstrumentID + '.' + p.m_strExchangeID
            print(f"持仓详情: {position_code}, 总量: {p.m_nVolume}, 可用: {p.m_nCanUseVolume}")
            
            # 对于新持仓，初始化相关记录
            if position_code not in g.holding_days:
                g.holding_days[position_code] = 1
                
                # 获取买入价格
                try:
                    g.entry_prices[position_code] = p.m_dOpenPrice
                except:
                    # 如果无法获取开仓价格，使用当前价格
                    current_price = 0
                    try:
                        tick_data = ContextInfo.get_full_tick([position_code])
                        if position_code in tick_data:
                            current_price = tick_data[position_code]['lastPrice']
                    except:
                        pass
                    g.entry_prices[position_code] = current_price
                
                # 计算股票波动率
                try:
                    price_data = g.price[position_code]['close']
                    if len(price_data) >= Config.VOLATILITY_LOOKBACK:
                        daily_returns = np.diff(price_data[-Config.VOLATILITY_LOOKBACK:]) / price_data[-Config.VOLATILITY_LOOKBACK:-1]
                        g.stock_volatility[position_code] = np.std(daily_returns) * 100  # 转为百分比
                    else:
                        g.stock_volatility[position_code] = 2.0  # 默认波动率
                except:
                    g.stock_volatility[position_code] = 2.0  # 默认波动率
        
        # 清理已不再持有的股票记录
        current_positions = set(p.m_strInstrumentID + '.' + p.m_strExchangeID for p in positions)
        for stock in list(g.holding_days.keys()):
            if stock not in current_positions:
                g.holding_days.pop(stock, None)
                g.entry_prices.pop(stock, None)
                g.max_profit.pop(stock, None)
                g.stock_volatility.pop(stock, None)
        
        profit_ratio = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_dProfitRate for p in positions}
        
        # 修改：同时考虑总持仓量和可用量
        total_hold_vol = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_nVolume for p in positions if p.m_nVolume>0}
        hold_vol = {p.m_strInstrumentID+'.'+p.m_strExchangeID:p.m_nCanUseVolume for p in positions if p.m_nCanUseVolume>0}
        
        # 只有在有持仓的情况下才获取行情
        if total_hold_vol:  # 使用总持仓判断是否有持仓
            full_tick = ContextInfo.get_full_tick([s for s in total_hold_vol])
        else:
            full_tick = {}
    except Exception as e:
        print(f"获取持仓数据时出错: {str(e)}")
        positions = []
        profit_ratio = {}
        total_hold_vol = {}
        hold_vol = {}
        full_tick = {}
    
    # 打印持仓和盈亏情况
    if total_hold_vol:  # 修改：使用总持仓判断是否有持仓
        print("\n====== 当前持仓情况 ======")
        
        total_profit = 0
        for s in total_hold_vol:  # 修改：遍历总持仓
            try:
                stock_detail = ContextInfo.get_instrumentdetail(s)
                stock_name = stock_detail['InstrumentName'] if stock_detail and 'InstrumentName' in stock_detail else "未知"
                profit = profit_ratio[s] if s in profit_ratio else 0
                total_profit += profit
                current_price = full_tick[s]['lastPrice'] if s in full_tick else 0
                
                # 修改：显示总量和可用量信息
                avail_vol = hold_vol.get(s, 0)  # 获取可用量，如果没有则为0
                total_vol = total_hold_vol[s]
                days_held = g.holding_days.get(s, 0)
                
                print(f"  {s} {stock_name}: 总量={total_vol}股, 可用={avail_vol}股, 价={current_price:.2f}, 盈亏={profit*100:.2f}%, 持有天数={days_held}")
            except Exception as e:
                print(f"处理持仓{s}信息时出错: {str(e)}")
        
        print(f"总体盈亏: {total_profit*100:.2f}%")
        # === 新增：打印详细持仓明细，包括红线止损价、ATR止损价、首批止盈价 ===
        try:
            print_position_details(positions, g, ContextInfo)
        except Exception as e:
            print(f"打印持仓明细出错: {e}")
        
        # 总体止损机制
        if 使用总体止损 and total_profit < -Config.TOTAL_STOP_LOSS_THRESHOLD:
            print(f"\n====== 触发止损：总亏损>{Config.TOTAL_STOP_LOSS_THRESHOLD*100}% ======")
            log_message(f"触发总体止损：总亏损={total_profit*100:.2f}% > {Config.TOTAL_STOP_LOSS_THRESHOLD*100}%")
            for s in hold_vol:  # 这里仍使用hold_vol，因为只能卖出可用的
                if s not in g.sell:
                    # 修改为使用整手卖出函数
                    actual_sold = sell_stock_in_lots(ContextInfo, s, hold_vol[s], f"总体止损，总亏损={total_profit*100:.2f}%")
                    if actual_sold > 0:
                        g.sell.append(s)
                        g.select = False  # 重置选股标志，允许重新选股
        
        # 个股止盈止损
        print("\n====== 个股止盈止损检查 ======")
        for s in total_hold_vol:  # 修改：遍历总持仓进行止盈止损检查
            # 跳过没有可用量的股票（不能卖出）
            if s not in hold_vol or hold_vol[s] <= 0:
                print(f"  {s}: 无可用量，跳过实时止盈止损")
                
                # 检查是否需要添加到次日交易计划
                if 使用个股止损 and profit < -Config.STOCK_STOP_LOSS_THRESHOLD:
                    # 记录次日需要执行的止损订单
                    next_day_order = {
                        "type": "stop_loss",
                        "stock": s,
                        "reason": f"个股亏损={profit*100:.2f}%",
                        "volume": total_hold_vol[s]
                    }
                    if next_day_order not in g.next_day_orders:
                        g.next_day_orders.append(next_day_order)
                        log_message(f"添加次日止损计划: {s}, 原因: 个股亏损={profit*100:.2f}%")
                
                continue
            
            if s not in full_tick:
                continue
            
            # 分批止盈逻辑
            profit = profit_ratio[s] if s in profit_ratio else 0
            
            # 个股止损机制
            if 使用个股止损 and profit < -Config.STOCK_STOP_LOSS_THRESHOLD and s not in g.sell:
                # 修改为使用整手卖出函数
                actual_sold = sell_stock_in_lots(ContextInfo, s, hold_vol[s], f"个股止损，亏损={profit*100:.2f}% < -{Config.STOCK_STOP_LOSS_THRESHOLD*100}%")
                if actual_sold > 0:
                    g.sell.append(s)
                    g.select = False  # 重置选股标志，允许重新选股
            
            # 第一批止盈点
            if profit > Config.FIRST_TAKE_PROFIT_THRESHOLD and s not in g.sell and hold_vol[s] > 0:
                sell_vol = int(hold_vol[s] * Config.FIRST_TAKE_PROFIT_RATIO)
                # 修改为使用整手卖出函数，标记为分批卖出
                actual_sold = sell_stock_in_lots(ContextInfo, s, sell_vol, f"分批止盈(第一批)，盈利={profit*100:.2f}% > {Config.FIRST_TAKE_PROFIT_THRESHOLD*100}%", is_partial=True)
                # 不将股票加入g.sell列表，因为只是部分平仓
            
            # 第二批止盈点
            if profit > Config.SECOND_TAKE_PROFIT_THRESHOLD and s not in g.sell and hold_vol[s] > 0:
                sell_vol = int(hold_vol[s] * Config.SECOND_TAKE_PROFIT_RATIO)
                # 修改为使用整手卖出函数
                actual_sold = sell_stock_in_lots(ContextInfo, s, sell_vol, f"分批止盈(第二批)，盈利={profit*100:.2f}% > {Config.SECOND_TAKE_PROFIT_THRESHOLD*100}%")
                # 不将股票加入g.sell列表，因为只是部分平仓
            
            # 第三批止盈点
            if profit > Config.THIRD_TAKE_PROFIT_THRESHOLD and s not in g.sell and hold_vol[s] > 0:
                # 修改为使用整手卖出函数
                actual_sold = sell_stock_in_lots(ContextInfo, s, hold_vol[s], f"分批止盈(第三批)，盈利={profit*100:.2f}% > {Config.THIRD_TAKE_PROFIT_THRESHOLD*100}%")
                if actual_sold > 0:
                    g.sell.append(s)
                    g.select = False  # 重置选股标志，允许重新选股
            
            # 新增时间止损机制
            holding_days = g.holding_days.get(s, 0)
            
            # 1. 超过最大持有天数
            if holding_days >= Config.MAX_HOLDING_DAYS and s not in g.sell and hold_vol[s] > 0:
                # 修改为使用整手卖出函数
                actual_sold = sell_stock_in_lots(ContextInfo, s, hold_vol[s], f"时间止损，持有{holding_days}天超过最大持有期限{Config.MAX_HOLDING_DAYS}天")
                if actual_sold > 0:
                    g.sell.append(s)
                    g.select = False  # 重置选股标志，允许重新选股
            
            # 2. 无盈利情况下超过较短的持有天数
            elif holding_days >= Config.MAX_HOLDING_DAYS_NO_PROFIT and profit <= 0 and s not in g.sell and hold_vol[s] > 0:
                # 修改为使用整手卖出函数
                actual_sold = sell_stock_in_lots(ContextInfo, s, hold_vol[s], f"时间止损，持有{holding_days}天且无盈利")
                if actual_sold > 0:
                    g.sell.append(s)
                    g.select = False  # 重置选股标志，允许重新选股
            
            # 3. 较长时间无明显盈利，减仓
            elif holding_days >= Config.MAX_HOLDING_DAYS_NO_PROFIT and profit > 0 and profit < Config.FIRST_TAKE_PROFIT_THRESHOLD and s not in g.sell and hold_vol[s] > 0:
                sell_vol = int(hold_vol[s] * Config.TIME_STOP_LOSS_FIRST_BATCH)
                # 修改为使用整手卖出函数
                sell_stock_in_lots(ContextInfo, s, sell_vol, f"时间止损减仓，持有{holding_days}天且盈利不足{Config.FIRST_TAKE_PROFIT_THRESHOLD*100}%")
            
            # 从最高点回撤超过阈值止盈 - 动态调整回撤阈值
            if 使用回撤止损:
                # 获取该股票的波动率
                volatility = g.stock_volatility.get(s, 2.0)  # 默认波动率为2%
                
                # 根据波动率动态调整回撤阈值
                # 波动率越大，回撤阈值越大；波动率越小，回撤阈值越小
                adjusted_threshold = min(
                    Config.MAX_DRAWDOWN_THRESHOLD,
                    max(Config.MIN_DRAWDOWN_THRESHOLD, 
                        volatility / Config.VOLATILITY_ADJUSTMENT_FACTOR)
                )
                
                if s in g.max_profit:
                    if g.max_profit[s] - profit > adjusted_threshold:  # 回撤超过动态阈值
                        # 修改为使用整手卖出函数
                        actual_sold = sell_stock_in_lots(ContextInfo, s, hold_vol[s], f"最高点回撤止损，从{g.max_profit[s]*100:.2f}%回撤至{profit*100:.2f}%，超过阈值{adjusted_threshold*100:.2f}%")
                        if actual_sold > 0:
                            g.sell.append(s)
                            g.select = False  # 重置选股标志，允许重新选股
                else:
                    g.max_profit[s] = profit
                
                # 更新最高盈利点
                if s not in g.max_profit or profit > g.max_profit[s]:
                    g.max_profit[s] = profit

            # 更新最高盈利点
            if s not in g.max_profit or profit > g.max_profit[s]:
                g.max_profit[s] = profit
            
            # 跌破红色支撑线止损逻辑
            if 使用红线止损:
                try:
                    # 获取并计算红白线
                    stime = full_tick[s]['timetag'][:8]
                    price_data = pd.DataFrame()
                    
                    # 确保股票在g.price中有数据
                    if s in g.price:
                        price_data['close'] = g.price[s]['close']
                        price_data['high'] = g.price[s]['high']
                        price_data['low'] = g.price[s]['low']
                        price_data['open'] = g.price[s]['open']
                        
                        # 安全地获取成交量数据
                        if 'volume' in g.price[s]:
                            price_data['volume'] = g.price[s]['volume']
                        else:
                            # 如果没有成交量数据，创建一个虚拟的成交量列
                            dummy_volume = np.ones_like(g.price[s]['close']) * 10000000
                            price_data['volume'] = dummy_volume
                    
                        # 更新当日数据
                        c = full_tick[s]['lastPrice']
                        h = full_tick[s]['high']
                        l = full_tick[s]['low']
                        o = full_tick[s]['open']
                        
                        if stime in g.price[s]['stime']:
                            price_data['close'].iloc[-1] = c
                            price_data['high'].iloc[-1] = max(h, price_data['high'].iloc[-1])
                            price_data['low'].iloc[-1] = min(l, price_data['low'].iloc[-1])
                            if 'volume' in full_tick[s]:
                                price_data['volume'].iloc[-1] = full_tick[s]['volume']
                            price_data['open'].iloc[-1] = o
                        else:
                            # 使用更安全的concat方法添加新行
                            new_row_data = {
                                'close': [c],
                                'high': [h],
                                'low': [l],
                                'open': [o]
                            }
                            # 安全地添加成交量
                            if 'volume' in full_tick[s]:
                                new_row_data['volume'] = [full_tick[s]['volume']]
                            else:
                                new_row_data['volume'] = [10000000]  # 默认值
                            
                            new_row = pd.DataFrame(new_row_data)
                            price_data = pd.concat([price_data, new_row], ignore_index=True)
                        
                        # 计算红白线
                        white_line, red_line = calculate_red_white_lines_exact(price_data)
                        
                        # 获取最新的红线值
                        last_red = red_line.iloc[-1] if not red_line.empty and not pd.isna(red_line.iloc[-1]) else None
                        
                        # 获取持有天数
                        holding_days = g.holding_days.get(s, 1)
                        
                        # 根据是否使用动态红线止损选择阈值
                        if 使用动态红线止损:
                            # 使用动态计算的红线止损阈值
                            threshold = calculate_dynamic_red_line_threshold(s, holding_days, price_data)
                            threshold_desc = f"动态阈值={threshold*100:.2f}%"
                        else:
                            # 使用固定阈值
                            threshold = Config.RED_LINE_STOP_LOSS_THRESHOLD
                            threshold_desc = f"固定阈值={threshold*100:.2f}%"
                        
                        # === 新增ATR止损逻辑 ===
                        atr = calculate_atr(price_data)
                        if atr is not None:
                            print(f"ATR止损参考: {s} ATR={atr:.4f}")
                        else:
                            print(f"ATR止损参考: {s} ATR=无")
                        # 设定ATR止损倍数（可调）
                        ATR_STOP_MULTIPLIER = 1.5
                        if last_red is not None and atr is not None and c < last_red - Config.ATR_STOP_MULTIPLIER * atr and s not in g.sell:
                            actual_sold = sell_stock_in_lots(ContextInfo, s, hold_vol[s], f"ATR止损，价格{c:.2f}低于红线{last_red:.2f}超过{Config.ATR_STOP_MULTIPLIER}倍ATR={atr:.2f}")
                            if actual_sold > 0:
                                g.sell.append(s)
                                g.select = False
                        # === 原有红线止损逻辑 ===
                        elif last_red is not None and c < last_red * (1 - threshold) and s not in g.sell:
                            # 修改为使用整手卖出函数
                            actual_sold = sell_stock_in_lots(ContextInfo, s, hold_vol[s], f"红线止损，价格{c:.2f}跌破红线{last_red:.2f}超过{threshold*100:.2f}%")
                            if actual_sold > 0:
                                g.sell.append(s)
                                g.select = False  # 重置选股标志，允许重新选股
                        else:
                            print(f"红线检查 {s}：价格{c:.2f}未跌破红线{last_red:.2f}，{threshold_desc}，不执行止损")
                            log_message(f"红线检查 {s}：价格{c:.2f}未跌破红线{last_red:.2f}，{threshold_desc}，不执行止损")
                    else:
                        print(f"警告: 股票{s}在g.price中没有数据，无法进行红线支撑检查")
                
                except Exception as e:
                    print(f"计算红线支撑出错({s}): {str(e)}")
            
            # 均线支撑止损
            if 使用均线止损:
                try:
                    # 确保股票在g.price中有数据
                    if s in g.price and s in full_tick:
                        price_data = pd.DataFrame()
                        price_data['close'] = g.price[s]['close']
                        
                        # 获取当前价格
                        c = full_tick[s]['lastPrice']
                        
                        # 更新当日数据
                        if 'stime' in g.price[s] and full_tick[s]['timetag'][:8] in g.price[s]['stime']:
                            price_data['close'].iloc[-1] = c
                        else:
                            # 添加当前价格作为新行
                            new_row = pd.DataFrame({'close': [c]})
                            price_data = pd.concat([price_data, new_row], ignore_index=True)
                        
                        # 计算均线
                        ma_period = Config.TREND_MA_PERIOD
                        ma_value = MA(price_data['close'], ma_period)[-1] if len(price_data['close']) >= ma_period else None
                        ma18_value = MA(price_data['close'], 18)[-1] if len(price_data['close']) >= 18 else None
                        
                        # 判断是否跌破均线支撑
                        if ma_value is not None and c < ma_value * (1 - Config.MA_STOP_LOSS_THRESHOLD) and s not in g.sell:
                            # 修改为使用整手卖出函数
                            actual_sold = sell_stock_in_lots(ContextInfo, s, hold_vol[s], f"均线止损，价格{c:.2f}跌破MA{ma_period}={ma_value:.2f}超过{Config.MA_STOP_LOSS_THRESHOLD*100}%")
                            if actual_sold > 0:
                                g.sell.append(s)
                                g.select = False  # 重置选股标志，允许重新选股
                        else:
                            print(f"均线检查 {s}：价格{c:.2f}未跌破MA{ma_period}={ma_value:.2f}，不执行止损")
                            log_message(f"均线检查 {s}：价格{c:.2f}未跌破MA{ma_period}={ma_value:.2f}，不执行止损")
                    else:
                        if s not in g.price:
                            print(f"警告: 股票{s}在g.price中没有数据，无法进行均线支撑检查")
                        elif s not in full_tick:
                            print(f"警告: 股票{s}无法获取实时行情，无法进行均线支撑检查")
                
                except Exception as e:
                    print(f"计算均线支撑出错({s}): {str(e)}")
                
    else:
        print("当前无持仓")
    
    # 选股逻辑
    if now >= 选股开始时间 and not g.select:#开始选股
        print(f"\n【{time.strftime('%Y-%m-%d %H:%M:%S')}】 ====== 开始选股 ======")
        g.select=True
        
        t0 = time.time()
        # 检查g.price是否为空
        if not g.price:
            print("价格数据为空，无法进行选股")
            stocks = []
            ma18_dict = {}
        else:
            stocks,ma18_dict = do_select(ContextInfo, True)
            stocks = set(stocks)
        
        t1 = time.time()
        print(f"选股耗时: {t1-t0:.2f}秒")
        
        if not stocks:
            print("没有选出符合条件的股票")
            return
        
        print(f"选出符合条件的股票: {len(stocks)}只")
        for s in stocks:
            try:
                stock_name = ContextInfo.get_instrumentdetail(s)['InstrumentName']
                print(f"  {s} {stock_name}")
            except:
                print(f"  {s}")
        
        # 获取账户信息
        try:
            accounts = get_trade_detail_data(account, accountType, 'account')
            if not accounts:
                print("账号错误或账号掉线")
                return
                
            money = accounts[0].m_dBalance
            available= accounts[0].m_dAvailable
            print(f"账户总资金: {money:.2f}, 可用资金: {available:.2f}")
        except Exception as e:
            print(f"获取账户信息时出错: {str(e)}")
            return
        
        # 获取当前持仓信息，避免重复买入
        try:
            positions = get_trade_detail_data(account, accountType, 'POSITION')
            current_holdings = set()
            for p in positions:
                # 修改：使用总量判断而不是可用量
                if p.m_nVolume > 0:  # 改为判断总量
                    stock_code = p.m_strInstrumentID + '.' + p.m_strExchangeID
                    current_holdings.add(stock_code)
                    print(f"当前持有: {stock_code}, 总量: {p.m_nVolume}, 可用量: {p.m_nCanUseVolume}")
            
            # 过滤掉已持有的股票
            original_count = len(stocks)
            stocks = [s for s in stocks if s not in current_holdings]
            if original_count > len(stocks):
                print(f"已过滤掉{original_count - len(stocks)}只已持有的股票")
                
            if not stocks:
                print("过滤后没有需要买入的新股票")
                return
        except Exception as e:
            print(f"获取持仓信息时出错: {str(e)}, 将继续执行买入操作")
            log_message(f"获取持仓信息时出错: {str(e)}, 系统将继续执行买入操作")
            # 即使出错也继续执行，避免完全无法交易
        
        # 无论选出多少只股票，都按相同逻辑处理
        print("\n====== 执行买入操作 ======")
        # 资金少于10000元的特殊处理
        if available < 10000:
            # 现有的小资金逻辑保持不变
            print(f"可用资金不足10000元，执行单一股票全仓买入策略")
            # 如果有多只股票，选择价格低于40元的股票中最符合条件的一只
            suitable_stocks = []
            for s in stocks:
                try:
                    full_tick = ContextInfo.get_full_tick([s])
                    if s in full_tick:
                        current_price = full_tick[s]['lastPrice']
                        if current_price < 40:
                            suitable_stocks.append((s, current_price))
                except Exception as e:
                    print(f"检查{s}价格时出错: {str(e)}")
                    
            if suitable_stocks:
                # 按价格排序，选择价格最低的股票
                suitable_stocks.sort(key=lambda x: x[1])
                best_stock = suitable_stocks[0][0]
                print(f"从{len(stocks)}只股票中选择价格最低的一只进行全仓买入：{best_stock}, 价格={suitable_stocks[0][1]}")
                stocks = [best_stock]
            else:
                print("没有找到价格低于40元的符合条件股票")
                return
            
            # 使用全部可用资金买入
            for s in stocks:
                try:
                    # 检查股票是否为大盘股
                    if Config.EXCLUDE_LARGE_CAP_STOCKS and is_large_cap_stock(ContextInfo, s):
                        log_message(f"跳过 {s}: 属于大盘股，不符合买入条件")
                        print(f"跳过 {s}: 属于大盘股，不符合买入条件")
                        continue
                        
                    # 检查股票是否已经在当天买入过
                    if s in g.bought_stocks:
                        print(f"跳过 {s}: 今日已买入过该股票")
                        continue
                    
                    # 检查股票是否已经在持仓中
                    if is_stock_in_position(ContextInfo, s):
                        print(f"跳过 {s}: 已持有该股票")
                        continue
                        
                    full_tick = ContextInfo.get_full_tick([s])
                    if s not in full_tick:
                        print(f"跳过 {s}: 无法获取实时行情")
                        continue
                        
                    # 获取涨停价，检查是否涨停
                    stock_details = ContextInfo.get_instrumentdetail(s)
                    up_stop_price = stock_details.get('UpStopPrice', 0)
                    current_price = full_tick[s]['lastPrice']
                    
                    # 检查是否涨停
                    if up_stop_price > 0 and abs(current_price - up_stop_price) < 0.01:
                        print(f"跳过 {s}: 当前价格{current_price}处于涨停状态({up_stop_price})")
                        continue
                    
                    if current_price < 40:
                        order_money = available  # 全部可用资金
                        price = get_price(ContextInfo, s)
                        
                        # 检查资金是否足够购买至少一手
                        if not check_sufficient_funds(order_money, price):
                            log_message(f"跳过买入 {s}: 可用资金{order_money:.2f}不足购买一手(100股)，当前价格:{price:.2f}，需要资金:{price*100*1.01:.2f}")
                            print(f"跳过买入 {s}: 可用资金{order_money:.2f}不足购买一手(100股)，需要资金:{price*100*1.01:.2f}")
                            continue
                        
                        print(f"全仓买入 {s}: 金额={order_money:.2f}, 价格={price:.2f}")
                        passorder(23, 1102, account, s, 11, price, order_money, '', 2, '', ContextInfo)
                        log_message(f"全仓买入 {s}: 金额={order_money:.2f}, 价格={price:.2f}")
                        available = 0
                        # 记录已买入的股票
                        g.bought_stocks.add(s)
                        
                        # 新增：记录新买入持仓信息
                        estimated_quantity = int(order_money / price)
                        record_new_position(s, price, estimated_quantity)
                    else:
                        print(f"跳过 {s}: 股价过高(>40元)")
                except Exception as e:
                    print(f"处理买入{s}时出错: {str(e)}")
        else:
            # 新的大资金逻辑 - 实现分批建仓
            print(f"可用资金充足，执行精选股票分批建仓策略")
            
            # 如果选出的股票超过3只，进行二次筛选
            if len(stocks) > 3:
                print(f"选出{len(stocks)}只股票，进行二次筛选选择最优的3只")
                
                # 二次筛选逻辑 - 可以根据您的偏好选择筛选标准
                # 方案1: 按价格突破强度排序（从大到小）
                stock_scores = []
                for s in stocks:
                    try:
                        # 获取股票价格
                        full_tick = ContextInfo.get_full_tick([s])
                        if s not in full_tick:
                            continue
                            
                        current_price = full_tick[s]['lastPrice']
                        
                        # 检查价格是否低于40元
                        if current_price >= 40:
                            continue
                        
                        # 获取价格突破强度 (可以从ma18_dict中获取或重新计算)
                        if s in ma18_dict:
                            price_strength = ma18_dict[s]
                        else:
                            # 如果没有现成的数据，可以用当前价格与MA18的比值作为简单评分
                            price_data = pd.DataFrame()
                            price_data['close'] = g.price[s]['close']
                            ma18_value = MA(price_data['close'], 18)[-1] if len(price_data['close']) >= 18 else current_price
                            price_strength = (current_price / ma18_value - 1) if ma18_value > 0 else 0
                        
                        # 计算成交量放大比例
                        volume_ratio = 1.0
                        try:
                            if 'volume' in g.price[s]:
                                current_volume = g.price[s]['volume'][-1]
                                avg_volume = np.mean(g.price[s]['volume'][-4:-1]) if len(g.price[s]['volume']) >= 4 else current_volume
                                volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
                        except:
                            pass
                        
                        # 综合评分 - 可以根据需要调整各因素权重
                        score = price_strength * 0.6 + volume_ratio * 0.4
                        
                        # 连续信号加分 - 对于连续出现买入信号的股票提高评分
                        signal_count = g.signal_counts.get(s, 0)
                        if signal_count >= Config.POSITION_UPGRADE_DAYS:
                            score += (signal_count - 1) * 0.2  # 每多一次信号增加0.2分
                        
                        stock_scores.append((s, score, current_price))
                    except Exception as e:
                        print(f"评估{s}时出错: {str(e)}")
                
                # 按评分从高到低排序
                stock_scores.sort(key=lambda x: x[1], reverse=True)
                
                # 选择前3只股票
                top_stocks = [item[0] for item in stock_scores[:3]]
                
                print(f"二次筛选结果 - 选择评分最高的3只股票:")
                for i, (s, score, price) in enumerate(stock_scores[:3]):
                    try:
                        stock_name = ContextInfo.get_instrumentdetail(s)['InstrumentName']
                        signal_count = g.signal_counts.get(s, 0)
                        print(f"  {i+1}. {s} {stock_name}: 评分={score:.4f}, 价格={price:.2f}, 信号次数={signal_count}")
                    except:
                        print(f"  {i+1}. {s}: 评分={score:.4f}, 价格={price:.2f}")
                
                stocks = top_stocks
            
            # 买入选中的股票，实现分批建仓
            for s in stocks:
                try:
                    # 检查股票是否为大盘股
                    if Config.EXCLUDE_LARGE_CAP_STOCKS and is_large_cap_stock(ContextInfo, s):
                        log_message(f"跳过 {s}: 属于大盘股，不符合买入条件")
                        print(f"跳过 {s}: 属于大盘股，不符合买入条件")
                        continue
                        
                    # 检查股票是否已经在持仓中
                    is_in_position = is_stock_in_position(ContextInfo, s)
                    signal_count = g.signal_counts.get(s, 0)
                    
                    # 确定当前建仓阶段
                    current_stage = g.position_stage.get(s, 0)
                    
                    # 如果股票已在持仓中且信号次数达到要求，考虑加仓
                    if is_in_position:
                        if s in g.bought_stocks:
                            print(f"跳过 {s}: 今日已买入过该股票")
                            continue
                        
                        # 获取当前持仓信息
                        positions = get_trade_detail_data(account, accountType, 'POSITION')
                        position_size = 0
                        for p in positions:
                            if p.m_strInstrumentID + '.' + p.m_strExchangeID == s:
                                position_size = p.m_nVolume
                                break
                        
                        # 只有信号次数达到要求才考虑加仓
                        if signal_count < Config.POSITION_UPGRADE_DAYS:
                            print(f"跳过加仓 {s}: 信号次数({signal_count})未达到加仓要求({Config.POSITION_UPGRADE_DAYS})")
                            continue
                        
                        # 已经完成三阶段建仓，不再加仓
                        if current_stage >= 3:
                            print(f"跳过加仓 {s}: 已完成全部建仓阶段({current_stage})")
                            continue
                        
                        print(f"检测到持仓股票 {s} 需要加仓, 当前阶段: {current_stage}, 信号次数: {signal_count}")
                    
                    # 获取股票实时行情
                    full_tick = ContextInfo.get_full_tick([s])
                    if s not in full_tick:
                        print(f"跳过 {s}: 无法获取实时行情")
                        continue
                    
                    # 获取涨停价，检查是否涨停
                    stock_details = ContextInfo.get_instrumentdetail(s)
                    up_stop_price = stock_details.get('UpStopPrice', 0)
                    current_price = full_tick[s]['lastPrice']
                    
                    # 检查是否涨停
                    if up_stop_price > 0 and abs(current_price - up_stop_price) < 0.01:
                        print(f"跳过 {s}: 当前价格{current_price}处于涨停状态({up_stop_price})")
                        continue
                    
                    # 检查价格是否低于40元
                    if current_price >= 40:
                        print(f"跳过 {s}: 股价过高(>40元)")
                        continue
                    
                    # 确定买入比例和数量
                    # 股票总目标持仓为1000股，分三批建仓
                    total_target = 1000  # 最终目标持仓1000股
                    
                    if not is_in_position:
                        # 首次建仓
                        stage = 1
                        g.position_stage[s] = stage
                        buy_ratio = Config.FIRST_POSITION_RATIO
                        buy_quantity = int(total_target * buy_ratio)
                        stage_name = "第一批建仓"
                    else:
                        # 加仓
                        if current_stage == 1:
                            stage = 2
                            g.position_stage[s] = stage
                            buy_ratio = Config.SECOND_POSITION_RATIO
                            buy_quantity = int(total_target * buy_ratio)
                            stage_name = "第二批加仓"
                        elif current_stage == 2:
                            stage = 3
                            g.position_stage[s] = stage
                            buy_ratio = Config.THIRD_POSITION_RATIO
                            buy_quantity = int(total_target * buy_ratio)
                            stage_name = "第三批加仓"
                        else:
                            print(f"跳过 {s}: 已完成全部建仓")
                            continue
                    
                    # 确保买入数量至少为100股（A股最小交易单位）
                    buy_quantity = max(100, buy_quantity)
                    
                    # 计算所需资金
                    price = get_price(ContextInfo, s)
                    order_money = price * buy_quantity
                    
                    # 检查资金是否足够
                    if order_money > available:
                        print(f"跳过 {s}: 资金不足，需要{order_money:.2f}，可用{available:.2f}")
                        continue
                    
                    # 执行买入
                    print(f"{stage_name} {s}: 数量={buy_quantity}股, 价格={price:.2f}, 金额={order_money:.2f}, 当前阶段={stage}")
                    
                    # 检查资金是否足够购买至少一手
                    if buy_quantity < 100 or not check_sufficient_funds(available, price):
                        log_message(f"跳过买入 {s}: 购买数量{buy_quantity}股小于一手或资金不足，当前价格:{price:.2f}，需要资金:{price*100*1.01:.2f}，可用资金:{available:.2f}")
                        print(f"跳过买入 {s}: 购买数量{buy_quantity}股小于一手或资金不足，需要资金:{price*100*1.01:.2f}，可用资金:{available:.2f}")
                        continue
                    
                    passorder(23, 1101, account, s, 11, price, buy_quantity, '', 2, '', ContextInfo)
                    log_message(f"{stage_name} {s}: 数量={buy_quantity}股, 价格={price:.2f}, 金额={order_money:.2f}, 当前阶段={stage}, 信号次数={signal_count}")
                    
                    available -= order_money
                    # 记录已买入的股票
                    g.bought_stocks.add(s)
                    
                    # 记录新买入持仓信息
                    record_new_position(s, price, buy_quantity)
                    
                except Exception as e:
                    print(f"处理买入{s}时出错: {str(e)}")
                
        # 打印当天已买入的股票
        print_bought_stocks()
                
    # 买入条件：尾盘14:57分开始选股。所有符合红白线条件的股票，按股票账户总金额三分一买入，只排除股价大于40元的股票。
    
# 获取股票价格的函数已在文件前面定义，此处注释掉重复定义
# def get_price(C, s):
#     try:
#         full = C.get_full_tick([s])
#         if s in full and 'lastPrice' in full[s]:
#             return full[s]['lastPrice']*1.01
#         else:
#             print(f"警告: 无法获取{s}的实时价格")
#             return 0
#     except Exception as e:
#         print(f"获取股票{s}价格时出错: {str(e)}")
#         return 0

# 严格按照文华指标源代码重写红白线计算函数
def calculate_red_white_lines_exact(price_data):
    """
    严格按照文华指标源代码计算红白线
    
    参数:
    price_data: 包含open, high, low, close的DataFrame
    
    返回:
    white_line: 白线序列 (K2=1时的G值)
    red_line: 红线序列 (K2=-3时的G值)
    """
    try:
        # 确保数据足够
        if len(price_data) < 10:
            empty_series = pd.Series(np.nan, index=price_data.index)
            return empty_series, empty_series
        
        # 转换为numpy数组以提高计算效率
        high = price_data['high'].values
        low = price_data['low'].values
        open_price = price_data['open'].values
        close = price_data['close'].values
        
        length = len(price_data)
        
        # 初始化所有序列
        hx = np.zeros(length)  # HX:=HHV(HIGH,2) - 2日最高价
        lx = np.zeros(length)  # LX:=LLV(LOW,2) - 2日最低价
        h1 = np.zeros(length)  # 条件H1
        l1 = np.zeros(length)  # 条件L1
        h2 = np.zeros(length)  # 记录H1有效值
        l2 = np.zeros(length)  # 记录L1有效值
        k1 = np.zeros(length)  # 当前状态
        k2 = np.zeros(length)  # 最近有效状态
        g = np.zeros(length)   # 最终输出值
        
        # 步骤1: 计算HX和LX (最近2天的最高价和最低价)
        for i in range(length):
            if i == 0:
                hx[i] = high[i]
                lx[i] = low[i]
            else:
                hx[i] = max(high[i], high[i-1])
                lx[i] = min(low[i], low[i-1])
        
        # 步骤2: 计算H1和L1
        for i in range(5, length):
            # 计算历史开盘价的最高/最低值
            max_open_history = np.max(open_price[:i+1])
            min_open_history = np.min(open_price[:i+1])
            
            # H1条件: HX<REF(HX,1)&&HX<REF(HX,2)&&HX<REF(HX,4)&&LX<REF(LX,1)&&LX<REF(LX,3)&&LX<REF(LX,5)&&OPEN>CLOSE&&(HHV(OPEN,0)-CLOSE)>0
            if (hx[i] < hx[i-1] and 
                hx[i] < hx[i-2] and 
                hx[i] < hx[i-4] and 
                lx[i] < lx[i-1] and 
                lx[i] < lx[i-3] and 
                lx[i] < lx[i-5] and
                open_price[i] > close[i] and  # 阴线
                (max_open_history - close[i]) > 0):
                h1[i] = hx[i-4]
            
            # L1条件: LX>REF(LX,1)&&LX>REF(LX,3)&&LX>REF(LX,5)&&HX>REF(HX,1)&&HX>REF(HX,2)&&HX>REF(HX,4)&&OPEN<CLOSE&&(CLOSE-LLV(OPEN,0))>0
            if (lx[i] > lx[i-1] and 
                lx[i] > lx[i-3] and 
                lx[i] > lx[i-5] and
                hx[i] > hx[i-1] and 
                hx[i] > hx[i-2] and 
                hx[i] > hx[i-4] and
                open_price[i] < close[i] and  # 阳线
                (close[i] - min_open_history) > 0):
                l1[i] = lx[i-4]
        
        # 步骤3: 计算H2和L2 (VALUEWHEN函数)
        last_valid_h1 = 0
        last_valid_l1 = 0
        
        for i in range(length):
            # H2:=VALUEWHEN(H1>0,H1)
            if h1[i] > 0:
                last_valid_h1 = h1[i]
            h2[i] = last_valid_h1
            
            # L2:=VALUEWHEN(L1>0,L1)
            if l1[i] > 0:
                last_valid_l1 = l1[i]
            l2[i] = last_valid_l1
        
        # 步骤4: 计算K1 (当前状态)
        for i in range(length):
            # K1:=IFELSE(CLOSE>H2,-3,IFELSE(CLOSE<L2,1,0))
            if h2[i] > 0 and close[i] > h2[i]:
                k1[i] = -3  # 红线状态
            elif l2[i] > 0 and close[i] < l2[i]:
                k1[i] = 1   # 白线状态
            else:
                k1[i] = 0
        
        # 步骤5: 计算K2 (VALUEWHEN函数)
        last_valid_k1 = 0
        
        for i in range(length):
            # K2:=VALUEWHEN(K1<>0,K1)
            if k1[i] != 0:
                last_valid_k1 = k1[i]
            k2[i] = last_valid_k1
        
        # 步骤6: 计算G
        for i in range(length):
            # G:=IFELSE(K2=1,L2,H2) - K2=1时取L2，否则取H2
            if k2[i] == 1:  # 白线状态
                g[i] = l2[i]  # 注意: 当K2=1时，G=L2
            else:  # 红线状态或其他
                g[i] = h2[i]  # 当K2=-3或其他值时，G=H2
        
        # 转换为红白线序列
        white_line = pd.Series(np.nan, index=price_data.index)
        red_line = pd.Series(np.nan, index=price_data.index)
        
        # 填充红白线值
        for i in range(length):
            if k2[i] == 1:  # 白线状态
                white_line.iloc[i] = g[i]
            elif k2[i] == -3:  # 红线状态
                red_line.iloc[i] = g[i]
        
        return white_line, red_line
    
    except Exception as e:
        print(f"计算文华指标红白线出错: {e}")
        empty_series = pd.Series(np.nan, index=price_data.index)
        return empty_series, empty_series

# 检查成交量是否放大
def check_volume_increase(volume_data, threshold_base=1.2):
    """检查最近的成交量是否相比之前有明显增加"""
    try:
        # 确保volume_data是一个有效的Series或数组
        if volume_data is None or len(volume_data) < 4:
            log_message(f"成交量数据不足(长度={0 if volume_data is None else len(volume_data)})，无法比较，默认通过")
            print("成交量数据不足，无法比较")
            return True  # 默认通过，避免因缺少成交量数据而过滤掉股票
        
        # 检查数据中是否有空值(NaN)
        if isinstance(volume_data, pd.Series):
            if pd.isna(volume_data).any():
                log_message("成交量数据包含空值，默认通过该检查")
                print("成交量数据包含空值，无法准确比较")
                return True  # 默认通过
            
            # 确保数据都是正数
            if (volume_data <= 0).any():
                log_message("成交量数据包含零或负值，默认通过该检查")
                print("成交量数据包含零或负值，无法准确比较")
                return True  # 默认通过
            
            current_volume = volume_data.iloc[-1]
            avg_prev_volume = volume_data.iloc[-4:-1].mean()
        else:
            # 处理numpy数组或list类型
            if np.isnan(volume_data).any():
                log_message("成交量数据包含空值，默认通过该检查")
                print("成交量数据包含空值，无法准确比较")
                return True  # 默认通过
            
            # 确保数据都是正数
            if np.any(np.array(volume_data) <= 0):
                log_message("成交量数据包含零或负值，默认通过该检查")
                print("成交量数据包含零或负值，无法准确比较")
                return True  # 默认通过
            
            current_volume = volume_data[-1]
            avg_prev_volume = np.mean(volume_data[-4:-1])
        
        # 确保之前的成交量有效
        if avg_prev_volume <= 0:
            log_message("历史成交量均值为零或负值，默认通过该检查")
            return True  # 默认通过
        
        # 判断成交量是否显著放大
        volume_ratio = current_volume / avg_prev_volume
        
        if volume_ratio >= threshold_base:
            log_message(f"成交量放大检查通过，当前/均值={volume_ratio:.2f} >= {threshold_base}")
        
        return volume_ratio >= threshold_base
    except Exception as e:
        log_message(f"检查成交量增加出错: {e}，默认通过该检查")
        print(f"检查成交量增加出错: {e}")
        return True  # 出错时默认通过，避免过滤掉股票

# 检查趋势
def check_trend(price_data, ma_value, lookback=5):
    """判断价格趋势"""
    try:
        if len(price_data) < lookback:
            return 0  # 数据不足，无法判断趋势
        
        # 获取收盘价
        close = price_data['close']
        
        # 计算最近n个周期的收盘价相对均线的位置
        above_ma_count = sum(1 for p in close[-lookback:] if p > ma_value)
        below_ma_count = lookback - above_ma_count
        
        # 当大部分收盘价在均线上方时，认为是上升趋势
        if above_ma_count >= lookback * 0.6:
            return 1
        # 当大部分收盘价在均线下方时，认为是下降趋势
        elif below_ma_count >= lookback * 0.6:
            return -1
        else:
            return 0  # 震荡市
    except Exception as e:
        print(f"检查趋势出错: {e}")
        return 0

def do_select(ContextInfo, print_all_stocks=False):
    '''
    基于日线周期的红白线选股策略
    
    参数:
    ContextInfo - 上下文信息
    print_all_stocks - 是否打印所有股票的条件检查结果（默认False只打印符合条件的）
    
    返回:
    selected_stock - 选出的股票列表
    ma18_dict - 每只股票与MA18的距离字典
    '''
    selected_stock=[]
    ma18_dict = {}
    full_tick = ContextInfo.get_full_tick(g.stock_list)
    
    print("\n====== 开始日线红白线选股 ======")
    print(f"股票池中股票数量: {len(g.price)}")
    
    # 如果g.price为空，进行处理
    if not g.price:
        print("警告: 价格数据为空，无法进行选股")
        return [], {}
    
    # 记录每个条件的通过/失败数量
    condition_stats = {
        "检查总数": 0,
        "基本价格条件": 0,
        "红线存在": 0,
        "上升趋势": 0,
        "成交量放大": 0,
        "价格>均线": 0,
        "红线距离合适": 0,
        "均线距离合适": 0,
        "白线转红线": 0,
        "价格突破强度": 0,
        "综合转换强度": 0,
        "排除大盘股": 0,
        "最终选中": 0
    }
    
    # 如果是检查所有股票，使用表格形式展示
    if print_all_stocks:
        print(f"\n股票代码    价格    红线值    白线值    MA{Config.TREND_MA_PERIOD}    趋势    开仓信号")
        print("-----------------------------------------------------------")
    
    # 创建列表记录满足特定条件的股票
    stocks_passed_volume = []
    
    # 记录每个股票的条件状态，用于后续分析
    stock_conditions = {}

    for s in g.price:
        try:
            # 是否打印详细分析信息
            print_detail = False
            
            condition_stats["检查总数"] += 1
            
            # 排除大盘股
            if Config.EXCLUDE_LARGE_CAP_STOCKS and is_large_cap_stock(ContextInfo, s):
                print(f"排除大盘股: {s}") if print_all_stocks else None
                continue
            
            condition_stats["排除大盘股"] += 1
            
            # 基本检查
            if s not in full_tick:
                continue
                
            h, l, o, c = full_tick[s]['high'], full_tick[s]['low'], full_tick[s]['open'], full_tick[s]['lastPrice']
            if o <= 0:  # 去除停牌
                continue
            
            # 新增：当天涨幅判断
            if o > 0:
                daily_change = (c - o) / o
                if daily_change < Config.MIN_DAILY_CHANGE:
                    continue
            else:
                daily_change = 0
            
            # 获取并处理日线历史数据
            stime = full_tick[s]['timetag'][:8]
            price_data = pd.DataFrame()
            price_data['close'] = g.price[s]['close']
            price_data['high'] = g.price[s]['high']
            price_data['low'] = g.price[s]['low']
            price_data['open'] = g.price[s]['open']
            
            # 安全地获取成交量数据
            volume_data = None
            if 'volume' in g.price[s]:
                volume_data = pd.Series(g.price[s]['volume'])
            else:
                # 如果没有成交量数据，尝试从估算数据获取
                if s in g.estimated_volume_data:
                    estimated_volume = g.estimated_volume_data[s]
                    # 创建一个与价格数据等长的成交量数据Series
                    dummy_volume = np.ones_like(g.price[s]['close']) * estimated_volume
                    volume_data = pd.Series(dummy_volume)
                else:
                    # 尝试估算成交量
                    estimated_volume = handle_missing_volume_data(s, ContextInfo)
                    if estimated_volume is not None:
                        # 存储估算数据以备后用
                        g.estimated_volume_data[s] = estimated_volume
                        # 创建成交量数据
                        dummy_volume = np.ones_like(g.price[s]['close']) * estimated_volume
                        volume_data = pd.Series(dummy_volume)
                    else:
                        # 使用默认值
                        dummy_volume = np.ones_like(g.price[s]['close']) * 10000000  # 设置一个默认值
                        volume_data = pd.Series(dummy_volume)
                        
                if not g.show_volume_warnings:
                    pass  # 不显示警告
                else:
                    log_message(f"警告: {s}缺少成交量数据，使用估算值")
            
            # 更新当日数据 - 修复重复索引问题
            if stime in g.price[s]['stime']:
                price_data['close'].iloc[-1] = c
                price_data['high'].iloc[-1] = max(h, price_data['high'].iloc[-1])
                price_data['low'].iloc[-1] = min(l, price_data['low'].iloc[-1])
                # 安全地更新当日成交量
                if 'volume' in full_tick[s]:
                    if volume_data is not None:
                        volume_data.iloc[-1] = full_tick[s]['volume']
                price_data['open'].iloc[-1] = o
            else:
                # 使用更安全的concat方法添加新行
                new_row_data = {
                    'close': [c],
                    'high': [h],
                    'low': [l],
                    'open': [o]
                }
                
                new_row = pd.DataFrame(new_row_data)
                price_data = pd.concat([price_data, new_row], ignore_index=True)
                
                # 更新成交量数据
                if volume_data is not None and 'volume' in full_tick[s]:
                    new_vol = full_tick[s]['volume']
                    volume_data = pd.concat([volume_data, pd.Series([new_vol])], ignore_index=True)
                elif volume_data is not None:
                    # 如果实时行情中没有成交量，使用估算值
                    if s in g.estimated_volume_data:
                        new_vol = g.estimated_volume_data[s]
                    else:
                        new_vol = 10000000  # 默认值
                    volume_data = pd.concat([volume_data, pd.Series([new_vol])], ignore_index=True)
            
            # 基本价格条件
            price_ok = (3 <= c <= 100)
            if not price_ok:
                continue
            
            condition_stats["基本价格条件"] += 1
                
            DTPRICE = ContextInfo.get_instrumentdetail(s)['DownStopPrice']
            if c <= DTPRICE:
                continue
                
            # 计算红白线
            white_line, red_line = calculate_red_white_lines_exact(price_data)
            
            # 获取红白线状态 - 安全处理
            try:
                last_white = white_line.iloc[-1] if not pd.isna(white_line.iloc[-1]) else None
            except:
                last_white = None
                
            try:
                prev_white = white_line.iloc[-2] if len(white_line) > 1 and not pd.isna(white_line.iloc[-2]) else None
            except:
                prev_white = None
                
            try:
                last_red = red_line.iloc[-1] if not pd.isna(red_line.iloc[-1]) else None
            except:
                last_red = None
                
            try:
                prev_red = red_line.iloc[-2] if len(red_line) > 1 and not pd.isna(red_line.iloc[-2]) else None
            except:
                prev_red = None
            
            # 检查是否有红线(当前红线和上一周期的值)
            has_red_line = last_red is not None
            if not has_red_line:
                continue
            
            condition_stats["红线存在"] += 1
                
            # 计算均线值 - 使用可配置的均线参数
            ma_period = Config.TREND_MA_PERIOD  # 使用配置的均线周期
            MA_Line = MA(price_data['close'], ma_period)
            ma_value = MA_Line[-1] if len(MA_Line) > 0 else None
            ma18_value = MA(price_data['close'], 18)[-1] if len(price_data['close']) >= 18 else None
            
            # 检查趋势是否向上 - 使用新的均线参数
            current_trend = check_trend(price_data, ma_value)
            trend_ok = current_trend == 1
            
            # 如果启用了趋势判断且趋势不满足，则过滤掉该股票
            if 使用趋势判断 and not trend_ok:
                continue
            
            # 无论趋势如何，都累加统计数据
            condition_stats["上升趋势"] += 1
                
            # 检查成交量放大 - 添加安全处理
            volume_confirmed = True  # 默认为True
            if 检查成交量放大:  # 只有在需要检查成交量时才进行检查
                try:
                    volume_confirmed = check_volume_increase(volume_data, Config.VOLUME_THRESHOLD_LONG)
                except Exception as e:
                    print(f"检查{s}成交量时出错: {str(e)}")
                    # 如果检查成交量出错，默认通过
                    volume_confirmed = True
            
            # 添加专门记录满足成交量条件的股票
            if volume_confirmed:
                try:
                    stock_name = ContextInfo.get_instrumentdetail(s)['InstrumentName']
                    try:
                        current_volume = volume_data.iloc[-1] if volume_data is not None and len(volume_data) > 0 else 0
                        avg_prev_volume = volume_data.iloc[-4:-1].mean() if volume_data is not None and len(volume_data) > 3 else 0
                        volume_ratio = current_volume / avg_prev_volume if avg_prev_volume > 0 else 0
                        stocks_passed_volume.append(f"{s} {stock_name}: 当前量/均量={volume_ratio:.2f}")
                    except:
                        stocks_passed_volume.append(f"{s} {stock_name}: 成交量数据不完整")
                except Exception as e:
                    stocks_passed_volume.append(f"{s}: 成交量放大")
            
            if not volume_confirmed:
                continue
            
            condition_stats["成交量放大"] += 1
                
            # 检查价格是否在均线上方 - 使用新的均线参数
            price_above_ma = c > ma_value if ma_value is not None else False
            if 检查价格均线关系 and not price_above_ma:
                continue
            
            condition_stats["价格>均线"] += 1
                
            # 检查与红线距离
            red_line_distance_ok = True
            red_line_distance = 0
            if last_red is not None:
                red_line_distance = (c - last_red) / last_red
                red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD
            
            if not red_line_distance_ok:
                continue
            
            condition_stats["红线距离合适"] += 1
                
            # 检查与均线距离 - 使用新的均线参数
            ma_distance_ok = True
            ma_distance = 0
            if ma_value is not None:
                ma_distance = abs(c - ma_value) / ma_value
                ma_distance_ok = not 检查均线距离 or ma_distance <= Config.MA_DISTANCE_THRESHOLD
            
            if not ma_distance_ok:
                continue
            
            condition_stats["均线距离合适"] += 1
                
            # 检查是否为白线转红线状态(突破信号)
            transition_long = False
            if prev_white is not None and last_red is not None:
                # 标准的白线转红线：前一个K线有白线，当前K线有红线
                if prev_red is None or pd.isna(prev_red):
                    # 前一个K线没有红线，符合标准的白线转红线模式
                    transition_long = True
                    print(f"\n检测到标准白线转红线: 前白={prev_white:.4f}, 当红={last_red:.4f}")
                elif last_white is None or pd.isna(last_white):
                    # 前一个K线有红线但当前K线没有白线，也算作一种白线转红线
                    transition_long = True
                    print(f"\n检测到非标准白线转红线: 前白={prev_white:.4f}, 前红={prev_red:.4f}, 当红={last_red:.4f}")
                # 添加更宽松的条件：前一个K线和当前K线都有红线，且当前价格明显高于上一个红线
                elif prev_red is not None and not pd.isna(prev_red) and (last_red/prev_red - 1) > 0.01:
                    # 红线上升幅度大于1%，也算作一种趋势改善信号
                    transition_long = True
                    print(f"\n检测到红线上升改善信号: 前红={prev_red:.4f}, 当红={last_red:.4f}, 上升={((last_red/prev_red)-1)*100:.2f}%")
            
            # 记录股票的条件状态
            stock_conditions[s] = stock_conditions.get(s, {})
            stock_conditions[s]['白线转红线'] = transition_long

            if not transition_long:
                continue

            condition_stats["白线转红线"] += 1
            
            # 转换强度判断
            price_strength_ok = False
            price_strength = 0
            if transition_long and prev_white is not None:
                price_strength = (c / prev_white - 1)
                price_strength_ok = price_strength >= Config.PRICE_STRENGTH_THRESHOLD
            
            # 记录股票的价格突破强度情况
            stock_conditions[s]['价格突破强度'] = price_strength_ok
            stock_conditions[s]['价格突破百分比'] = price_strength * 100 if price_strength != 0 else 0
            
            if not price_strength_ok:
                continue
            
            condition_stats["价格突破强度"] += 1
                
            # 成交量强度判断 - 添加安全处理
            volume_ratio = 1.0  # 默认值
            try:
                if volume_data is not None and len(volume_data) > 3:
                    current_volume = volume_data.iloc[-1]
                    avg_volume = volume_data.iloc[-3:].mean()
                    volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
            except Exception as e:
                print(f"计算{s}成交量比例时出错: {str(e)}")
            
            volume_strength_ok = volume_ratio >= Config.VOLUME_STRENGTH_THRESHOLD
            
            # K线形态判断
            is_bullish = c > o
            body_ratio = (c - o) / (h - o) if h > o else 0
            candle_strength_ok = is_bullish and body_ratio > Config.CANDLE_BODY_RATIO
            
            # 综合转换强度 - 放宽条件，只要价格突破强度满足即可
            transition_strength_ok = price_strength_ok  # 移除volume_strength_ok和candle_strength_ok的要求
            
            # 记录股票的综合转换强度情况
            stock_conditions[s]['综合转换强度'] = transition_strength_ok
            
            if not transition_strength_ok:
                continue
            
            condition_stats["综合转换强度"] += 1
            
            # 最终做多条件
            can_long = (transition_long and 
                       price_above_ma and 
                       (not 使用趋势判断 or current_trend == 1) and  # 只有在启用趋势判断时才检查趋势
                       volume_confirmed and 
                       ma_distance_ok and  # 这里会自动符合条件如果检查均线距离=False
                       red_line_distance_ok and 
                       transition_strength_ok)
            
            # 对选中的股票进行详细分析输出
            if can_long:
                condition_stats["最终选中"] += 1
                selected_stock.append(s)
                ma_ratio = (c - ma18_value) / ma18_value if ma18_value is not None else 0
                ma18_dict[s] = ma_ratio
                
                # 更新股票信号计数和最近信号时间
                signal_count = update_stock_signals(s)
                print(f"\n股票 {s} 连续买入信号次数: {signal_count}")
                
                # 只对选中的股票输出详细信息
                print(f"\n====== 符合条件股票分析: {s} ======")
                print(f"当前价: {c}, 开盘价: {o}, 最高价: {h}, 最低价: {l}")
                print(f"当天涨幅: {daily_change*100:.2f}% (阈值: {Config.MIN_DAILY_CHANGE*100:.2f}%)")
                
                print("\n红白线状态:")
                if last_white is not None:
                    print(f"  当前白线: {last_white:.4f}")
                else:
                    print("  当前白线: 无")
                    
                if prev_white is not None:
                    print(f"  前一白线: {prev_white:.4f}")
                else:
                    print("  前一白线: 无")
                    
                if last_red is not None:
                    print(f"  当前红线: {last_red:.4f}")
                else:
                    print("  当前红线: 无")
                
                print(f"红白线转换检查: {'是白线转红线' if transition_long else '不是白线转红线'}")
                print(f"红线存在检查: {'存在' if has_red_line else '不存在'}")
                
                print("\n均线数据:")
                if ma_value is not None:
                    print(f"  MA{ma_period}: {ma_value:.4f}")
                else:
                    print(f"  MA{ma_period}: 无")
                    
                if ma18_value is not None:
                    print(f"  MA18: {ma18_value:.4f}")
                else:
                    print("  MA18: 无")
                
                print(f"趋势判断: {current_trend} (1=上升, -1=下降, 0=震荡), {'满足' if trend_ok else '不满足'}")
                print(f"是否使用趋势判断: {'是' if 使用趋势判断 else '否'}")
                
                # 安全地获取成交量数据
                try:
                    current_volume = volume_data.iloc[-1] if volume_data is not None and len(volume_data) > 0 else 0
                    avg_prev_volume = volume_data.iloc[-4:-1].mean() if volume_data is not None and len(volume_data) > 3 else 0
                    volume_ratio = current_volume / avg_prev_volume if avg_prev_volume > 0 else 0
                    print(f"成交量检查: 当前量={current_volume}, 前3日均量={avg_prev_volume}, 比例={volume_ratio:.2f}, {'满足' if volume_confirmed else '不满足'} (阈值={Config.VOLUME_THRESHOLD_LONG})")
                except:
                    print(f"成交量检查: 数据不完整，默认满足")
                print(f"是否检查成交量放大: {'是' if 检查成交量放大 else '否'}")
                
                ma_value_str = f"{ma_value:.4f}" if ma_value is not None else "无"
                print(f"价格与MA{ma_period}关系: 价格{c} {'>' if price_above_ma else '<='} 均线{ma_value_str}, {'满足' if price_above_ma else '不满足'}")
                
                if last_red is not None:
                    print(f"价格与红线距离: {red_line_distance*100:.2f}% {'(符合)' if red_line_distance_ok else '(过远)'}, 阈值={Config.RED_LINE_DISTANCE_THRESHOLD*100:.2f}%")
                
                if ma_value is not None:
                    print(f"价格与MA{ma_period}距离: {ma_distance*100:.2f}% {'(符合)' if ma_distance_ok else '(过远)'}, 阈值={Config.MA_DISTANCE_THRESHOLD*100:.2f}%")
                    print(f"是否检查均线距离: {'是' if 检查均线距离 else '否'}")
                
                print(f"白线转红线状态: {'是' if transition_long else '否'}")
                
                if transition_long and prev_white is not None:
                    print(f"价格突破强度: {price_strength*100:.4f}% {'(符合)' if price_strength_ok else '(不足)'}, 阈值={Config.PRICE_STRENGTH_THRESHOLD*100:.2f}%")
                
                print(f"成交量强度: 当前/均值={volume_ratio:.2f} {'(符合)' if volume_strength_ok else '(不足)'}, 阈值={Config.VOLUME_STRENGTH_THRESHOLD}")
                
                print(f"K线形态: {'阳线' if is_bullish else '阴线'}, 实体比例={body_ratio:.2f} {'(符合)' if candle_strength_ok else '(不符)'}, 阈值={Config.CANDLE_BODY_RATIO}")
                
                print(f"转换强度综合判断: {'通过' if transition_strength_ok else '不通过'}")
                print(f"最终选股结果: {'选中' if can_long else '不选'}")
            
            # 如果要打印所有股票的情况，使用简化格式
            if print_all_stocks:
                # 获取股票名称
                try:
                    stock_name = ContextInfo.get_instrumentdetail(s)['InstrumentName']
                    name_short = stock_name[:4]  # 截取前几个字避免太长
                except:
                    name_short = "未知"
                    
                trend_str = "上升" if trend_ok else "非上"
                result = "√" if can_long else "×"
                
                # 创建安全的字符串表示
                red_line_str = f"{last_red:.2f}" if last_red is not None else "无"
                white_line_str = f"{last_white:.2f}" if last_white is not None else "无"
                ma_str = f"{ma_value:.2f}" if ma_value is not None else "无"
                
                # 简洁打印红白线和关键信息
                print(f"{s[-8:]} {name_short} {c:.2f}  {red_line_str}  {white_line_str}  {ma_str}  {trend_str}  {result}")
                
        except Exception as e:
            print(f"分析股票{s}出错: {str(e)}")
            continue
    
    # 打印统计数据
    print("\n====== 条件筛选统计 ======")
    print(f"检查总数: {condition_stats['检查总数']}")
    print(f"通过基本价格条件: {condition_stats['基本价格条件']} ({condition_stats['基本价格条件']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"存在红线: {condition_stats['红线存在']} ({condition_stats['红线存在']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"上升趋势: {condition_stats['上升趋势']} ({condition_stats['上升趋势']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"成交量放大: {condition_stats['成交量放大']} ({condition_stats['成交量放大']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"价格>均线: {condition_stats['价格>均线']} ({condition_stats['价格>均线']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"红线距离合适: {condition_stats['红线距离合适']} ({condition_stats['红线距离合适']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"均线距离合适: {condition_stats['均线距离合适']} ({condition_stats['均线距离合适']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"白线转红线: {condition_stats['白线转红线']} ({condition_stats['白线转红线']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"价格突破强度: {condition_stats['价格突破强度']} ({condition_stats['价格突破强度']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"综合转换强度: {condition_stats['综合转换强度']} ({condition_stats['综合转换强度']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"排除大盘股: {condition_stats['排除大盘股']} ({condition_stats['排除大盘股']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    print(f"最终选中: {condition_stats['最终选中']} ({condition_stats['最终选中']/max(1,condition_stats['检查总数'])*100:.1f}%)")
    
    # 当没有选出股票时，输出接近条件的股票信息，便于分析
    if not selected_stock and len(stock_conditions) > 0:
        print("\n====== 接近条件的股票分析 ======")
        for s, conditions in stock_conditions.items():
            if '白线转红线' in conditions and conditions['白线转红线']:
                try:
                    stock_name = ContextInfo.get_instrumentdetail(s)['InstrumentName']
                    price = full_tick[s]['lastPrice'] if s in full_tick else "未知"
                    price_strength = conditions.get('价格突破百分比', 0)
                    print(f"{s} {stock_name}: 价格={price}, 满足白线转红线, 价格突破强度={price_strength:.2f}% (阈值={Config.PRICE_STRENGTH_THRESHOLD*100:.2f}%)")
                except:
                    print(f"{s}: 满足白线转红线，但价格突破强度不足")
    
    return selected_stock, ma18_dict

def RD(N,D=3):   return np.round(N,D)        #四舍五入取3位小数 
def RET(S,N=1):  return np.array(S)[-N]      #返回序列倒数第N个值,默认返回最后一个
def ABS(S):      return np.abs(S)            #返回N的绝对值
def MAX(S1,S2):  return np.maximum(S1,S2)    #序列max
def MIN(S1,S2):  return np.minimum(S1,S2)    #序列min
def IF(S,A,B):   return np.where(S,A,B)      #序列布尔判断 return=A  if S==True  else  B

def REF(S, N=1):          #对序列整体下移动N,返回序列(shift后会产生NAN)    
    return pd.Series(S).shift(N).values  

def DIFF(S, N=1):         #前一个值减后一个值,前面会产生nan 
    return pd.Series(S).diff(N).values     #np.diff(S)直接删除nan，会少一行

def STD(S,N):             #求序列的N日标准差，返回序列    
    return  pd.Series(S).rolling(N).std(ddof=0).values     

def SUM(S, N):            #对序列求N天累计和，返回序列    N=0对序列所有依次求和         
    return pd.Series(S).rolling(N).sum().values if N>0 else pd.Series(S).cumsum().values  

def CONST(S):             #返回序列S最后的值组成常量序列
    return np.full(len(S),S[-1])
  
def HHV(S,N):             #HHV(C, 5) 最近5天收盘最高价        
    return pd.Series(S).rolling(N).max().values     

def LLV(S,N):             #LLV(C, 5) 最近5天收盘最低价     
    return pd.Series(S).rolling(N).min().values    
    
def HHVBARS(S,N):         #求N周期内S最高值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]),raw=True).values 

def LLVBARS(S,N):         #求N周期内S最低值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]),raw=True).values    
  
def MA(S,N):              #求序列的N日简单移动平均值，返回序列                    
    return pd.Series(S).rolling(N).mean().values  
  
def EMA(S,N):             #指数移动平均,为了精度 S>4*N  EMA至少需要120周期     alpha=2/(span+1)    
    return pd.Series(S).ewm(span=N, adjust=False).mean().values     

def SMA(S, N, M=1):       #中国式的SMA,至少需要120周期才精确 (雪球180周期)    alpha=1/(1+com)    
    return pd.Series(S).ewm(alpha=M/N,adjust=False).mean().values           #com=N-M/M

def DMA(S, A):            #求S的动态移动平均，A作平滑因子,必须 0<A<1  (此为核心函数，非指标）
    return pd.Series(S).ewm(alpha=A, adjust=False).mean().values

def WMA(S, N):            #通达信S序列的N日加权移动平均 Yn = (1*X1+2*X2+3*X3+...+n*Xn)/(1+2+3+...+Xn)
    return pd.Series(S).rolling(N).apply(lambda x:x[::-1].cumsum().sum()*2/N/(N+1),raw=True).values 
  
def AVEDEV(S, N):         #平均绝对偏差  (序列与其平均值的绝对差的平均值)   
    return pd.Series(S).rolling(N).apply(lambda x: (np.abs(x - x.mean())).mean()).values 

def SLOPE(S, N):          #返S序列N周期回线性回归斜率            
    return pd.Series(S).rolling(N).apply(lambda x: np.polyfit(range(N),x,deg=1)[0],raw=True).values

def FORCAST(S, N):        #返回S序列N周期回线性回归后的预测值， jqz1226改进成序列出    
    return pd.Series(S).rolling(N).apply(lambda x:np.polyval(np.polyfit(range(N),x,deg=1),N-1),raw=True).values  

def LAST(S, A, B):        #从前A日到前B日一直满足S_BOOL条件, 要求A>B & A>0 & B>=0 
    return np.array(pd.Series(S).rolling(A+1).apply(lambda x:np.all(x[::-1][B:]),raw=True),dtype=bool)
  
#------------------   1级：应用层函数(通过0级核心函数实现） ----------------------------------
def COUNT(S, N):                       # COUNT(CLOSE>O, N):  最近N天满足S_BOO的天数  True的天数
    return SUM(S,N)    

def EVERY(S, N):                       # EVERY(CLOSE>O, 5)   最近N天是否都是True
    return  IF(SUM(S,N)==N,True,False)                    
  
def EXIST(S, N):                       # EXIST(CLOSE>3010, N=5)  n日内是否存在一天大于3000点  
    return IF(SUM(S,N)>0,True,False)

def FILTER(S, N):                      # FILTER函数，S满足条件后，将其后N周期内的数据置为0, FILTER(C==H,5)
    for i in range(len(S)): S[i+1:i+1+N]=0  if S[i] else S[i+1:i+1+N]        
    return S                           # 例：FILTER(C==H,5) 涨停后，后5天不再发出信号 
  
def BARSLAST(S):                       #上一次条件成立到当前的周期, BARSLAST(C/REF(C,1)>=1.1) 上一次涨停到今天的天数 
    M=np.concatenate(([0],np.where(S,1,0)))  
    for i in range(1, len(M)):  M[i]=0 if M[i] else M[i-1]+1    
    return M[1:]                       

def BARSLASTCOUNT(S):                  # 统计连续满足S条件的周期数        by jqz1226
    rt = np.zeros(len(S)+1)            # BARSLASTCOUNT(CLOSE>OPEN)表示统计连续收阳的周期数
    for i in range(len(S)): rt[i+1]=rt[i]+1  if S[i] else rt[i+1]
    return rt[1:]  
  
def BARSSINCEN(S, N):                  # N周期内第一次S条件成立到现在的周期数,N为常量  by jqz1226
    return pd.Series(S).rolling(N).apply(lambda x:N-1-np.argmax(x) if np.argmax(x) or x[0] else 0,raw=True).fillna(0).values.astype(int)

  
def CROSS(S1, S2):                     #判断向上金叉穿越 CROSS(MA(C,5),MA(C,10))  判断向下死叉穿越 CROSS(MA(C,10),MA(C,5))   
    return np.concatenate(([False], np.logical_not((S1>S2)[:-1]) & (S1>S2)[1:]))    # 不使用0级函数,移植方便  by jqz1226
    
def LONGCROSS(S1,S2,N):                #两条线维持一定周期后交叉,S1在N周期内都小于S2,本周期从S1下方向上穿过S2时返回1,否则返回0         
    return  np.array(np.logical_and(LAST(S1<S2,N,1),(S1>S2)),dtype=bool)            # N=1时等同于CROSS(S1, S2)
    
def VALUEWHEN(S, X):                   #当S条件成立时,取X的当前值,否则取VALUEWHEN的上个成立时的X值   by jqz1226
    return pd.Series(np.where(S,X,np.nan)).ffill().values  

def log_message(message):
    """记录日志消息到文件和控制台"""
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)
    
    try:
        with open(g.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except Exception as e:
        print(f"写入日志文件出错: {str(e)}")

# 买入后记录到持仓日志
def record_new_position(stock_code, price, quantity):
    """记录新买入的持仓信息"""
    g.holding_days[stock_code] = 1
    g.entry_prices[stock_code] = price
    g.bought_stocks.add(stock_code)
    
    # 如果是首次建仓，初始化建仓阶段为1
    if stock_code not in g.position_stage:
        g.position_stage[stock_code] = 1
    
    # 计算股票波动率
    try:
        if stock_code in g.price:
            price_data = g.price[stock_code]['close']
            if len(price_data) >= Config.VOLATILITY_LOOKBACK:
                daily_returns = np.diff(price_data[-Config.VOLATILITY_LOOKBACK:]) / price_data[-Config.VOLATILITY_LOOKBACK:-1]
                g.stock_volatility[stock_code] = np.std(daily_returns) * 100  # 转为百分比
            else:
                g.stock_volatility[stock_code] = 2.0  # 默认波动率
    except:
        g.stock_volatility[stock_code] = 2.0  # 默认波动率
    
    stage = g.position_stage.get(stock_code, 1)
    signal_count = g.signal_counts.get(stock_code, 1)
    log_message(f"新增持仓: {stock_code}, 价格={price}, 数量={quantity}, 初始波动率={g.stock_volatility.get(stock_code, 2.0):.2f}%, 建仓阶段={stage}, 信号次数={signal_count}")

def sync_position_status(ContextInfo):
    """同步持仓状态，确保系统状态与实际持仓一致"""
    try:
        positions = get_trade_detail_data(account, accountType, 'POSITION')
        current_positions = set()
        
        # 获取实际持仓情况
        for p in positions:
            position_code = p.m_strInstrumentID + '.' + p.m_strExchangeID
            if p.m_nVolume > 0:  # 使用总量判断
                current_positions.add(position_code)
                
                # 更新持仓相关信息
                if position_code not in g.holding_days:
                    g.holding_days[position_code] = 1
                    g.entry_prices[position_code] = p.m_dOpenPrice
                    
                    # 确保建仓阶段信息存在
                    if position_code not in g.position_stage:
                        g.position_stage[position_code] = 1
                    # 计算波动率等信息...
        
        # 清理不再持有的股票记录
        for stock in list(g.holding_days.keys()):
            if stock not in current_positions:
                log_message(f"同步状态：{stock}已不在持仓中，清理相关记录")
                g.holding_days.pop(stock, None)
                g.entry_prices.pop(stock, None)
                g.max_profit.pop(stock, None)
                g.stock_volatility.pop(stock, None)
                g.position_stage.pop(stock, None)  # 清理建仓阶段信息
                g.signal_counts.pop(stock, None)   # 清理信号计数
                g.last_signal_time.pop(stock, None)  # 清理信号时间
                
        return current_positions
    except Exception as e:
        log_message(f"同步持仓状态出错: {str(e)}")
        return set()

def log_trade_execution(order_type, stock_code, price, volume, reason=""):
    """记录交易执行详情"""
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
    trade_info = {
        "time": timestamp,
        "type": order_type,  # 买入/卖出
        "stock": stock_code,
        "price": price,
        "volume": volume,
        "reason": reason,
        "total_value": price * volume
    }
    
    # 记录到日志文件
    log_entry = f"[{timestamp}][交易] {order_type} {stock_code}, 价格:{price:.2f}, 数量:{volume}, 金额:{price*volume:.2f}, 原因:{reason}"
    log_message(log_entry)
    
    # 可以选择将交易记录保存到额外的CSV文件
    try:
        import csv
        import os
        
        trade_log_file = "trade_records.csv"
        file_exists = os.path.isfile(trade_log_file)
        
        with open(trade_log_file, 'a', newline='') as f:
            writer = csv.DictWriter(f, fieldnames=trade_info.keys())
            if not file_exists:
                writer.writeheader()
            writer.writerow(trade_info)
    except Exception as e:
        print(f"保存交易记录出错: {str(e)}")

def calculate_adaptive_volatility(price_data, min_days=10, max_days=30):
    """计算自适应波动率，短期数据不足时使用更少的日期"""
    if len(price_data) < min_days:
        return 2.0  # 默认波动率
    
    # 确定使用的天数 - 数据越多使用越长的窗口
    lookback = min(max_days, max(min_days, len(price_data) // 2))
    
    # 计算波动率
    returns = np.diff(price_data[-lookback:]) / price_data[-lookback:-1]
    volatility = np.std(returns) * 100  # 转为百分比
    
    # 波动率有效性检查
    if np.isnan(volatility) or volatility < 0.5:
        volatility = 2.0  # 设置合理的默认值
    elif volatility > 10:
        volatility = 10.0  # 设置合理的上限
        
    return volatility

def adjust_trading_parameters(ContextInfo):
    """根据市场状况动态调整交易参数"""
    try:
        # 获取大盘指数
        index_data = ContextInfo.get_market_data_ex_ori(['close'], ['000300.SH'], period='1d', count=20)
        
        if 'close' in index_data['000300.SH']:
            index_close = index_data['000300.SH']['close']
            
            # 计算大盘短期波动率
            index_returns = np.diff(index_close) / index_close[:-1]
            market_volatility = np.std(index_returns) * 100
            
            # 根据市场波动调整参数
            if market_volatility > 2.5:  # 高波动市场
                # 提高止损阈值，降低持仓时间
                Config.STOCK_STOP_LOSS_THRESHOLD = 0.10  # 提高到10%
                Config.MAX_HOLDING_DAYS = 8  # 缩短最大持有期
                log_message(f"检测到高波动市场(波动率={market_volatility:.2f}%)，调整止损阈值为10%，最大持有期为8天")
            else:  # 低波动市场
                # 恢复默认参数
                Config.STOCK_STOP_LOSS_THRESHOLD = 0.08  # 恢复到8%
                Config.MAX_HOLDING_DAYS = 12  # 恢复到12天
                log_message(f"检测到常规市场波动(波动率={market_volatility:.2f}%)，使用默认参数")
    except Exception as e:
        log_message(f"调整交易参数出错: {str(e)}")

def handle_missing_volume_data(stock_code, ContextInfo):
    """
    处理无法获取成交量数据的情况
    当无法获取成交量数据时，使用默认值或历史数据估算
    
    Args:
        stock_code: 股票代码
        ContextInfo: 上下文信息
    
    Returns:
        估算的成交量数据，如果无法估算则返回None
    """
    try:
        # 检查是否已经有估算数据
        if hasattr(g, 'estimated_volume_data') and stock_code in g.estimated_volume_data:
            return g.estimated_volume_data[stock_code]
            
        # 尝试获取该股票的历史价格数据
        current_price = 0
        try:
            # 方法1: 使用get_price函数
            current_price = get_price(ContextInfo, stock_code)
        except:
            try:
                # 方法2: 使用get_full_tick
                full_tick = ContextInfo.get_full_tick([stock_code])
                if stock_code in full_tick and 'lastPrice' in full_tick[stock_code]:
                    current_price = full_tick[stock_code]['lastPrice']
            except:
                try:
                    # 方法3: 从价格字典获取
                    if stock_code in g.price and 'close' in g.price[stock_code]:
                        price_data = g.price[stock_code]['close']
                        if len(price_data) > 0:
                            current_price = price_data[-1]
                except:
                    pass
        
        if current_price <= 0:
            # 如果无法获取价格，使用默认价格
            current_price = 20  # 假设平均价格为20元
            
        # 获取股票相关信息以更准确估算
        stock_detail = None
        try:
            stock_detail = ContextInfo.get_instrumentdetail(stock_code)
        except:
            pass
            
        # 设置默认估算成交量
        estimated_volume = 10000000  # 默认1000万成交量
        
        # 根据价格估算更合理的成交量
        # 经验公式: 成交量与价格成反比，低价股成交量通常更大
        # 约100万元成交额的合理估算
        if current_price > 0:
            # 基础估算: 100万元 / 当前价格
            base_volume = int(1000000 / current_price)
            
            # 根据股票类型调整
            if stock_detail and 'InstrumentName' in stock_detail:
                name = stock_detail['InstrumentName']
                # 大盘蓝筹股通常成交量更大
                if '银行' in name or '保险' in name or '石油' in name:
                    base_volume *= 3
                # 科技股通常活跃度更高
                elif '科技' in name or '电子' in name or '半导体' in name:
                    base_volume *= 2
            
            estimated_volume = base_volume
            
        # 确保成交量在合理范围内
        estimated_volume = max(500000, min(estimated_volume, 50000000))
        
        # 只在调试模式下记录日志
        if hasattr(g, 'show_volume_warnings') and g.show_volume_warnings:
            log_message(f"为股票 {stock_code} 估算成交量: {estimated_volume}")
            
        return estimated_volume
    except Exception as e:
        # 只在调试模式下记录错误
        if hasattr(g, 'show_volume_warnings') and g.show_volume_warnings:
            log_message(f"估算成交量出错: {str(e)}")
        return 10000000  # 出错时返回默认值而不是None，确保流程继续

def check_sufficient_funds(available_cash, stock_price):
    """
    检查可用资金是否足够购买至少一手(100股)股票
    
    Args:
        available_cash: 可用资金
        stock_price: 股票价格
    
    Returns:
        bool: 资金是否足够
    """
    min_purchase = stock_price * 100  # 一手100股
    # 考虑手续费等因素，预留1%的余量
    required_cash = min_purchase * 1.01
    
    return available_cash >= required_cash

def calculate_dynamic_red_line_threshold(stock_code, holding_days, price_data):
    """
    动态计算红线止损阈值，根据多个因素自适应调整
    
    Args:
        stock_code: 股票代码
        holding_days: 持有天数
        price_data: 价格数据，用于计算波动率
    
    Returns:
        调整后的红线止损阈值
    """
    try:
        # 基础阈值
        base_threshold = Config.RED_LINE_STOP_LOSS_THRESHOLD
        
        # 因素1: 根据股票波动率调整
        # 计算股票的历史波动率
        stock_volatility = 0
        N = Config.DYNAMIC_RED_LINE_VOLATILITY_DAYS
        close_prices = price_data['close']
        if len(close_prices) > N:
            # 取最近N+1天，保证diff和分母长度一致
            recent_closes = close_prices[-(N+1):]
            returns = np.diff(recent_closes) / recent_closes[:-1]
            stock_volatility = np.std(returns) * 100  # 转为百分比
        else:
            # 使用已有的波动率记录
            stock_volatility = g.stock_volatility.get(stock_code, 2.0)
        
        # 波动率调整因子: 波动率高的股票需要更大的止损阈值
        volatility_factor = min(
            Config.DYNAMIC_RED_LINE_VOLATILITY_MAX, 
            max(Config.DYNAMIC_RED_LINE_VOLATILITY_MIN, 
                stock_volatility / Config.DYNAMIC_RED_LINE_VOLATILITY_DIVISOR)
        )
        
        # 因素2: 根据持有时间调整
        # 持仓时间短时使用更严格的止损，持仓时间长时可以更宽松
        time_factor = min(
            Config.DYNAMIC_RED_LINE_TIME_MAX, 
            max(Config.DYNAMIC_RED_LINE_TIME_BASE, 
                Config.DYNAMIC_RED_LINE_TIME_BASE + Config.DYNAMIC_RED_LINE_TIME_INCREMENT * holding_days)
        )
        
        # 因素3: 考虑当前市场整体情况
        market_factor = 1.0
        try:
            # 获取大盘指数数据（沪深300）
            index_code = '000300.SH'
            index_data = ContextInfo.get_market_data_ex_ori(['close'], [index_code], period='1d', count=10)
            if index_code in index_data and 'close' in index_data[index_code]:
                index_close = index_data[index_code]['close']
                # 计算大盘短期波动率
                index_returns = np.diff(index_close) / index_close[:-1]
                market_volatility = np.std(index_returns) * 100
                
                # 市场波动大时提高阈值
                if market_volatility > Config.DYNAMIC_RED_LINE_MARKET_HIGH_VOLATILITY:  # 高波动市场
                    market_factor = min(
                        Config.DYNAMIC_RED_LINE_MARKET_HIGH_FACTOR, 
                        max(1.0, market_volatility / Config.DYNAMIC_RED_LINE_MARKET_DIVISOR)
                    )
                elif market_volatility < Config.DYNAMIC_RED_LINE_MARKET_LOW_VOLATILITY:  # 低波动市场
                    market_factor = Config.DYNAMIC_RED_LINE_MARKET_LOW_FACTOR
        except:
            # 获取失败时使用默认值
            pass
        
        # 因素4: 考虑盈亏状态
        profit_factor = 1.0
        try:
            positions = get_trade_detail_data(account, accountType, 'POSITION')
            for p in positions:
                position_code = p.m_strInstrumentID + '.' + p.m_strExchangeID
                if position_code == stock_code:
                    profit_rate = p.m_dProfitRate
                    if profit_rate > Config.DYNAMIC_RED_LINE_PROFIT_HIGH:  # 盈利>阈值时可以更宽松
                        profit_factor = Config.DYNAMIC_RED_LINE_PROFIT_FACTOR
                    elif profit_rate < Config.DYNAMIC_RED_LINE_LOSS_HIGH:  # 亏损>阈值时更谨慎
                        profit_factor = Config.DYNAMIC_RED_LINE_LOSS_FACTOR
                    break
        except:
            pass
            
        # 综合计算最终阈值
        final_threshold = base_threshold * volatility_factor * time_factor * market_factor * profit_factor
        
        # 限制最终阈值在合理范围内
        final_threshold = min(Config.DYNAMIC_RED_LINE_MAX_THRESHOLD, max(Config.DYNAMIC_RED_LINE_MIN_THRESHOLD, final_threshold))
        
        # 记录日志
        log_message(f"动态红线止损({stock_code}): 基础={base_threshold*100:.2f}%, 波动率={stock_volatility:.2f}%, 调整后={final_threshold*100:.2f}%")
        
        return final_threshold
        
    except Exception as e:
        log_message(f"计算动态红线止损阈值出错: {str(e)}，使用默认值{Config.RED_LINE_STOP_LOSS_THRESHOLD*100:.2f}%")
        return Config.RED_LINE_STOP_LOSS_THRESHOLD  # 出错时返回默认值

def sell_stock_in_lots(context_info, stock_code, total_vol, reason="", is_partial=False):
    """
    按整手(100股)卖出股票
    
    参数:
    context_info: 上下文信息
    stock_code: 股票代码
    total_vol: 计划卖出的总数量
    reason: 卖出原因
    is_partial: 是否为分批卖出，如果是，且总持仓仅1-2手时采用特殊处理
    
    返回:
    实际卖出数量
    """
    # 确保total_vol是整数
    total_vol = int(total_vol)
    
    # 获取当前总持仓量
    total_position = 0
    try:
        positions = get_trade_detail_data(account, accountType, 'POSITION')
        for p in positions:
            if p.m_strInstrumentID + '.' + p.m_strExchangeID == stock_code:
                total_position = p.m_nVolume
                break
    except:
        log_message(f"获取{stock_code}持仓信息失败")
    
    # 特殊处理小额持仓的分批止盈
    if is_partial and total_position <= 200:  # 持仓1-2手时
        if total_position == 100:  # 只有1手
            log_message(f"{stock_code}持仓仅1手(100股)，分批止盈改为全部卖出")
            sell_vol = 100
        else:  # 2手时，分批卖出1手
            log_message(f"{stock_code}持仓为2手(200股)，分批止盈卖出1手")
            sell_vol = 100
    else:
        # 正常处理：向下取整到100的倍数
        sell_vol = (total_vol // 100) * 100
        
        # 确保至少卖出1手
        if sell_vol < 100 and total_vol >= 100:
            sell_vol = 100
    
    # 只有当卖出量至少为1手时才执行
    if sell_vol >= 100:
        passorder(24, 1101, account, stock_code, 8, 0, sell_vol, '', 2, '', context_info)
        log_message(f"{reason} {stock_code}：卖出{sell_vol}股")
        return sell_vol
    else:
        log_message(f"跳过卖出 {stock_code}：数量{total_vol}不足1手(100股)")
        return 0

def is_large_cap_stock(ContextInfo, stock_code):
    """判断是否为大盘股，根据股票名称关键字判断"""
    try:
        # 获取股票名称
        stock_detail = ContextInfo.get_instrumentdetail(stock_code)
        if stock_detail is None or 'InstrumentName' not in stock_detail:
            return False  # 无法获取名称时默认不是大盘股
        
        stock_name = stock_detail['InstrumentName']
        
        # 检查是否包含大盘股关键字
        for keyword in Config.LARGE_CAP_KEYWORDS:
            if keyword in stock_name:
                print(f"排除大盘股: {stock_code} {stock_name} (匹配关键字: {keyword})")
                return True
        
        return False
    except Exception as e:
        log_message(f"检查大盘股时出错: {str(e)}")
        return False  # 出错时默认不是大盘股

# === 新增ATR计算函数 ===
def calculate_atr(price_data, period=14):
    """
    计算ATR（Average True Range，平均真实波幅）
    Args:
        price_data: DataFrame，包含'high', 'low', 'close'
        period: ATR周期，默认14
    Returns:
        atr: float，最近一个ATR值
    """
    try:
        high = price_data['high']
        low = price_data['low']
        close = price_data['close']
        tr_list = []
        for i in range(1, len(close)):
            tr = max(
                high.iloc[i] - low.iloc[i],
                abs(high.iloc[i] - close.iloc[i-1]),
                abs(low.iloc[i] - close.iloc[i-1])
            )
            tr_list.append(tr)
        if len(tr_list) < period:
            return None
        atr = pd.Series(tr_list).rolling(period).mean().iloc[-1]
        return atr
    except Exception as e:
        print(f"计算ATR出错: {e}")
        return None

# === 优化持仓明细日志输出函数 ===
def print_position_details(positions, g, ContextInfo):
    print("====== 当前持仓明细 ======")
    if not positions:
        print("当前无持仓")
    else:
        print(f"{'代码':<10} {'市场':<6} {'总量':<6} {'可用':<6} {'成本价':<8} {'盈亏%':<8} {'红线止损':<10} {'ATR止损':<10} {'首批止盈':<10}")
        for p in positions:
            try:
                code = p.m_strInstrumentID
                market = p.m_strExchangeID
                total = p.m_nVolume
                avail = p.m_nCanUseVolume
                cost = p.m_dOpenPrice
                profit = p.m_dProfitRate * 100
                # 计算红线止损价、ATR止损价、首批止盈价
                stop_red = ''
                stop_atr = ''
                take_profit1 = ''
                s = code + '.' + market
                if s in g.price:
                    # 构造price_data
                    price_data = pd.DataFrame()
                    price_data['close'] = g.price[s]['close']
                    price_data['high'] = g.price[s]['high']
                    price_data['low'] = g.price[s]['low']
                    price_data['open'] = g.price[s]['open']
                    # 计算红白线
                    white_line, red_line = calculate_red_white_lines_exact(price_data)
                    last_red = red_line.iloc[-1] if not red_line.empty and not pd.isna(red_line.iloc[-1]) else None
                    # 红线止损价
                    threshold = Config.RED_LINE_STOP_LOSS_THRESHOLD
                    if last_red is not None:
                        stop_red = f"{last_red * (1 - threshold):.2f}"
                    # ATR止损价
                    atr = calculate_atr(price_data)
                    if last_red is not None and atr is not None:
                        stop_atr = f"{last_red - Config.ATR_STOP_MULTIPLIER * atr:.2f}"
                    # 首批止盈价
                    if cost > 0:
                        take_profit1 = f"{cost * (1 + Config.FIRST_TAKE_PROFIT_THRESHOLD):.2f}"
                print(f"{code:<10} {market:<6} {total:<6} {avail:<6} {cost:<8.2f} {profit:<8.2f} {stop_red:<10} {stop_atr:<10} {take_profit1:<10}")
            except Exception as e:
                print(f"持仓对象解析出错: {e}")
    print("="*60)











