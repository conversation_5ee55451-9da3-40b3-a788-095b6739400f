#encoding:gbk


open_long_num = 2
open_short_num = 2
hand = 1  # 交易张数
moving_tick = 0.020  # 移动止损点位  #期权的波动单位为0.0001
fs_line = 0.010 #  FS即时价位—分时均线价位<0.1
sleep_time = 25 # 分钟 (5分钟周期下调整为25分钟)
stoploss_ratio = -25 # % 止损比例% 盈亏比例低于该值时卖出
stopprofit_ratio = 25 #% 盈利比例% 盈亏比例高于该值时卖出


import math
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta


class G():
	pass


g = G()
g.buy_long = 0
g.buy_short = 0
g.hold = 0
g.hold_price = 0
g.open_price = 0
g.trace_time_long = 0
g.trace_time_short = 0
g.opened_t = []
g.hold_code = ''


def init(ContextInfo):
	g.remark = ContextInfo.request_id[-10:]
	
	g.call_one = None
	g.put_one = None
	ContextInfo.set_account(account)
	g.undl_code = g.code = g.stock = ContextInfo.stockcode+'.'+ContextInfo.market
	g.curr_hold = None


def after_init(ContextInfo):
	download_history_data(g.code,'1d','********','********')
	return ContextInfo.get_trading_dates('SHO','','********',count=2, period='1d')
	

def handlebar(ContextInfo):
	timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
	bar_date = timetag_to_datetime(timetag, '%Y%m%d%H%M%S')
	
	# 确保在使用t变量前先定义它
	if not ContextInfo.is_last_bar():
		return
		
	price_1d=ContextInfo.get_market_data_ex(['close','open'],[g.code],
							period='1d',count=2,subscribe=False)[g.code]
	if price_1d.index[-1]==bar_date[:8]:
		CC = price_1d.iloc[-2]['close']
		OO = price_1d.iloc[-2]['open']
	else:
		CC = price_1d.iloc[-1]['close']
		OO = price_1d.iloc[-1]['open']
	
	start, end = after_init(ContextInfo)
	
	# 修改周期为5分钟
	price = ContextInfo.get_market_data_ex(['close','amount','volume','high','low'],[g.code],period='5m',
					start_time=end+'093000',
					end_time=end+'150000',
					)[g.code]
	C = CLOSE = price['close']
	H = HIGH = price['high']
	L = LOW = price['low']
	VOL = price['volume']
	price_len = price.shape[0]
	if price_len<=6:  # 5分钟周期下至少需要6根K线才有足够的数据
		return
		
	# 定义t变量 - 5分钟周期
	t = price.index[-1][-6:-2]
	if t<='0935':  # 9:35开始执行策略，适应5分钟周期
		return 

	try:
		CG,FL,MA_Line,FS,B1,B2 = cal_vba(ContextInfo, price)
	
		# 检查是否有NaN值
		if any([isinstance(x, np.ndarray) and np.isnan(x).any() for x in [CG, FL, MA_Line, FS, B1, B2]]):
			print("警告: 指标计算结果包含NaN值")
			return
			
		first, last_time = get_shape(CG,FL,MA_Line,FS,B1,B2)
		pct = time.strftime("%H%M%S")
		b1 = not pct >='145000' # 收盘前10分钟清仓，5分钟周期下调整为14:50
	
		# 检查CG是否为数组且长度足够
		if not isinstance(CG, np.ndarray) or len(CG) < 5:
			print("警告: CG不是数组或长度不足")
			return
			
		if any([math.isnan(CG[i]) for i in [-1,-2,-3,-4,-5]] ):
			return
		print(f'{g.code} {t} c:{C[-1]} 均线：{MA_Line[-1]} B1:{B1[-3:]} FS:{FS[-1]} 昨CC:{CC} 昨OO:{OO} ')
	
		# 计算看透主力指标 - 现在返回更多信息
		OBV1, OBV2, OBV3, MAC3, DIF, DEA, MACD, current_yellow_red, current_green, open_long_condition, open_short_condition = cal_kantouz(price)
	
		# 打印看透主力指标信息
		try:
			print(f'看透主力指标 - DIF:{round(float(DIF[-1]),4)} DEA:{round(float(DEA[-1]),4)} MACD:{round(float(MACD[-1]),4)}')
			print(f'OBV3:{round(float(OBV3[-1]),2)} MAC3:{round(float(MAC3[-1]),4)} OBV3变化:{round(float(OBV3[-1]-OBV3[-2]),2)} MAC3变化:{round(float(MAC3[-1]-MAC3[-2]),4)}')
		except (IndexError, TypeError) as e:
			print(f"指标打印错误: {e}")
		
		# 安全地访问数组元素
		def safe_get(arr, idx, default=False):
			try:
				if isinstance(arr, np.ndarray) and len(arr) > abs(idx):
					return arr[idx]
				return default
			except:
				return default
		
		last_five_minute_long = all([safe_get(CG, -1) == safe_get(FL, -1) == safe_get(FS, -1)])
			
		last_five_minute_short = all([safe_get(CG, -1) != safe_get(FL, -1) and safe_get(CG, -1) != safe_get(FS, -1) and safe_get(FL, -1) != safe_get(FS, -1)])
		
		# 使用新的开仓条件
		tj6_long = open_long_condition
		
		# 只使用条件6作为开仓条件
		kaiduo = tj6_long
		
		first, last_time = get_shape_kong(CG,FL,MA_Line,FS,B1,B2)
		
		# 使用新的开仓条件
		tj6_short = open_short_condition
		
		# 只使用条件6作为开仓条件
		kaikong = tj6_short
		
		# 条件1 买认购
		# 1.现价线和FS线（单红色线）在分时均线上方 且 B1-B2>0
		# 2.首次出现三色线，且在分时均线上方持续时间<15分钟，再度变成单红色线
		# 3.FS即时价位—分时均线价位<5
		
		#现价线和FS线（单红色线）在分时均线上方 且 B1-B2>0
		#fs金叉ma
		cross_up =FS[-1]>MA_Line[-1] and FS[-2]<=MA_Line[-2] and B1[-1]-B2[-1]>0
		#开多 
		tj_long = first==1 and last_time<=3 and last_five_minute_long and C[-1]>MA_Line[-1] and FS[-1] > MA_Line[-1] and B1[-1]-B2[-1]>0 and FS[-1]-MA_Line[-1]<fs_line
		# 开多
		
		# 条件3 # 买多时，绿色（B1<B2）不能超过15分钟
		b12 = list(B1<B2)
		b12kong = list(B1>B2)
		#b12.reverse()
		#print(b12)
		#tj3_long = B1[-1]>20 and B1[-2]<=20 and not all(b12kong[-10:-1]) and MA_Line[-1]-FS[-1]>fs_line
		tj3_long = B1[-1]>80 and B1[-2]<=80 and not all(b12[-3:-1]) and C[-1]-MA_Line[-1]>=fs_line
		
		# 条件6 - 使用新的开仓条件
		tj6_long = open_long_condition
		
		# 只使用条件6作为开仓条件
		kaiduo = tj6_long
		#kaiduo = ((tj_long or tj3_long) and C[-1]>(CC+OO)/2) or tj6_long

		#fs死叉
		cross_down =FS[-1]<MA_Line[-1] and FS[-2]>=MA_Line[-2] and B1[-1]-B2[-1]<0
		# 条件1 买认购
		# 1.现价线和FS CG FL（三色线）在分时均线下方 且B1-B2<0
		# 2.首次出现单条红色线，且在分时均线下方持续时间<15分钟，再度变成三色线
		# 3.分时均线价位—FS即时价位<5
		tj_short = first==1 and last_time<=3 and last_five_minute_short and C[-1]<MA_Line[-1] and max(FS[-1],CG[-1],FL[-1]) < MA_Line[-1]  and B1[-1]-B2[-1]<0 and MA_Line[-1] - FS[-1]<fs_line

		#开空 条件1 ，吗，买认沽
		# 1.现价线和FS CG FL（三色线）在分时均线下方 且B1-B2<0
		# 2.首次出现单条红色线，且在分时均线下方持续时间<15分钟，再度变成三色线
		# 3.分时均线价位—FS即时价位<5
		# 条件3 # 买空时，红色（B1>B2）不能超过15分钟
		#tj3_short = B1[-2]<80 and B1[-3]>=80 and not all(b12[-10:-1]) and FS[-1]>MA_Line[-1] and C[-1]-MA_Line[-1]>fs_line
		tj3_short = B1[-1]>20 and B1[-2]<=20 and not all(b12kong[-3:-1]) and MA_Line[-1]-C[-1]>=fs_line
		
		# 条件6 - 使用新的开仓条件
		tj6_short = open_short_condition
		
		# 只使用条件6作为开仓条件
		kaikong = tj6_short
		#kaikong = ((tj3_short or tj_short) and C[-1]<(CC+OO)/2) or tj6_short

		if g.hold== 0 and b1 and g.buy_long<open_long_num and kaiduo:
			buy_long = True
		else:
			buy_long = False
		if g.hold ==0 and b1 and g.buy_short<open_short_num and kaikong:
			buy_short = True
		else:
			buy_short = False

		if buy_long and time.time()-g.trace_time_long>sleep_time*60 and t not in g.opened_t and g.hold !=1:
			if g.hold == -1:
				passorder(51, 1101, account, g.put_one, 12, 0, hand, '',1,'策略平空'+g.remark,ContextInfo)
			call_one, put_one = get_option_real_one(ContextInfo)
			g.call_one = call_one
			g.buy_long+=1
			passorder(50, 1101, account, call_one, 12,0, hand,'',1,'期权策略开多'+g.remark,ContextInfo)
			g.curr_hold = call_one
			g.hold = 1
			g.trace_time_long = time.time()
			g.hold_price = 0
			g.open_price = 0
			g.opened_t.append(t)
			g.hold_code = call_one
			print(f'{call_one} 开多 [看透主力指标] 黄中带红柱 (前一周期为绿柱)')

		if buy_short and time.time()-g.trace_time_short>sleep_time*60 and t not in g.opened_t and g.hold !=-1:
			if g.hold == 1:
				passorder(51, 1101, account, g.call_one, 12, 0, hand, '',1,'策略平多'+g.remark,ContextInfo)
			call_one, put_one = get_option_real_one(ContextInfo)
			g.put_one = put_one
			passorder(50, 1101, account, put_one, 12,0, hand,'',1,'期权策略开空'+g.remark,ContextInfo)
			print(f'{g.put_one} 开空 [看透主力指标] 黄中带绿柱 (前一周期为红柱)')
			g.curr_hold = put_one
			g.buy_short+=1
			g.opened_t.append(t)
			g.hold_price = 9999999
			g.open_price = 0
			g.hold = -1
			g.hold_code = put_one
			g.trace_time_short = time.time()

		if g.hold != 0:
			print(g.put_one, g.call_one, g.hold, g.open_price)
		
		if g.open_price >0:
			full = ContextInfo.get_full_tick([g.curr_hold])[g.curr_hold]
			c = full['lastPrice']
			hold_ratio = round((c/g.open_price-1)*100,2)
			
			# 添加指标反转止损
			if g.hold == 1 and current_green:
				passorder(51, 1101, account, g.call_one, 12, 0, hand, '',1,'指标反转平多',ContextInfo)
				print(f'{g.call_one} 指标反转平多 (黄中带绿)')
				g.hold = 0
			elif g.hold == -1 and current_yellow_red:
				passorder(51, 1101, account, g.put_one, 12, 0, hand, '',1,'指标反转平空',ContextInfo)
				print(f'{g.put_one} 指标反转平空 (黄中带红)')
				g.hold = 0
			
			# 原有的止盈止损逻辑
			if g.hold>0:
				g.hold_price = max(c, g.hold_price)
				print(f'多 当前持有：{g.curr_hold},方向:{g.hold} 成本价:{g.open_price} 持有最高价：{g.hold_price} 最新价:{c} 盈亏比例:{hold_ratio}%')
			elif g.hold<0:
				g.hold_price = max(c, g.hold_price)
				print(f'空当前持有 {g.curr_hold},方向:{g.hold} 成本价:{g.open_price} 持有最低价：{g.hold_price} 最新价:{c} 盈亏比例:{hold_ratio}%')
			if g.hold ==1:
				g.hold_price = max(g.hold_price, c)
				if hold_ratio<stoploss_ratio or hold_ratio>stopprofit_ratio:
					# 7 平多,优先平昨
					passorder(51, 1101, account, g.call_one, 12, 0, hand, '',1,'百分比止盈止损',ContextInfo)
					print(f'{g.call_one} 平多 (百分比止盈止损: {hold_ratio}%)')
					g.hold =0
			if g.hold ==-1:
				g.hold_price = max(g.hold_price, c)
				if hold_ratio<stoploss_ratio or hold_ratio>stopprofit_ratio:
					passorder(51, 1101, account, g.put_one, 12, 0, hand, '',1,'百分比止盈止损',ContextInfo)
					g.hold =0
					print(f'{g.put_one} 平空 (百分比止盈止损: {hold_ratio}%)')
		if not b1:
			if g.hold !=0:
				orders = get_trade_detail_data(account,'STOCK','STOCK_OPTION')
				for o in orders:
					if o.m_nOrderStatus  in [50,55]: # 委托可撤时再撤单
						cancel(o.m_strOrderSysID, account, 'stock', ContextInfo)
				time.sleep(1)
				passorder(51, 1101, account, g.hold_code, 12, 0, hand, '',1,'',ContextInfo) 
				print('尾盘平仓',g.hold_code)
				g.hold = 0

	except Exception as e:
		print(f"handlebar执行错误: {e}")
		import traceback
		traceback.print_exc()
		return 

def order_callback(ContextInfo, orderInfo):
	print(orderInfo.m_strRemark, orderInfo.m_nOrderStatus, orderInfo.m_nOffsetFlag,orderInfo.m_dTradedPrice)
	if orderInfo.m_strRemark not in  ['期权策略开多'+g.remark,'期权策略开空'+g.remark]:
		return
	marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(orderInfo.m_strExchangeID, orderInfo.m_strExchangeID)
	k = orderInfo.m_strInstrumentID+'.'+marekt
	if k not in [g.call_one, g.put_one]:
		return
	#if orderInfo.m_nOrderStatus==56 and orderInfo.m_nOffsetFlag == 48 and orderInfo.m_dTradedPrice>0:
	#	g.open_price = round(orderInfo.m_dTradedPrice,1)
	#	g.hold_price = round(orderInfo.m_dTradedPrice,1)
	if orderInfo.m_nOrderStatus==56 and orderInfo.m_strRemark.startswith('期权策略开多'+g.remark):
		g.buy_long+=1
		print(f"{g.code} 开多次数+1 {g.buy_long}")
	if orderInfo.m_nOrderStatus==56 and orderInfo.m_strRemark.startswith('期权策略开空'+g.remark):
		g.buy_short+=1
		print(f"{g.code} 开空次数+1 {g.buy_short}")

	if orderInfo.m_nOrderStatus==57 and orderInfo.m_nOffsetFlag == 48:
		g.hold =0
		print("order_callback set 0")

def orderError_callback(ContextInfo,passOrderInfo,msg):
	if '期权策略'+g.remark in passOrderInfo.strategyName:
		g.hold =0
		print("orderError_callback set 0", msg)
	if '期权策略开空'+g.remark in passOrderInfo.strategyName:
		g.buy_short+=1
		print(f"{g.code} 开空次数+1 {g.buy_short}")
	if '期权策略开多'+g.remark in passOrderInfo.strategyName:
		g.buy_long+=1
		print(f"{g.code} 开多次数+1 {g.buy_long}")



def deal_callback(ContextInfo, dealInfo):
	print(f"deal callback m_nOffsetFlag:[{dealInfo.m_nOffsetFlag}] m_strRemark:[{dealInfo.m_strRemark}], [{dealInfo.m_strInstrumentID}] [{dealInfo.m_strExchangeID}]  [{dealInfo.m_dPrice}]",)
	if dealInfo.m_strRemark not in  ['期权策略开多'+g.remark,'期权策略开空'+g.remark]:
		return
	
	marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
	k = dealInfo.m_strInstrumentID+'.'+marekt
	print(k in [g.call_one, g.put_one],g.code.find(dealInfo.m_strInstrumentID))
	if k not in [g.call_one, g.put_one]:
		return
	if dealInfo.m_nOffsetFlag == 48:
		print("deal callback", dealInfo.m_dPrice)
		g.open_price = round(dealInfo.m_dPrice, 4)
		g.hold_price = round(dealInfo.m_dPrice,4)

def REF(S, N=1):          #对序列整体下移动N,返回序列(shift后会产生NAN)    
    return pd.Series(S).shift(N).values  
def SMA(S, N, M=1):       #中国式的SMA,至少需要120周期才精确 (雪球180周期)    alpha=1/(1+com)    
    return pd.Series(S).ewm(alpha=M/N,adjust=False).mean().values           #com=N-M/M
def SUM(S, N):            #对序列求N天累计和，返回序列    N=0对序列所有依次求和         
    return pd.Series(S).rolling(N).sum().values if N>0 else pd.Series(S).cumsum().values  
def HHV(S,N):             #HHV(C, 5) 最近5天收盘最高价        
    return pd.Series(S).rolling(N).max().values     

def LLV(S,N):             #LLV(C, 5) 最近5天收盘最低价     
    return pd.Series(S).rolling(N).min().values    
    
def HHVBARS(S,N):         #求N周期内S最高值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]),raw=True).values 

def LLVBARS(S,N):         #求N周期内S最低值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]),raw=True).values    
  
def MA(S,N):              #求序列的N日简单移动平均值，返回序列                    
    return pd.Series(S).rolling(N).mean().values  
def EMA(S,N):             #指数移动平均,为了精度 S>4*N  EMA至少需要120周期     alpha=2/(span+1)    
    return pd.Series(S).ewm(span=N, adjust=False).mean().values     

def EMA(source, N:int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    """
    # if not isinstance(source, np.ndarray):
    #     source = np.array(source)
    M = 2
    if N<M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N-1
    M = 2
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M*x+d*temp)/(N+1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
	index = S.index
	result_list = []
	slist = list(S)

	for i in range(len(S.index)):
		l = slist[max(0, i+1-34):i+1]
		result_list.append(min(l))
	return pd.Series(result_list, index=index)
	
	
def PyHHV(S, N):
	index = S.index
	result_list = []
	slist = list(S)

	for i in range(len(S.index)):
		l = slist[max(0, i+1-34):i+1]
		result_list.append(max(l))
	return pd.Series(result_list, index=index) 

def cal_vba(ContextInfo, price):
	C = CLOSE = price['close']
	HIGH = price['close']
	LOW = price['close']
	AMT = price['amount']
	VOL = price['volume']
	
	try:
		# 检查数据是否足够
		if len(CLOSE) < 34:
			print("警告: 数据长度不足34，无法计算指标")
			return [0]*6
			
		# 安全除法函数
		def safe_divide(a, b, default=0):
			if isinstance(b, (int, float)) and b == 0:
				return default
			if isinstance(b, np.ndarray):
				b = np.where(b == 0, np.nan, b)
			try:
				result = a / b
				if isinstance(result, np.ndarray):
					result = np.nan_to_num(result, nan=default)
				return result
			except:
				return default
		
		# 计算分时均线，避免除零错误
		sum_cv = SUM(C*VOL, 0)
		sum_v = SUM(VOL, 0)
		MA_Line = safe_divide(sum_cv, sum_v)
		
		# 5分钟周期下，调整CG参数为8
		CG = MA(C, 8)
		FL = HHV(CG, 3)
		FS = CG-(FL-CG)
		
		VA6 = (2*CLOSE+HIGH+LOW)/4
		VA8 = LLV(LOW, 34)
		VARB = HHV(HIGH, 34)
		
		# 避免除零错误
		denominator = VARB-VA8
		numerator = VA6-VA8
		ratio = safe_divide(numerator, denominator, 0) * 100
		
		VARC = EMA(ratio, 13)
		VARD = EMA(0.667*REF(VARC,1)+0.333*VARC, 2)
		# 生命线:EMA(VARD,10)
		life_line = EMA(VARD, 10)

		VAR1 = HHV(HIGH,9)-LLV(LOW,9)
		VAR2 = HHV(HIGH,9)-CLOSE
		VAR3 = CLOSE-LLV(LOW,9)
		
		# 避免除零错误
		VAR4 = safe_divide(VAR2, VAR1, 0) * 100 - 70
		
		denominator2 = HHV(HIGH,60)-LLV(LOW,60)
		numerator2 = CLOSE-LLV(LOW,60)
		VAR5 = safe_divide(numerator2, denominator2, 0) * 100

		VAR6 = (2*CLOSE+HIGH+LOW)/4
		index = VAR6.index
		VAR6 = pd.Series([v for v in VAR6], index=index)
		
		VAR7 = SMA(safe_divide(VAR3, VAR1, 0) * 100, 3, 1)
		VAR8 = PyLLV(CLOSE, min(34, len(LOW)))
		VAR9 = SMA(VAR7, 3, 1) - SMA(VAR4, 9, 1)
		VAR10 = pd.Series([v-100 if v>100 else 0 for v in VAR9])
		VAR11 = PyHHV(CLOSE, min(34, len(HIGH)))
		
		# 避免除零错误
		denominator3 = VAR11-VAR8
		numerator3 = VAR6-VAR8
		vv = safe_divide(numerator3, denominator3, 0) * 100
		
		vv = vv.fillna(0)
		vv = vv.replace([np.inf, -np.inf], np.nan).fillna(0)
		# 5分钟周期调整B1参数
		B1 = EMA(vv, 6)
		B2 = EMA(B1, 3)
		
		# 处理NaN值
		CG = np.nan_to_num(CG, nan=0)
		FL = np.nan_to_num(FL, nan=0)
		MA_Line = np.nan_to_num(MA_Line, nan=0)
		FS = np.nan_to_num(FS, nan=0)
		B1 = np.nan_to_num(B1, nan=0)
		B2 = np.nan_to_num(B2, nan=0)
		
		try:
			print(f"VAR6[-1]={VAR6.iloc[-1]}, VAR11[-1]={VAR11.iloc[-1]}, VAR8[-1]={VAR8.iloc[-1]}")
		except:
			print("无法打印VAR6/VAR11/VAR8的值")
			
		return CG, FL, MA_Line, FS, B1, B2
		
	except Exception as e:
		print(f"cal_vba执行错误: {e}")
		import traceback
		traceback.print_exc()
		return [0]*6


def get_shape(CG,FL,MA_Line,FS,B1,B2):
	# 2.首次出现三色线，且在分时均线上方持续时间<15分钟，再度变成单红色线
	count = 0
	record = []
	compare_ma = []
	for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
		if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
			continue
		record.append(cg==fl==fs)
		compare_ma.append(min(cg,fl,fs)>ma)
	pre=None
	record.reverse()
	compare_ma.reverse()
	i = 0
	if not record:
		return 0, 99
	if not record[0]:
		return 0, 99
	if not compare_ma[0]:
		return 0, 99
	# 获取大于均线的三条线
	uprecord = []
	for r, cpma in zip(record, compare_ma):
		if not cpma:
			break
		uprecord.append(r)
	# 去除重复连续
	drop_uprecord = []
	for i in range(len(uprecord)):
		if i == 0 or uprecord[i] != uprecord[i-1]:
			drop_uprecord.append(uprecord[i])
	if drop_uprecord.count(False)!=1:
		return 0, 99
	else:
		return 1, uprecord.count(False)

# 1.现价线和FS CG FL（三色线）在分时均线下方 且B1-B2<0
# 2.首次出现单条红色线，且在分时均线下方持续时间<15分钟，再度变成三色线
# 3.分时均线价位—FS即时价位<5
def get_shape_kong(CG,FL,MA_Line,FS,B1,B2):
	count = 0
	record = []
	compare_ma = []
	for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
		if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
			continue
		record.append(not cg==fl==fs)
		compare_ma.append(max(cg,fl,fs)<ma)
	# record True 三色线
	pre=None
	record.reverse()
	compare_ma.reverse()
	i = 0
	if not record:
		return 0, 99
	if not record[0]:
		return 0, 99
	if not compare_ma[0]:
		return 0, 99
	
	uprecord = []
	for r, cpma in zip(record, compare_ma):
		if not cpma:
			break
		uprecord.append(r)
	# 去除重复连续
	drop_uprecord = []
	for i in range(len(uprecord)):
		if i == 0 or uprecord[i] != uprecord[i-1]:
			drop_uprecord.append(uprecord[i])
	if drop_uprecord.count(False)!=1:
		return 0, 99
	else:
		return 1, uprecord.count(False)


# 计算看透主力指标 - 5分钟周期版本
def cal_kantouz(price):
	CLOSE = price['close']
	VOL = price['volume']
	HIGH = price['high']
	LOW = price['low']
	
	# 确保数据长度足够
	if len(CLOSE) < 30:
		print("数据长度不足30，无法计算看透主力指标")
		return [0]*7 + [False, False, False, False]  # 返回默认值
	
	try:
		# VA:=IF(CLOSE>REF(CLOSE,1),VOL,-VOL)
		VA = np.where(CLOSE > REF(CLOSE, 1), VOL, -VOL)
		
		# OBV1:=SUM(IF(CLOSE=REF(CLOSE,1),0,VA),0)
		VA_adj = np.where(CLOSE == REF(CLOSE, 1), 0, VA)
		OBV1 = SUM(VA_adj, 0)
		
		# 5分钟周期下，调整一些参数
		# OBV2:=EMA(OBV1,3)-MA(OBV1,9)
		OBV2 = EMA(OBV1, 2) - MA(OBV1, 6)
		
		# OBV3:=EMA(IF(OBV2>0,OBV2,0),3)
		OBV3 = EMA(np.where(OBV2 > 0, OBV2, 0), 2)
		
		# MAC3:=MA(C,3)
		MAC3 = MA(CLOSE, 2)
		
		# 5分钟周期下，MACD参数调整
		SHORT = 6
		LONG = 13
		MID = 5
		
		# DIF:EMA(CLOSE,SHORT)-EMA(CLOSE,LONG)
		DIF = EMA(CLOSE, SHORT) - EMA(CLOSE, LONG)
		
		# DEA:EMA(DIF,MID)
		DEA = EMA(DIF, MID)
		
		# MACD:=(DIF-DEA)*2
		MACD = (DIF - DEA) * 2
		
		# 处理可能的NaN值
		DIF = np.nan_to_num(DIF, nan=0)
		DEA = np.nan_to_num(DEA, nan=0)
		MACD = np.nan_to_num(MACD, nan=0)
		OBV3 = np.nan_to_num(OBV3, nan=0)
		MAC3 = np.nan_to_num(MAC3, nan=0)
		
		# OBV3和MAC3的变化
		OBV3_ref = REF(OBV3, 1)
		MAC3_ref = REF(MAC3, 1)
		OBV3_ref = np.nan_to_num(OBV3_ref, nan=0)
		MAC3_ref = np.nan_to_num(MAC3_ref, nan=0)
		
		# DIF的变化
		DIF_ref = REF(DIF, 1)
		DIF_ref = np.nan_to_num(DIF_ref, nan=0)
		
		# 根据通达信源码:
		# 黄中带红柱: OBV3>REF(OBV3,1) AND MAC3>REF(MAC3,1)
		yellow_red_bar = np.logical_and(OBV3 > OBV3_ref, MAC3 > MAC3_ref)
		
		# 根据通达信源码:
		# 入1:IF(DIF>REF(DIF,1),DIF,DRAWNULL),COLORRED,LINETHICK1;
		# 入2:IF(DIF<REF(DIF,1),DIF,DRAWNULL),COLOR00FF00,LINETHICK1;
		# 红色DIF线表示DIF上升，绿色DIF线表示DIF下降
		dif_up = DIF > DIF_ref
		dif_down = DIF < DIF_ref
		
		# 根据分析，白色柱子可能是以下几种情况之一:
		# 1. MACD接近0
		# 2. DIF和DEA接近0或相交
		# 3. OBV3和MAC3变化不明显
		
		# 判断白色柱子 - 使用np.logical_or和np.logical_and替代位运算符
		# 5分钟周期下，调整白色柱子判断阈值
		cond1 = np.abs(MACD) < 0.002  # MACD接近0，阈值增大
		cond2 = np.abs(DIF - DEA) < 0.001  # DIF和DEA接近
		cond3 = np.logical_and(np.abs(OBV3 - OBV3_ref) < 0.2, np.abs(MAC3 - MAC3_ref) < 0.0002)  # OBV3和MAC3变化不明显
		
		white_bar = np.logical_or(np.logical_or(cond1, cond2), cond3)
		
		# 绿色柱子: 不是黄中带红且不是白色柱子
		green_bar = np.logical_and(np.logical_not(yellow_red_bar), np.logical_not(white_bar))
		
		# 处理可能的NaN值
		yellow_red_bar = np.nan_to_num(yellow_red_bar, nan=False)
		green_bar = np.nan_to_num(green_bar, nan=False)
		white_bar = np.nan_to_num(white_bar, nan=False)
		
		# 当前周期是否为黄中带红
		current_yellow_red = False
		if len(yellow_red_bar) >= 1:
			current_yellow_red = yellow_red_bar[-1]
		
		# 当前周期是否为绿色柱子
		current_green = False
		if len(green_bar) >= 1:
			current_green = green_bar[-1]
		
		# 当前周期是否为白色柱子
		current_white_bar = False
		if len(white_bar) >= 1:
			current_white_bar = white_bar[-1]
		
		# 前一周期是否为黄中带红
		prev_yellow_red = False
		if len(yellow_red_bar) >= 2:
			prev_yellow_red = yellow_red_bar[-2]
		
		# 前一周期是否为绿色柱子
		prev_green = False
		if len(green_bar) >= 2:
			prev_green = green_bar[-2]
		
		# 前一周期是否为白色柱子
		prev_white_bar = False
		if len(white_bar) >= 2:
			prev_white_bar = white_bar[-2]
		
		# 满足开空条件：前一周期为红柱，当前周期为绿柱
		open_short_condition = prev_yellow_red and current_green
		
		# 满足开多条件：前一周期为绿柱，当前周期为红柱
		open_long_condition = prev_green and current_yellow_red
		
		# 输出调试信息
		print(f"前一周期黄中带红:{prev_yellow_red} 当前周期绿柱:{current_green} 当前白色柱子:{current_white_bar} 开空条件:{open_short_condition}")
		print(f"前一周期绿柱:{prev_green} 当前周期黄中带红:{current_yellow_red} 当前白色柱子:{current_white_bar} 开多条件:{open_long_condition}")
		print(f"MACD值:{MACD[-1]:.6f} DIF值:{DIF[-1]:.6f} DEA值:{DEA[-1]:.6f}")
		print(f"OBV3当前值:{OBV3[-1]:.2f} OBV3变化:{(OBV3[-1]-OBV3_ref[-1]):.2f} MAC3当前值:{MAC3[-1]:.4f} MAC3变化:{(MAC3[-1]-MAC3_ref[-1]):.4f}")
		
		return OBV1, OBV2, OBV3, MAC3, DIF, DEA, MACD, current_yellow_red, current_green, open_long_condition, open_short_condition
	
	except Exception as e:
		print(f"计算看透主力指标出错: {e}")
		import traceback
		traceback.print_exc()
		return [0]*7 + [False, False, False, False]  # 返回默认值 

def get_option_real_one(ContextInfo):
	call_one = put_one = None
	now = time.strftime("%Y%m%d")
	call_list = get_current_month_option(ContextInfo, g.undl_code, now,'CALL')

	# call_list = get_current_month_option(ContextInfo, g.undl_code, '20231011','PUT')
	# print(len(call_list),call_list)
	# print([ContextInfo.get_instrumentdetail(s)['InstrumentName']for s in call_list])
	undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
	#实值实值认购一档
	call_dict = {call:ContextInfo.get_option_detail_data(call)['OptExercisePrice'] for call in call_list}
	real_list = {code for code in call_dict if call_dict[code]<undl_price }
	print("购实值 所有",real_list)
	real_list = sorted(real_list,key=lambda code:call_dict[code], reverse=True) #小于标的现价的行权价最大的票
	if real_list:
		print("购实一", real_list[0])
		call_one = real_list[0]

	call_list = get_current_month_option(ContextInfo, g.undl_code, now,'PUT')
	print(call_list)
	undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
	#市值认沽一档
	call_dict = {call:ContextInfo.get_option_detail_data(call)['OptExercisePrice'] for call in call_list}
	real_list = {code for code in call_dict if call_dict[code]>undl_price }
	print("沽 实值 所有",real_list)
	real_list = sorted(real_list,key=lambda code:call_dict[code], reverse=False) #小于标的现价的行权价最大的票
	if real_list:
		print("沽实一", real_list[0])
		put_one = real_list[0]
	return call_one, put_one


def get_current_month_option(ContextInfo,object, dedate, opttype="",):
	#dedate 日期 %Y%m%d 
	#获取截止到ddate这天还未到行权日的当月期权合约
	isavailavle = True
	result = []
	opt_by_month = {}
	undlMarket = "";
	undlCode = "";
	marketcodeList = object.split('.');
	if(len(marketcodeList) !=2):
		return [];
	undlCode = marketcodeList[0]
	undlMarket = marketcodeList[1];
	market = ""
	if(undlMarket == "SH"):
		if undlCode == "000016" or undlCode == "000300" or undlCode == "000852" or undlCode == "000905":
			market = 'IF'
		else:
			market = "SHO"
	elif(undlMarket == "SZ"):
		market = "SZO";
	if(opttype.upper() == "C"):
		opttype = "CALL"
	elif(opttype.upper() == "P"):
		opttype = "PUT"
	optList = []
	if market == 'SHO':
		optList += get_stock_list_in_sector('上证期权')
		
	elif market == 'SZO':
		optList += get_stock_list_in_sector('深证期权')
		
	elif market == 'IF':
		optList += get_stock_list_in_sector('中金所')
	for opt in optList:
		if(opt.find(market) < 0):
			continue
		inst = ContextInfo.get_option_detail_data(opt)
		if('optType' not in inst):
			continue
		endDate = inst['EndDelivDate']
		if( isavailavle and  str(endDate) <= dedate):
			continue
		if(opttype.upper()  != "" and  opttype.upper() != inst["optType"]):
			continue
		if 1: # option is trade,guosen demand
			createDate = inst['OpenDate'];
			openDate = inst['OpenDate'];
			if(createDate >= 1):
				openDate = min(openDate,createDate);
			if(openDate < 20150101 or str(openDate) > dedate):
				continue
		if(inst['ProductID'].find(undlCode) > 0 or inst['OptUndlCode'] == undlCode):
			result.append(opt)
			#print('append')
			#print(opt, inst)
			month = str(endDate)[:6]
			if month not in opt_by_month:
				opt_by_month[month] = [opt]
			else:
				opt_by_month[month].append(opt)
	opt_list = sorted(opt_by_month,reverse=False)
	print(opt_by_month.keys())
	for opt in opt_by_month:
		if opt_list:
			b = datetime.strptime(opt_list[0],'%Y%m')
			a = datetime.now()
			if (b-a).days<15:
				return opt_by_month[opt_list[0]]
			else:
				print(object,'未找到期权',opt_list[0],opt_by_month)
				return []
	else:
		return [] 