import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def calculate_wenhua_lines(price_data):
    """严格按照文华指标计算红白线值"""
    if len(price_data) < 10:  # 需要足够的历史数据
        return 0, 0, 0
        
    # 获取数据
    high = price_data['high'].values
    low = price_data['low'].values
    open_price = price_data['open'].values
    close = price_data['close'].values
    
    # 初始化结果数组
    h1 = np.zeros(len(price_data))
    l1 = np.zeros(len(price_data))
    h2 = np.zeros(len(price_data))
    l2 = np.zeros(len(price_data))
    k1 = np.zeros(len(price_data))
    k2 = np.zeros(len(price_data))
    g = np.zeros(len(price_data))
    
    # 计算HX和LX
    hx = np.zeros(len(price_data))
    lx = np.zeros(len(price_data))
    
    for i in range(len(price_data)):
        if i >= 1:
            hx[i] = max(high[i-1:i+1])  # HHV(HIGH,2)
            lx[i] = min(low[i-1:i+1])   # LLV(LOW,2)
        else:
            hx[i] = high[i]
            lx[i] = low[i]
    
    # 计算H1和L1
    for i in range(5, len(price_data)):
        # H1条件: IFELSE(HX<REF(HX,1)&&HX<REF(HX,2)&&HX<REF(HX,4)&&LX<REF(LX,1)&&LX<REF(LX,3)&&LX<REF(LX,5)&&OPEN>CLOSE&&(HHV(OPEN,0)-CLOSE)>0,REF(HX,4),0)
        h1_condition = (
            hx[i] < hx[i-1] and 
            hx[i] < hx[i-2] and 
            hx[i] < hx[i-4] and 
            lx[i] < lx[i-1] and 
            lx[i] < lx[i-3] and 
            lx[i] < lx[i-5] and 
            open_price[i] > close[i] and 
            (max(open_price[:i+1]) - close[i]) > 0
        )
        
        if h1_condition:
            h1[i] = hx[i-4]  # REF(HX,4)
        
        # L1条件: IFELSE(LX>REF(LX,1)&&LX>REF(LX,3)&&LX>REF(LX,5)&&HX>REF(HX,1)&&HX>REF(HX,2)&&HX>REF(HX,4)&&OPEN<CLOSE&&(CLOSE-LLV(OPEN,0))>0,REF(LX,4),0)
        l1_condition = (
            lx[i] > lx[i-1] and 
            lx[i] > lx[i-3] and 
            lx[i] > lx[i-5] and 
            hx[i] > hx[i-1] and 
            hx[i] > hx[i-2] and 
            hx[i] > hx[i-4] and 
            open_price[i] < close[i] and 
            (close[i] - min(open_price[:i+1])) > 0
        )
        
        if l1_condition:
            l1[i] = lx[i-4]  # REF(LX,4)
    
    # 计算H2和L2 (VALUEWHEN函数)
    last_h1 = 0
    last_l1 = 0
    for i in range(len(price_data)):
        if h1[i] > 0:
            last_h1 = h1[i]
        h2[i] = last_h1
        
        if l1[i] > 0:
            last_l1 = l1[i]
        l2[i] = last_l1
    
    # 计算K1
    for i in range(len(price_data)):
        if h2[i] > 0 and close[i] > h2[i]:
            k1[i] = -3
        elif l2[i] > 0 and close[i] < l2[i]:
            k1[i] = 1
        else:
            k1[i] = 0
    
    # 计算K2 (VALUEWHEN函数)
    last_k1 = 0
    for i in range(len(price_data)):
        if k1[i] != 0:
            last_k1 = k1[i]
        k2[i] = last_k1
    
    # 计算G
    for i in range(len(price_data)):
        if k2[i] == 1:
            g[i] = h2[i]  # G:=IFELSE(K2=1,H2,L2)
        else:
            g[i] = l2[i]
    
    # 获取最新值
    latest_k2 = k2[-1]
    latest_g = g[-1]
    
    # 根据K2值确定显示的颜色
    white_line = latest_g if latest_k2 == 1 else None
    red_line = latest_g if latest_k2 == -3 else None
    
    return white_line, red_line, latest_k2

def get_k2_series(price_data):
    """计算完整的K2序列"""
    if len(price_data) < 10:
        return []
    
    # 获取数据
    high = price_data['high'].values
    low = price_data['low'].values
    open_price = price_data['open'].values
    close = price_data['close'].values
    
    # 初始化结果数组
    h1 = np.zeros(len(price_data))
    l1 = np.zeros(len(price_data))
    h2 = np.zeros(len(price_data))
    l2 = np.zeros(len(price_data))
    k1 = np.zeros(len(price_data))
    k2 = np.zeros(len(price_data))
    
    # 计算HX和LX
    hx = np.zeros(len(price_data))
    lx = np.zeros(len(price_data))
    
    for i in range(len(price_data)):
        if i >= 1:
            hx[i] = max(high[i-1:i+1])  # HHV(HIGH,2)
            lx[i] = min(low[i-1:i+1])   # LLV(LOW,2)
        else:
            hx[i] = high[i]
            lx[i] = low[i]
    
    # 计算H1和L1
    for i in range(5, len(price_data)):
        # H1条件
        h1_condition = (
            hx[i] < hx[i-1] and 
            hx[i] < hx[i-2] and 
            hx[i] < hx[i-4] and 
            lx[i] < lx[i-1] and 
            lx[i] < lx[i-3] and 
            lx[i] < lx[i-5] and 
            open_price[i] > close[i] and 
            (max(open_price[:i+1]) - close[i]) > 0
        )
        
        if h1_condition:
            h1[i] = hx[i-4]  # REF(HX,4)
        
        # L1条件
        l1_condition = (
            lx[i] > lx[i-1] and 
            lx[i] > lx[i-3] and 
            lx[i] > lx[i-5] and 
            hx[i] > hx[i-1] and 
            hx[i] > hx[i-2] and 
            hx[i] > hx[i-4] and 
            open_price[i] < close[i] and 
            (close[i] - min(open_price[:i+1])) > 0
        )
        
        if l1_condition:
            l1[i] = lx[i-4]  # REF(LX,4)
    
    # 计算H2和L2 (VALUEWHEN函数)
    last_h1 = 0
    last_l1 = 0
    for i in range(len(price_data)):
        if h1[i] > 0:
            last_h1 = h1[i]
        h2[i] = last_h1
        
        if l1[i] > 0:
            last_l1 = l1[i]
        l2[i] = last_l1
    
    # 计算K1
    for i in range(len(price_data)):
        if h2[i] > 0 and close[i] > h2[i]:
            k1[i] = -3
        elif l2[i] > 0 and close[i] < l2[i]:
            k1[i] = 1
        else:
            k1[i] = 0
    
    # 计算K2 (VALUEWHEN函数)
    last_k1 = 0
    for i in range(len(price_data)):
        if k1[i] != 0:
            last_k1 = k1[i]
        k2[i] = last_k1
    
    return k2.tolist()

# 创建测试数据
def create_test_data():
    """创建模拟的K线数据来测试信号检测"""
    np.random.seed(42)
    n = 50
    
    # 创建基础趋势
    base_trend = np.linspace(100, 110, n) + np.random.normal(0, 0.5, n)
    
    # 创建OHLC数据
    open_prices = base_trend + np.random.normal(0, 0.2, n)
    close_prices = base_trend + np.random.normal(0, 0.2, n)
    high_prices = np.maximum(open_prices, close_prices) + np.random.uniform(0, 0.5, n)
    low_prices = np.minimum(open_prices, close_prices) - np.random.uniform(0, 0.5, n)
    
    # 创建时间索引
    dates = pd.date_range('2024-01-01', periods=n, freq='5min')
    
    # 创建DataFrame
    df = pd.DataFrame({
        'open': open_prices,
        'high': high_prices,
        'low': low_prices,
        'close': close_prices
    }, index=dates)
    
    return df

def test_signal_detection():
    """测试信号检测功能"""
    print("开始测试文华指标信号检测...")
    
    # 创建测试数据
    test_data = create_test_data()
    print(f"测试数据长度: {len(test_data)}")
    print(f"测试数据前5行:\n{test_data.head()}")
    
    # 计算K2序列
    k2_series = get_k2_series(test_data)
    print(f"K2序列长度: {len(k2_series)}")
    print(f"K2序列最后10个值: {k2_series[-10:]}")
    
    # 检测信号
    if len(k2_series) >= 2:
        prev_k2 = k2_series[-2]
        curr_k2 = k2_series[-1]
        
        print(f"前一个K2值: {prev_k2}")
        print(f"当前K2值: {curr_k2}")
        
        # 检测买入信号 (K2从0变为1)
        buy_signal = (prev_k2 == 0 and curr_k2 == 1)
        print(f"买入信号: {buy_signal}")
        
        # 检测卖出信号 (K2从0变为-3)
        sell_signal = (prev_k2 == 0 and curr_k2 == -3)
        print(f"卖出信号: {sell_signal}")
        
        if buy_signal:
            print("✅ 检测到买入信号!")
        if sell_signal:
            print("✅ 检测到卖出信号!")
        if not buy_signal and not sell_signal:
            print("❌ 未检测到信号")
    else:
        print("❌ K2序列长度不足，无法检测信号")
    
    # 计算当前的红白线值
    white_line, red_line, k2 = calculate_wenhua_lines(test_data)
    print(f"\n当前红白线值:")
    print(f"白色线: {white_line}")
    print(f"红色线: {red_line}")
    print(f"K2值: {k2}")

if __name__ == "__main__":
    test_signal_detection() 