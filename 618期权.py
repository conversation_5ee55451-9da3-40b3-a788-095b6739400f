#encoding:gbk


open_long_num =2
open_short_num = 2
hand = 2  #交易张数
moving_tick = 0.020  # 移动止损点位  #期权的波动单位为0.0001
fs_line = 0.010 #  FS即时价位—分时均线价位<0.1
sleep_time = 5 #分钟 1分钟内不连续开同方向仓位
stoploss_ratio = -25 # % 止损比例% 盈亏比例低于该值时卖出
stopprofit_ratio = 25 #% 盈利比例% 盈亏比例高于该值时卖出
# 新增价格飙升保护参数
price_surge_threshold = 15  # 价格飙升阈值，涨幅超过15%视为飙升
price_pullback_threshold = 5  # 回撤阈值，从最高点回撤5%时触发止盈
# 小资金账户特殊处理参数
small_account_threshold = 10000  # 小资金账户阈值，持仓市值小于该值视为小资金账户
small_account_profit_ratio = 3  # 小资金账户止盈比例，盈利超过3%时触发止盈

# ================= 新增条件5参数 =================
# 红白线策略参数（来自期权策略买方.py）
# 红线：EMA(EMA(HLC3, 13), 8)
# 白线：EMA(HLC3, 13)
# 开多条件：
# 1. 白线转红线：白线从红线下方穿过红线
# 2. 突破白线：价格突破白线
# 3. 红线支撑：价格在红线上方受到支撑
# 开空条件：
# 1. 红线转白线：红线从白线下方穿过白线
# 2. 跌破红线：价格跌破红线
# 3. 白线压制：价格在白线下方受到压制
# ===============================================

import math
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

# 添加日志记录功能
def log_trade(message, log_type="INFO"):
    """
    记录交易日志
    
    参数:
    message: str 日志消息
    log_type: str 日志类型，如INFO, WARNING, ERROR等
    """
    try:
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"{current_time} [{log_type}] {message}"
        
        print(log_message)
        
        # 将日志写入文件
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        log_file = os.path.join(log_dir, f"trade_log_{datetime.now().strftime('%Y%m%d')}.txt")
        with open(log_file, "a", encoding="utf-8") as f:
            f.write(log_message + "\n")
    except Exception as e:
        print(f"记录日志时出错: {e}")

class G():
	pass


g = G()
g.buy_long = 0
g.buy_short = 0
g.hold = 0
g.hold_price = 0
g.open_price = 0
g.trace_time_long = 0
g.trace_time_short = 0
g.opened_t = []
g.hold_code = ''
g.last_status_time = 0  # 上次记录状态的时间
g.max_profit_ratio = 0  # 记录最大盈利比例，用于回撤止盈判断


def init(ContextInfo):
	g.remark = ContextInfo.request_id[-10:]
	
	g.call_one = None
	g.put_one = None
	ContextInfo.set_account(account)
	g.undl_code = g.code = g.stock = ContextInfo.stockcode+'.'+ContextInfo.market
	g.curr_hold = None
	g.last_status_time = time.time()
	g.max_profit_ratio = 0  # 初始化最大盈利比例
	
	# 记录策略启动日志
	log_trade(f"策略启动 - 标的: {g.code}, 账户: {account}", "SYSTEM")


def after_init(ContextInfo):
	download_history_data(g.code,'1d','********','********')
	return ContextInfo.get_trading_dates('SHO','','********',count=2, period='1d')
	

def handlebar(ContextInfo):
	timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
	bar_date = timetag_to_datetime(timetag, '%Y%m%d%H%M%S')
	
	# 定期记录交易状态（每5分钟）
	current_time = time.time()
	if current_time - g.last_status_time > 300:  # 5分钟 = 300秒
		log_trade(f"交易状态 - 标的:{g.code}, 持仓方向:{g.hold}, 持仓代码:{g.hold_code}, 开仓价:{g.open_price}, 最高/低价:{g.hold_price}, 开多次数:{g.buy_long}, 开空次数:{g.buy_short}", "STATUS")
		g.last_status_time = current_time
		
	if not ContextInfo.is_last_bar():
		return
	price_1d=ContextInfo.get_market_data_ex(['close','open'],[g.code],
							period='1d',count=2,subscribe=False)[g.code]
	if price_1d.index[-1]==bar_date[:8]:
		CC = price_1d.iloc[-2]['close']
		OO = price_1d.iloc[-2]['open']
	else:
		CC = price_1d.iloc[-1]['close']
		OO = price_1d.iloc[-1]['open']
	
	start, end = after_init(ContextInfo)
	
	price = ContextInfo.get_market_data_ex(['close','amount','volume','high','low'],[g.code],period='1m',
					start_time=end+'093000',
					end_time=end+'150000',
					)[g.code]
	C = CLOSE = price['close']
	price_len = price.shape[0]
	if price_len<=20:
		return
	t = price.index[-1][-6:-2]
	if t<='0930':# 9：56开始执行策略
		return
	CG,FL,MA_Line,FS,B1,B2 = cal_vba(ContextInfo, price)
	first, last_time = get_shape(CG,FL,MA_Line,FS,B1,B2)
	pct = time.strftime("%H%M%S")
	b1 = not pct >='145400' # 收盘前10分钟清仓
	if any([math.isnan(CG[i]) for i in [-1,-2,-3,-4,-5]] ):
		return
	print(f'{g.code} {t} c:{C[-1]} 均线：{MA_Line[-1]} B1:{B1[-3:]} FS:{FS[-1]} 昨CC:{CC} 昨OO:{OO} ')
	
	last_five_minute_long = all([CG[i]==FL[i]==FS[i] for i in (-1,)])
	ContextInfo.paint('B1',B1[-1],-1,0,)
	ContextInfo.paint('B2',B2[-1],-1,0,)
	last_five_minute_short = all([CG[i]!=FL[i] and CG[i]!=FS[i] and FL[i]!=FS[i] for i in (-1,)])
	# 条件1 买认购
	# 1.现价线和FS线（单红色线）在分时均线上方 且 B1-B2>0
	# 2.首次出现三色线，且在分时均线上方持续时间<15分钟，再度变成单红色线
	# 3.FS即时价位—分时均线价位<5
	
	#现价线和FS线（单红色线）在分时均线上方 且 B1-B2>0
	#fs金叉ma
	cross_up =FS[-1]>MA_Line[-1] and FS[-2]<=MA_Line[-2] and B1[-1]-B2[-1]>0
	#开多 
	tj_long = first==1 and last_time<=15 and last_five_minute_long and C[-1]>MA_Line[-1] and FS[-1] > MA_Line[-1] and B1[-1]-B2[-1]>0 and FS[-1]-MA_Line[-1]<fs_line
	# 开多
	
	# 条件3 # 买多时，绿色（B1<B2）不能超过15分钟
	b12 = list(B1<B2)
	b12kong = list(B1>B2)
	#b12.reverse()
	#print(b12)
	#tj3_long = B1[-1]>20 and B1[-2]<=20 and not all(b12kong[-10:-1]) and MA_Line[-1]-FS[-1]>fs_line
	tj3_long = B1[-1]>80 and B1[-2]<=80 and not all(b12[-10:-1]) and C[-1]-MA_Line[-1]>=fs_line
	kaiduo =  (tj_long or tj3_long) and C[-1]>(CC+OO)/2
	#kaiduo =  tj3_long

	first, last_time = get_shape_kong(CG,FL,MA_Line,FS,B1,B2)

	#fs死叉
	cross_down =FS[-1]<MA_Line[-1] and FS[-2]>=MA_Line[-2] and B1[-1]-B2[-1]<0
	# 条件1 买认购
	# 1.现价线和FS线（单红色线）在分时均线上方 且 B1-B2>0
	# 2.首次出现三色线，且在分时均线上方持续时间<15分钟，再度变成单红色线
	# 3.FS即时价位—分时均线价位<5
	tj_short = first==1 and last_time<=15 and last_five_minute_short and C[-1]<MA_Line[-1] and max(FS[-1],CG[-1],FL[-1]) < MA_Line[-1]  and B1[-1]-B2[-1]<0 and MA_Line[-1] - FS[-1]<fs_line

	#开空 条件1 ，吗，买认沽
	# 1.现价线和FS CG FL（三色线）在分时均线下方 且B1-B2<0
	# 2.首次出现单条红色线，且在分时均线下方持续时间<15分钟，再度变成三色线
	# 3.分时均线价位—FS即时价位<5
	# 条件3 # 买空时，红色（B1>B2）不能超过15分钟
	#tj3_short = B1[-2]<80 and B1[-3]>=80 and not all(b12[-10:-1]) and FS[-1]>MA_Line[-1] and C[-1]-MA_Line[-1]>fs_line
	tj3_short = B1[-1]>20 and B1[-2]<=20 and not all(b12kong[-10:-1]) and MA_Line[-1]-C[-1]>=fs_line
	kaikong = (tj3_short or tj_short) and C[-1]<(CC+OO)/2
	#kaikong = tj3_short
	
	# 新增条件5：从期权策略买方.py中引入的开多和开空条件
	try:
		# 计算红白线指标
		white_line_5m, red_line_5m = calculate_red_white_lines_exact(price)
		final_white_line = white_line_5m.iloc[-1] if not pd.isna(white_line_5m.iloc[-1]) else None
		final_red_line = red_line_5m.iloc[-1] if not pd.isna(red_line_5m.iloc[-1]) else None
		current_price = C[-1]
		
		# 计算均线
		MA_Line_5m = calculate_ma(price['close'], price['volume'], 20)
		
		# 判断趋势
		trend_5m = check_trend(price, MA_Line_5m.iloc[-1])
		
		# 检测红白线转换信号
		transition_signal = detect_line_transition(
			white_line_5m.iloc[-2] if len(white_line_5m) > 1 else None,
			white_line_5m.iloc[-1],
			red_line_5m.iloc[-2] if len(red_line_5m) > 1 else None,
			red_line_5m.iloc[-1]
		)
		
		# 打印红白线值
		print(f"红白线值 - 红线:{final_red_line if final_red_line is None else round(final_red_line, 4)}, 白线:{final_white_line if final_white_line is None else round(final_white_line, 4)}, 价格:{round(current_price, 4)}, 均线:{round(MA_Line_5m.iloc[-1], 4) if not pd.isna(MA_Line_5m.iloc[-1]) else None}")
		
		# 定义信号变量
		transition_long = transition_signal == "white_to_red"
		transition_short = transition_signal == "red_to_white"
		pullback_long_good = False
		pullback_short_good = False
		break_red_short = False
		break_white_long = False
		
		# 检查是否突破白线
		if final_white_line is not None and not pd.isna(final_white_line):
			if current_price > final_white_line:
				break_white_long = True
		
		# 检查是否回调到红线做多
		if final_red_line is not None and not pd.isna(final_red_line):
			if current_price > final_red_line:
				pullback_long_good = True
		
		# 检查是否反弹到白线做空
		if final_white_line is not None and not pd.isna(final_white_line):
			if current_price < final_white_line:
				pullback_short_good = True
		
		# 检查是否跌破红线
		if final_red_line is not None and not pd.isna(final_red_line):
			if current_price < final_red_line:
				break_red_short = True
		
		# 定义新的开多条件
		kaiduo_new = ((pullback_long_good or transition_long or break_white_long) and 
					current_price > MA_Line_5m.iloc[-1] and 
					g.buy_long < open_long_num and 
					b1 and 
					trend_5m == 1)
		
		# 定义新的开空条件
		kaikong_new = ((pullback_short_good or transition_short or break_red_short) and 
					current_price < MA_Line_5m.iloc[-1] and 
					g.buy_short < open_short_num and 
					b1 and 
					trend_5m == -1)
		
		# 合并条件
		kaiduo = kaiduo or kaiduo_new
		kaikong = kaikong or kaikong_new
		
		print(f"条件5 - 新增红白线策略: 开多={kaiduo_new}, 开空={kaikong_new}")
	except Exception as e:
		print(f"条件5计算出错: {e}")
		# 如果条件5计算出错，不影响原有条件

	if g.hold== 0 and b1 and g.buy_long<open_long_num and kaiduo:
		buy_long = True
	else:
		buy_long = False
	if g.hold ==0 and b1 and g.buy_short<open_short_num and kaikong:
		buy_short = True
	else:
		buy_short = False

	# 增加多腿策略的管理逻辑
	# 如果设置了多腿策略标志，则不允许同时持有多头和空头
	is_multi_leg_strategy = True  # 设置为True表示启用多腿策略管理
	
	if is_multi_leg_strategy:
		# 记录当前持仓方向
		if g.hold != 0:
			log_trade(f"当前持仓方向: {'多头' if g.hold > 0 else '空头'}, 代码: {g.hold_code}", "STATUS")
		
		# 如果已经有持仓，禁止开反向仓位
		if g.hold != 0:
			# 如果已有多头持仓，禁止开空
			if g.hold > 0 and buy_short:
				buy_short = False
				log_trade("多腿策略管理: 已有多头持仓，禁止开空", "TRADE")
				print("多腿策略管理: 已有多头持仓，禁止开空")
			
			# 如果已有空头持仓，禁止开多
			if g.hold < 0 and buy_long:
				buy_long = False
				log_trade("多腿策略管理: 已有空头持仓，禁止开多", "TRADE")
				print("多腿策略管理: 已有空头持仓，禁止开多")

	if buy_long and time.time()-g.trace_time_long>sleep_time*60 and t not in g.opened_t and g.hold !=1:
		# 多腿策略下，如果有空头持仓，先平空再开多
		if g.hold == -1:
			log_trade(f"多腿策略管理: 平掉空头持仓 {g.put_one} 后再开多", "TRADE")
			print(f"多腿策略管理: 平掉空头持仓 {g.put_one} 后再开多")
			passorder(51, 1101, account, g.put_one, 12, 0, hand, '',1,'策略平空'+g.remark,ContextInfo)
			time.sleep(1)  # 等待平仓指令处理
		call_one, put_one = get_option_real_one(ContextInfo)
		g.call_one = call_one
		g.buy_long+=1
		passorder(50, 1101, account, call_one, 12,0, hand,'',1,'期权策略开多'+g.remark,ContextInfo)
		g.curr_hold = call_one
		g.hold = 1
		g.trace_time_long = time.time()
		g.hold_price = 0
		g.open_price = 0
		g.opened_t.append(t)
		g.hold_code = call_one
		# 更新打印信息，包含新增条件的信息
		signal_info = ""
		try:
			if transition_long:
				signal_info += "白转红 "
			if break_white_long:
				signal_info += "突破白线 "
			if pullback_long_good:
				signal_info += "红线支撑 "
		except:
			signal_info = ""
		log_trade(f"{call_one} 开多 原条件:{tj_long , tj3_long} {signal_info}", "TRADE")
		print(f'{call_one} 开多 原条件:{tj_long , tj3_long} {signal_info}')

	if buy_short and time.time()-g.trace_time_short>sleep_time*60 and t not in g.opened_t and g.hold !=-1:
		# 多腿策略下，如果有多头持仓，先平多再开空
		if g.hold == 1:
			log_trade(f"多腿策略管理: 平掉多头持仓 {g.call_one} 后再开空", "TRADE")
			print(f"多腿策略管理: 平掉多头持仓 {g.call_one} 后再开空")
			passorder(51, 1101, account, g.call_one, 12, 0, hand, '',1,'策略平多'+g.remark,ContextInfo)
			time.sleep(1)  # 等待平仓指令处理
		call_one, put_one = get_option_real_one(ContextInfo)
		g.put_one = put_one
		passorder(50, 1101, account, put_one, 12,0, hand,'',1,'期权策略开空'+g.remark,ContextInfo)
		# 更新打印信息，包含新增条件的信息
		signal_info = ""
		try:
			if transition_short:
				signal_info += "红转白 "
			if break_red_short:
				signal_info += "跌破红线 "
			if pullback_short_good:
				signal_info += "白线压制 "
		except:
			signal_info = ""
		log_trade(f"{g.put_one} 开空 原条件:{tj_short or tj3_short} {signal_info}", "TRADE")
		print(f'{g.put_one} 开空 原条件:{tj_short or tj3_short} {signal_info}')
		g.curr_hold = put_one
		g.buy_short+=1
		g.opened_t.append(t)
		g.hold_price = 0  # 修改：将空头初始值设为0，后面会用min更新为实际价格
		g.open_price = 0
		g.hold = -1
		g.hold_code = put_one
		g.trace_time_short = time.time()

	if g.hold != 0:
		print(g.put_one, g.call_one, g.hold, g.open_price)
		
	if g.open_price >0:
		try:
			full = ContextInfo.get_full_tick([g.curr_hold])[g.curr_hold]
			c = full['lastPrice']
			
			# 记录原始数据，用于调试
			log_trade(f"价格数据 - 代码:{g.curr_hold}, 成本价:{g.open_price}, 当前价:{c}, 方向:{g.hold}", "DEBUG")
			
			# 确保价格有效
			if c <= 0:
				log_trade(f"获取到无效价格: {c}，跳过止盈止损检查", "WARNING")
				return
				
			hold_ratio = round((c/g.open_price-1)*100,2)
			
			# 更新最大盈利比例
			if hold_ratio > g.max_profit_ratio:
				old_max = g.max_profit_ratio
				g.max_profit_ratio = hold_ratio
				log_trade(f"最大盈利比例更新: {old_max}% -> {g.max_profit_ratio}%", "PROFIT")
			
			# 计算从最高点的回撤比例
			if g.max_profit_ratio > 0:
				pullback_ratio = g.max_profit_ratio - hold_ratio
				log_trade(f"当前回撤: 最大盈利:{g.max_profit_ratio}%, 当前盈利:{hold_ratio}%, 回撤幅度:{pullback_ratio}%", "DEBUG")
				
				# 价格飙升保护：当盈利超过阈值且回撤超过设定幅度时触发止盈
				price_surge_protection = g.max_profit_ratio >= price_surge_threshold and pullback_ratio >= price_pullback_threshold
				if price_surge_protection:
					log_trade(f"触发价格飙升保护 - 最大盈利:{g.max_profit_ratio}%, 当前盈利:{hold_ratio}%, 回撤:{pullback_ratio}%", "PROTECTION")
			else:
				price_surge_protection = False
				pullback_ratio = 0
			
			# 计算持仓市值
			position_value = c * hand * 10000  # 期权合约乘数一般为10000
			is_small_account = position_value < small_account_threshold
			if is_small_account:
				log_trade(f"小资金账户检测 - 持仓市值:{position_value}元，低于{small_account_threshold}元阈值", "ACCOUNT")
				if hold_ratio >= small_account_profit_ratio:
					log_trade(f"小资金账户止盈条件满足 - 当前盈利:{hold_ratio}%, 止盈阈值:{small_account_profit_ratio}%", "ACCOUNT")
			
			if g.hold>0:
				prev_hold_price = g.hold_price
				g.hold_price = max(c, g.hold_price)
				if g.hold_price != prev_hold_price:
					log_trade(f"多头最高价更新: {prev_hold_price} -> {g.hold_price}", "DEBUG")
				print(f'多 当前持有：{g.curr_hold},方向:{g.hold} 成本价:{g.open_price} 持有最高价：{g.hold_price} 最新价:{c} 盈亏比例:{hold_ratio}%')
			elif g.hold<0:
				prev_hold_price = g.hold_price
				if g.hold_price == 0:  # 如果是初始值，直接设置为当前价格
					g.hold_price = c
				else:
					g.hold_price = min(c, g.hold_price) # 修正：对空头使用min跟踪最低价
				if g.hold_price != prev_hold_price:
					log_trade(f"空头最低价更新: {prev_hold_price} -> {g.hold_price}", "DEBUG")
				print(f'空当前持有 {g.curr_hold},方向:{g.hold} 成本价:{g.open_price} 持有最低价：{g.hold_price} 最新价:{c} 盈亏比例:{hold_ratio}%')
				
			# 记录盈亏比例，便于查看是否达到止盈条件
			log_trade(f"盈亏状态 - 代码:{g.curr_hold}, 方向:{g.hold}, 成本价:{g.open_price}, 当前价:{c}, 盈亏比例:{hold_ratio}%, 止盈线:{stopprofit_ratio}%", "PROFIT")
			
			if g.hold ==1:
				g.hold_price = max(g.hold_price, c)
				# 移动止损：当盈利超过12%时，止损线上移至成本价的95%
				if hold_ratio > 12 and g.open_price > 0:
					dynamic_stoploss = max(stoploss_ratio, -5)  # 收紧止损
					print(f"多头移动止损 - 盈利超过12%，收紧止损至:{dynamic_stoploss}%")
				else:
					# 检查期权代码是否有效
					if g.call_one and is_valid_option_code(ContextInfo, g.call_one):
						dynamic_stoploss = calculate_dynamic_stoploss(ContextInfo, g.call_one)
					else:
						dynamic_stoploss = stoploss_ratio
						print(f"多头使用默认止损比例: {dynamic_stoploss}%，因为期权代码无效: {g.call_one}")
					
				print(f"多头止损检查 - 当前盈亏比例:{hold_ratio}%, 止损线:{dynamic_stoploss}%, 止盈线:{stopprofit_ratio}%")
				
				# 检查是否达到止盈条件
				if hold_ratio > stopprofit_ratio or price_surge_protection or (is_small_account and hold_ratio >= small_account_profit_ratio):
					if is_small_account and hold_ratio >= small_account_profit_ratio:
						reason = f"小资金账户止盈(市值:{position_value}元，盈利:{hold_ratio}%)"
					else:
						reason = "常规止盈" if hold_ratio > stopprofit_ratio else f"价格飙升保护(最高:{g.max_profit_ratio}%,回撤:{pullback_ratio}%)"
					log_trade(f"触发止盈 - 代码:{g.curr_hold}, 盈亏比例:{hold_ratio}%, 原因:{reason}", "PROFIT")
					passorder(51, 1101, account, g.call_one, 12, 0, hand, '', 1, '止盈平多'+g.remark, ContextInfo)
					print(f'{g.call_one} 平多，原因：{reason}')
					g.hold = 0
					g.max_profit_ratio = 0  # 重置最大盈利比例
				# 检查是否达到止损条件
				elif hold_ratio < dynamic_stoploss:
					log_trade(f"触发止损 - 代码:{g.curr_hold}, 盈亏比例:{hold_ratio}%, 止损线:{dynamic_stoploss}%", "LOSS")
					passorder(51, 1101, account, g.call_one, 12, 0, hand, '', 1, '止损平多'+g.remark, ContextInfo)
					print(f'{g.call_one} 平多，原因：止损')
					g.hold = 0
					g.max_profit_ratio = 0  # 重置最大盈利比例
			
			if g.hold ==-1:
				g.hold_price = min(g.hold_price, c)  # 修正：对空头应该跟踪最低价而不是最高价
				# 移动止损：当盈利超过12%时，止损线上移至成本价的-5%
				if hold_ratio > 12 and g.open_price > 0:
					dynamic_stoploss = max(stoploss_ratio, -5)  # 收紧止损
					print(f"空头移动止损 - 盈利超过12%，收紧止损至:{dynamic_stoploss}%")
				else:
					# 检查期权代码是否有效
					if g.put_one and is_valid_option_code(ContextInfo, g.put_one):
						dynamic_stoploss = calculate_dynamic_stoploss(ContextInfo, g.put_one)
					else:
						dynamic_stoploss = stoploss_ratio
						print(f"空头使用默认止损比例: {dynamic_stoploss}%，因为期权代码无效: {g.put_one}")
					
				print(f"空头止损检查 - 当前盈亏比例:{hold_ratio}%, 止损线:{dynamic_stoploss}%, 止盈线:{stopprofit_ratio}%")
				
				# 检查是否达到止盈条件
				if hold_ratio > stopprofit_ratio or price_surge_protection or (is_small_account and hold_ratio >= small_account_profit_ratio):
					if is_small_account and hold_ratio >= small_account_profit_ratio:
						reason = f"小资金账户止盈(市值:{position_value}元，盈利:{hold_ratio}%)"
					else:
						reason = "常规止盈" if hold_ratio > stopprofit_ratio else f"价格飙升保护(最高:{g.max_profit_ratio}%,回撤:{pullback_ratio}%)"
					log_trade(f"触发止盈 - 代码:{g.curr_hold}, 盈亏比例:{hold_ratio}%, 原因:{reason}", "PROFIT")
					passorder(51, 1101, account, g.put_one, 12, 0, hand, '', 1, '止盈平空'+g.remark, ContextInfo)
					print(f'{g.put_one} 平空，原因：{reason}')
					g.hold = 0
					g.max_profit_ratio = 0  # 重置最大盈利比例
				# 检查是否达到止损条件
				elif hold_ratio < dynamic_stoploss:
					log_trade(f"触发止损 - 代码:{g.curr_hold}, 盈亏比例:{hold_ratio}%, 止损线:{dynamic_stoploss}%", "LOSS")
					passorder(51, 1101, account, g.put_one, 12, 0, hand, '', 1, '止损平空'+g.remark, ContextInfo)
					print(f'{g.put_one} 平空，原因：止损')
					g.hold = 0
					g.max_profit_ratio = 0  # 重置最大盈利比例
		except Exception as e:
			log_trade(f"止盈止损检查出错: {e}", "ERROR")
			print(f"止盈止损检查出错: {e}")

	if not b1:
		if g.hold !=0:
			orders = get_trade_detail_data(account,'STOCK','STOCK_OPTION')
			for o in orders:
				if o.m_nOrderStatus  in [50,55]: # 委托可撤时再撤单
					cancel(o.m_strOrderSysID, account, 'stock', ContextInfo)
			time.sleep(1)
			passorder(51, 1101, account, g.hold_code, 12, 0, hand, '',1,'',ContextInfo) 
			print('尾盘平仓',g.hold_code)
			g.hold = 0
		# if g.hold ==-1:
			# passorder(51, 1101, account, g.put_one, 12, 0, hand, '',1,'',ContextInfo) # 尾盘平空
		# if g.hold == 1:
			# passorder(51, 1101, account, g.call_one, 12, 0, hand, '',1,'',ContextInfo) # 尾盘平多


def get_shape(CG,FL,MA_Line,FS,B1,B2):
	# 2.首次出现三色线，且在分时均线上方持续时间<15分钟，再度变成单红色线
	count = 0
	record = []
	compare_ma = []
	for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
		if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
			continue
		record.append(cg==fl==fs)
		compare_ma.append(min(cg,fl,fs)>ma)
	pre=None
	record.reverse()
	compare_ma.reverse()
	i = 0
	if not record:
		return 0, 99
	if not record[0]:
		return 0, 99
	if not compare_ma[0]:
		return 0, 99
	# 获取大于均线的三条线
	uprecord = []
	for r, cpma in zip(record, compare_ma):
		if not cpma:
			break
		uprecord.append(r)
	# 去除重复连续
	drop_uprecord = []
	for i in range(len(uprecord)):
		if i == 0 or uprecord[i] != uprecord[i-1]:
			drop_uprecord.append(uprecord[i])
	if drop_uprecord.count(False)!=1:
		return 0, 99
	else:
		return 1, uprecord.count(False)

# 1.现价线和FS CG FL（三色线）在分时均线下方 且B1-B2<0
# 2.首次出现单条红色线，且在分时均线下方持续时间<15分钟，再度变成三色线
# 3.分时均线价位—FS即时价位<5
def get_shape_kong(CG,FL,MA_Line,FS,B1,B2):
	count = 0
	record = []
	compare_ma = []
	for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
		if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
			continue
		record.append(not cg==fl==fs)
		compare_ma.append(max(cg,fl,fs)<ma)
	# record True 三色线
	pre=None
	record.reverse()
	compare_ma.reverse()
	i = 0
	if not record:
		return 0, 99
	if not record[0]:
		return 0, 99
	if not compare_ma[0]:
		return 0, 99
	
	uprecord = []
	for r, cpma in zip(record, compare_ma):
		if not cpma:
			break
		uprecord.append(r)
	# 去除重复连续
	drop_uprecord = []
	for i in range(len(uprecord)):
		if i == 0 or uprecord[i] != uprecord[i-1]:
			drop_uprecord.append(uprecord[i])
	if drop_uprecord.count(False)!=1:
		return 0, 99
	else:
		return 1, uprecord.count(False)


def get_shape_old(CG,FL,MA_Line,FS,B1,B2):
	#判断是不是首次出现的三色线
	#CG[i]==FL[i]==FS[i]
	count = 0
	record = []
	compare_ma = []
	for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
		if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
			continue
		record.append(cg==fl==fs)
		compare_ma.append(min(cg,fl,fs)>ma)
	pre=None
	for pos, b in enumerate(record):
		if pre is None:
			pre = b
			continue
		if b and pre==False:
			count += 1
			pre = b
			continue
		if not b and pos == len(record) -1:
			count+=1
			pre = b
			continue
		pre = b
	return count, sum([1 for r, cp in zip(record, compare_ma) if not r and cp])


def get_shape_kong_old(CG,FL,MA_Line,FS,B1,B2):
	#判断是不是首次出现的三色线
	#CG[i]==FL[i]==FS[i]
	count = 0
	record = []
	compare_ma = []
	for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
		if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
			continue
		record.append(not cg==fl==fs)
		compare_ma.append(min(cg,fl,fs)<ma)
	pre=None
	for pos, b in enumerate(record):
		if pre is None:
			pre = b
			continue
		if b and pre==False:
			count += 1
			pre = b
			continue
		if not b and pos == len(record) -1:
			count+=1
			pre = b
			continue
		pre = b
	return count, sum([1 for r, cp in zip(record, compare_ma) if not r and cp])


def order_callback(ContextInfo, orderInfo):
	print(orderInfo.m_strRemark, orderInfo.m_nOrderStatus, orderInfo.m_nOffsetFlag,orderInfo.m_dTradedPrice)
	if orderInfo.m_strRemark not in  ['期权策略开多'+g.remark,'期权策略开空'+g.remark,'策略平多'+g.remark,'策略平空'+g.remark]:
		return
	marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(orderInfo.m_strExchangeID, orderInfo.m_strExchangeID)
	k = orderInfo.m_strInstrumentID+'.'+marekt
	if k not in [g.call_one, g.put_one]:
		return
	#if orderInfo.m_nOrderStatus==56 and orderInfo.m_nOffsetFlag == 48 and orderInfo.m_dTradedPrice>0:
	#	g.open_price = round(orderInfo.m_dTradedPrice,1)
	#	g.hold_price = round(orderInfo.m_dTradedPrice,1)
	if orderInfo.m_nOrderStatus==56 and orderInfo.m_strRemark.startswith('期权策略开多'+g.remark):
		g.buy_long+=1
		log_trade(f"{g.code} 开多次数+1 {g.buy_long}", "ORDER")
		print(f"{g.code} 开多次数+1 {g.buy_long}")
	if orderInfo.m_nOrderStatus==56 and orderInfo.m_strRemark.startswith('期权策略开空'+g.remark):
		g.buy_short+=1
		log_trade(f"{g.code} 开空次数+1 {g.buy_short}", "ORDER")
		print(f"{g.code} 开空次数+1 {g.buy_short}")

	# 处理平仓订单
	if orderInfo.m_nOrderStatus==56 and (orderInfo.m_strRemark.startswith('策略平多'+g.remark) or orderInfo.m_strRemark.startswith('策略平空'+g.remark)):
		g.hold = 0
		g.hold_price = 0
		g.open_price = 0
		log_trade(f"平仓成功: {orderInfo.m_strRemark}, 持仓状态已重置", "ORDER")
		print(f"平仓成功: {orderInfo.m_strRemark}, 持仓状态已重置")

	if orderInfo.m_nOrderStatus==57 and orderInfo.m_nOffsetFlag == 48:
		g.hold = 0
		log_trade("order_callback set 0", "ORDER")
		print("order_callback set 0")

def orderError_callback(ContextInfo,passOrderInfo,msg):
	if '期权策略'+g.remark in passOrderInfo.strategyName:
		g.hold = 0
		print("orderError_callback set 0", msg)
	if '期权策略开空'+g.remark in passOrderInfo.strategyName:
		g.buy_short+=1
		print(f"{g.code} 开空次数+1 {g.buy_short}")
	if '期权策略开多'+g.remark in passOrderInfo.strategyName:
		g.buy_long+=1
		print(f"{g.code} 开多次数+1 {g.buy_long}")
	# 处理平仓错误
	if '策略平多'+g.remark in passOrderInfo.strategyName or '策略平空'+g.remark in passOrderInfo.strategyName:
		print(f"平仓失败: {passOrderInfo.strategyName}, 错误信息: {msg}")



def deal_callback(ContextInfo, dealInfo):
	print(f"deal callback m_nOffsetFlag:[{dealInfo.m_nOffsetFlag}] m_strRemark:[{dealInfo.m_strRemark}], [{dealInfo.m_strInstrumentID}] [{dealInfo.m_strExchangeID}]  [{dealInfo.m_dPrice}]",)
	if dealInfo.m_strRemark not in  ['期权策略开多'+g.remark,'期权策略开空'+g.remark,'策略平多'+g.remark,'策略平空'+g.remark,'止盈平多'+g.remark,'止盈平空'+g.remark,'止损平多'+g.remark,'止损平空'+g.remark]:
		return
	
	marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
	k = dealInfo.m_strInstrumentID+'.'+marekt
	print(k in [g.call_one, g.put_one],g.code.find(dealInfo.m_strInstrumentID))
	
	try:
		if dealInfo.m_nOffsetFlag == 48:  # 开仓
			print("deal callback", dealInfo.m_dPrice)
			g.open_price = round(dealInfo.m_dPrice, 4)
			g.hold_price = round(dealInfo.m_dPrice,4)
			log_trade(f"开仓成交 - 代码:{k}, 价格:{g.open_price}, 方向:{'多' if '开多' in dealInfo.m_strRemark else '空'}", "TRADE")
			
			# 计算持仓市值并判断是否为小资金账户
			position_value = g.open_price * hand * 10000  # 期权合约乘数一般为10000
			is_small_account = position_value < small_account_threshold
			if is_small_account:
				log_trade(f"开仓成交：小资金账户 - 持仓市值:{position_value}元，将在盈利{small_account_profit_ratio}%时触发止盈", "ACCOUNT")
			
		elif dealInfo.m_nOffsetFlag == 49:  # 平仓
			print(f"平仓成交: {dealInfo.m_strRemark}, 价格: {dealInfo.m_dPrice}")
			# 计算平仓盈亏
			if g.open_price > 0:
				profit_ratio = round((dealInfo.m_dPrice/g.open_price-1)*100,2)
				log_trade(f"平仓盈亏 - 代码:{k}, 开仓价:{g.open_price}, 平仓价:{dealInfo.m_dPrice}, 盈亏比例:{profit_ratio}%", "PROFIT")
				
				# 记录平仓原因
				if '止盈平' in dealInfo.m_strRemark:
					position_value = g.open_price * hand * 10000
					is_small_account = position_value < small_account_threshold
					if is_small_account and profit_ratio >= small_account_profit_ratio:
						log_trade(f"小资金账户止盈平仓成功 - 市值:{position_value}元, 盈利:{profit_ratio}%", "ACCOUNT")
			
			g.hold = 0
			g.hold_price = 0
			g.open_price = 0
	except Exception as e:
		log_trade(f"处理成交回调时出错: {e}", "ERROR")

def REF(S, N=1):          #对序列整体下移动N,返回序列(shift后会产生NAN)    
    return pd.Series(S).shift(N).values  
def SMA(S, N, M=1):       #中国式的SMA,至少需要120周期才精确 (雪球180周期)    alpha=1/(1+com)    
    return pd.Series(S).ewm(alpha=M/N,adjust=False).mean().values           #com=N-M/M
def SUM(S, N):            #对序列求N天累计和，返回序列    N=0对序列所有依次求和         
    return pd.Series(S).rolling(N).sum().values if N>0 else pd.Series(S).cumsum().values  
def HHV(S,N):             #HHV(C, 5) 最近5天收盘最高价        
    return pd.Series(S).rolling(N).max().values     

def LLV(S,N):             #LLV(C, 5) 最近5天收盘最低价     
    return pd.Series(S).rolling(N).min().values    
    
def HHVBARS(S,N):         #求N周期内S最高值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]),raw=True).values 

def LLVBARS(S,N):         #求N周期内S最低值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]),raw=True).values    
  
def MA(S,N):              #求序列的N日简单移动平均值，返回序列                    
    return pd.Series(S).rolling(N).mean().values  
def EMA(S,N):             #指数移动平均,为了精度 S>4*N  EMA至少需要120周期     alpha=2/(span+1)    
    return pd.Series(S).ewm(span=N, adjust=False).mean().values     

def EMA(source, N:int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    """
    # if not isinstance(source, np.ndarray):
    #     source = np.array(source)
    M = 2
    if N<M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N-1
    M = 2
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M*x+d*temp)/(N+1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
	index = S.index
	result_list = []
	slist = list(S)

	for i in range(len(S.index)):
		l = slist[max(0, i+1-34):i+1]
		result_list.append(min(l))
	return pd.Series(result_list, index=index)
	
	
def PyHHV(S, N):
	index = S.index
	result_list = []
	slist = list(S)

	for i in range(len(S.index)):
		l = slist[max(0, i+1-34):i+1]
		result_list.append(max(l))
	return pd.Series(result_list, index=index)

def cal_vba(ContextInfo, price):
	C = CLOSE = price['close']
	HIGH = price['close']
	LOW = price['close']
	AMT = price['amount']
	VOL = price['volume']
	MA_Line = SUM(C*VOL,0)/SUM(VOL, 0)
	CG=MA(C,21)
	FL=HHV(CG,3)
	FS=CG-(FL-CG)
	
	VA6=(2*CLOSE+HIGH+LOW)/4
	VA8=LLV(LOW,34)
	VARB=HHV(HIGH,34)
	VARC=EMA((VA6-VA8)/(VARB-VA8)*100,13)
	VARD=EMA(0.667*REF(VARC,1)+0.333*VARC,2)
	生命线:EMA(VARD,10)

	VAR1=HHV(HIGH,9)-LLV(LOW,9)
	VAR2=HHV(HIGH,9)-CLOSE
	VAR3=CLOSE-LLV(LOW,9)
	VAR4=((VAR2)/(VAR1))*(100)-70
	VAR5=((CLOSE-LLV(LOW,60))/(HHV(HIGH,60)-LLV(LOW,60)))*(100)


	VAR6=((2)*(CLOSE)+HIGH+LOW)/(4)
	index = VAR6.index
	VAR6 = pd.Series([v for v in VAR6],index=index)
	VAR7=SMA(((VAR3)/(VAR1))*(100),3,1)
	VAR8=PyLLV(CLOSE,min(34, len(LOW)))
	VAR9=SMA(VAR7,3,1)-SMA(VAR4,9,1)
	VAR10 = pd.Series([v-100 if v>100 else 0 for v in VAR9])
	VAR11=PyHHV(CLOSE,min(34, len(HIGH)))
	vv = ((VAR6-VAR8)/(VAR11-VAR8))*(100)
	vv=vv.fillna(0)
	vv=vv.replace([np.inf, -np.inf], np.nan).fillna(0)
	B1=EMA(vv,8)
	B2=EMA(B1,5)
	print(VAR6[-1],VAR11[-1],VAR8[-1])
	return CG,FL,MA_Line,FS,B1,B2


def get_option_real_one(ContextInfo):
	call_one = put_one = None
	now = time.strftime("%Y%m%d")
	call_list = get_current_month_option(ContextInfo, g.undl_code, now,'CALL')

	# call_list = get_current_month_option(ContextInfo, g.undl_code, '20231011','PUT')
	# print(len(call_list),call_list)
	# print([ContextInfo.get_instrumentdetail(s)['InstrumentName']for s in call_list])
	undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
	#实值实值认购一档
	call_dict = {call:ContextInfo.get_option_detail_data(call)['OptExercisePrice'] for call in call_list}
	real_list = {code for code in call_dict if call_dict[code]<undl_price }
	print("购实值 所有",real_list)
	real_list = sorted(real_list,key=lambda code:call_dict[code], reverse=True) #小于标的现价的行权价最大的票
	if real_list:
		print("购实一", real_list[0])
		call_one = real_list[0]

	call_list = get_current_month_option(ContextInfo, g.undl_code, now,'PUT')
	print(call_list)
	undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
	#市值认沽一档
	call_dict = {call:ContextInfo.get_option_detail_data(call)['OptExercisePrice'] for call in call_list}
	real_list = {code for code in call_dict if call_dict[code]>undl_price }
	print("沽 实值 所有",real_list)
	real_list = sorted(real_list,key=lambda code:call_dict[code], reverse=False) #小于标的现价的行权价最大的票
	if real_list:
		print("沽实一", real_list[0])
		put_one = real_list[0]
	return call_one, put_one


def get_current_month_option(ContextInfo,object, dedate, opttype="",):
	#dedate 日期 %Y%m%d 
	#获取截止到ddate这天还未到行权日的当月期权合约
	isavailavle = True
	result = []
	opt_by_month = {}
	undlMarket = "";
	undlCode = "";
	marketcodeList = object.split('.');
	if(len(marketcodeList) !=2):
		return [];
	undlCode = marketcodeList[0]
	undlMarket = marketcodeList[1];
	market = ""
	if(undlMarket == "SH"):
		if undlCode == "000016" or undlCode == "000300" or undlCode == "000852" or undlCode == "000905":
			market = 'IF'
		else:
			market = "SHO"
	elif(undlMarket == "SZ"):
		market = "SZO";
	if(opttype.upper() == "C"):
		opttype = "CALL"
	elif(opttype.upper() == "P"):
		opttype = "PUT"
	optList = []
	if market == 'SHO':
		optList += get_stock_list_in_sector('上证期权')
		
	elif market == 'SZO':
		optList += get_stock_list_in_sector('深证期权')
		
	elif market == 'IF':
		optList += get_stock_list_in_sector('中金所')
	for opt in optList:
		if(opt.find(market) < 0):
			continue
		inst = ContextInfo.get_option_detail_data(opt)
		if('optType' not in inst):
			continue
		endDate = inst['EndDelivDate']
		if( isavailavle and  str(endDate) <= dedate):
			continue
		if(opttype.upper()  != "" and  opttype.upper() != inst["optType"]):
			continue
		if 1: # option is trade,guosen demand
			createDate = inst['OpenDate'];
			openDate = inst['OpenDate'];
			if(createDate >= 1):
				openDate = min(openDate,createDate);
			if(openDate < 20150101 or str(openDate) > dedate):
				continue
		if(inst['ProductID'].find(undlCode) > 0 or inst['OptUndlCode'] == undlCode):
			result.append(opt)
			#print('append')
			#print(opt, inst)
			month = str(endDate)[:6]
			if month not in opt_by_month:
				opt_by_month[month] = [opt]
			else:
				opt_by_month[month].append(opt)
	opt_list = sorted(opt_by_month,reverse=False)
	print(opt_by_month.keys())
	for opt in opt_by_month:
		if opt_list:
			b = datetime.strptime(opt_list[0],'%Y%m')
			a = datetime.now()
			if (b-a).days<15:
				return opt_by_month[opt_list[0]]
			else:
				print(object,'未找到期权',opt_list[0],opt_by_month)
				return []
	else:
		return []

def calculate_red_white_lines_exact(price_data, debug_mode=False):
    """
    计算红白线指标
    
    参数:
    price_data: DataFrame 包含价格数据的DataFrame，需要包含close,high,low列
    debug_mode: bool 是否输出调试信息
    
    返回:
    white_line: Series 白线数据
    red_line: Series 红线数据
    """
    try:
        # 获取必要的价格数据
        close = price_data['close']
        high = price_data['high'] if 'high' in price_data else price_data['close']
        low = price_data['low'] if 'low' in price_data else price_data['close']
        
        # 计算基础指标
        hl2 = (high + low) / 2
        hlc3 = (high + low + close) / 3
        
        # 计算红白线
        white_line = EMA(hlc3, 13)
        red_line = EMA(white_line, 8)
        
        # 转换为pandas Series
        white_line = pd.Series(white_line, index=price_data.index)
        red_line = pd.Series(red_line, index=price_data.index)
        
        if debug_mode:
            print("红白线计算成功")
            
        return white_line, red_line
    except Exception as e:
        print(f"计算红白线时出错: {e}")
        # 返回空Series
        empty = pd.Series([], index=price_data.index)
        return empty, empty

def check_trend(price_data, ma_value, lookback=5):
    """
    判断价格趋势
    
    参数:
    price_data: DataFrame 价格数据
    ma_value: float 均线值
    lookback: int 回看的K线数量
    
    返回:
    int: 1=上升趋势, -1=下降趋势, 0=盘整
    """
    try:
        # 获取收盘价
        close = price_data['close']
        
        # 计算最近几根K线的趋势
        recent_closes = close[-lookback:]
        
        # 如果大部分收盘价高于均线，认为是上升趋势
        above_ma_count = sum(recent_closes > ma_value)
        below_ma_count = sum(recent_closes < ma_value)
        
        # 计算收盘价的方向
        price_direction = 1 if close.iloc[-1] > close.iloc[-lookback] else -1
        
        # 综合判断趋势
        if above_ma_count > below_ma_count and price_direction > 0:
            return 1  # 上升趋势
        elif below_ma_count > above_ma_count and price_direction < 0:
            return -1  # 下降趋势
        else:
            return 0  # 盘整
    except Exception as e:
        print(f"判断趋势时出错: {e}")
        return 0  # 默认为盘整

def detect_line_transition(prev_white, curr_white, prev_red, curr_red):
    """
    检测红白线转换信号
    
    参数:
    prev_white: float 前一个时间点的白线值
    curr_white: float 当前时间点的白线值
    prev_red: float 前一个时间点的红线值
    curr_red: float 当前时间点的红线值
    
    返回:
    str: "white_to_red"=白线转红线, "red_to_white"=红线转白线, None=无转换
    """
    try:
        # 检查参数是否有效
        if prev_white is None or curr_white is None or prev_red is None or curr_red is None:
            return None
        
        if pd.isna(prev_white) or pd.isna(curr_white) or pd.isna(prev_red) or pd.isna(curr_red):
            return None
        
        # 白线转红线：之前白线在红线下方，现在白线在红线上方
        if prev_white < prev_red and curr_white > curr_red:
            return "white_to_red"
        
        # 红线转白线：之前红线在白线下方，现在红线在白线上方
        if prev_red < prev_white and curr_red > curr_white:
            return "red_to_white"
        
        return None
    except Exception as e:
        print(f"检测线转换时出错: {e}")
        return None

def check_break_red_line(price_data, red_line_prev, red_line_curr):
    """
    检查价格是否跌破红线
    
    参数:
    price_data: DataFrame 价格数据
    red_line_prev: float 前一个时间点的红线值
    red_line_curr: float 当前时间点的红线值
    
    返回:
    tuple: (是否跌破红线, 跌破幅度)
    """
    try:
        # 获取最近的收盘价
        close = price_data['close'].iloc[-1]
        
        # 如果红线不存在，无法判断
        if red_line_curr is None or pd.isna(red_line_curr):
            return False, 0
        
        # 计算价格与红线的比率
        price_red_ratio = (close / red_line_curr - 1) * 100
        
        # 判断是否跌破红线
        if close < red_line_curr:
            # 如果前一个红线值存在，判断是否是新跌破
            if red_line_prev is not None and not pd.isna(red_line_prev):
                prev_close = price_data['close'].iloc[-2]
                was_above = prev_close > red_line_prev
                
                if was_above:
                    return True, price_red_ratio  # 新跌破
                else:
                    return False, price_red_ratio  # 持续在红线下方
            else:
                # 无法判断是否是新跌破，但价格确实在红线下方
                return True, price_red_ratio
        else:
            # 价格在红线上方
            return False, price_red_ratio
    except Exception as e:
        print(f"检查跌破红线时出错: {e}")
        return False, 0

def calculate_ma(close, volume, period):
    """
    计算成交量加权移动平均线
    
    参数:
    close: Series 收盘价序列
    volume: Series 成交量序列
    period: int 周期
    
    返回:
    Series: 成交量加权移动平均线
    """
    try:
        # 确保输入数据有效
        if len(close) != len(volume):
            print("收盘价和成交量长度不匹配")
            return pd.Series(close)  # 返回原始收盘价作为备用
        
        # 计算成交量加权移动平均线
        weighted_price = close * volume
        ma = pd.Series(SUM(weighted_price, period) / SUM(volume, period), index=close.index)
        
        return ma
    except Exception as e:
        print(f"计算成交量加权移动平均线时出错: {e}")
        return pd.Series(close)  # 返回原始收盘价作为备用

def check_volume_increase(volume_data, threshold_base=1.2):
    """
    检查成交量是否放大
    
    参数:
    volume_data: Series 成交量数据
    threshold_base: float 成交量放大阈值基准
    
    返回:
    bool: 成交量是否放大
    """
    try:
        # 获取最近的成交量数据
        recent_volume = volume_data.iloc[-5:]
        
        # 计算平均成交量
        avg_volume = recent_volume.iloc[:-1].mean()
        
        # 获取最新成交量
        latest_volume = recent_volume.iloc[-1]
        
        # 判断成交量是否放大
        return latest_volume > avg_volume * threshold_base
    except Exception as e:
        print(f"检查成交量放大时出错: {e}")
        return False

def is_valid_option_code(ContextInfo, option_code):
    """
    检查期权代码是否有效
    
    参数:
    ContextInfo: 上下文信息对象
    option_code: str 期权代码
    
    返回:
    bool: 是否为有效的期权代码
    """
    try:
        if option_code is None or option_code == '':
            return False
            
        # 尝试获取期权详情
        option_detail = ContextInfo.get_option_detail_data(option_code)
        return 'optType' in option_detail
    except Exception as e:
        print(f"检查期权代码有效性时出错: {e}")
        return False

def calculate_dynamic_stoploss(ContextInfo, option_code, base_stoploss=None):
    """
    根据期权隐含波动率动态调整止损比例
    
    参数:
    ContextInfo: 上下文信息对象
    option_code: str 期权代码
    base_stoploss: float 基础止损比例，如果为None则使用全局的stoploss_ratio
    
    返回:
    float: 调整后的止损比例
    """
    try:
        # 使用全局止损比例作为默认值
        if base_stoploss is None:
            base_stoploss = stoploss_ratio
            
        # 检查期权代码是否有效
        if not is_valid_option_code(ContextInfo, option_code):
            print(f"无效的期权代码: {option_code}，使用默认止损比例: {base_stoploss}")
            return base_stoploss
            
        # 获取期权隐含波动率
        option_detail = ContextInfo.get_option_detail_data(option_code)
        implied_vol = option_detail.get('ImpliedVol', 0.3)  # 默认波动率30%
        
        # 根据波动率调整止损比例
        adjusted_stoploss = base_stoploss * (0.3 / max(implied_vol, 0.1))
        
        # 限制在合理范围内
        final_stoploss = max(min(adjusted_stoploss, -15), -35)
        
        # 打印调试信息
        print(f"动态止损计算 - 期权:{option_code}, 隐含波动率:{implied_vol:.2f}, 基础止损:{base_stoploss}, 调整后:{adjusted_stoploss:.2f}, 最终值:{final_stoploss:.2f}")
        
        return final_stoploss
    except Exception as e:
        print(f"计算动态止损时出错: {e}")
        return base_stoploss  # 发生错误时返回基础止损比例