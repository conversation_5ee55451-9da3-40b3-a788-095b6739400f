#encoding:gbk
交易北交所='是' #否

# 策略关键参数设置
选股开始时间 = '093000'  # 格式为'HHMMSS'，改为9:30开盘后立即选股
选股检查间隔 = ['00', '10', '20', '30', '40', '50']  # 每10分钟检查一次
尾盘选股时间 = '145000'  # 尾盘额外选股时间点
使用趋势判断 = False  # 是否将趋势判断作为筛选条件，False=不使用，True=使用
检查均线距离 = False  # 是否将价格与均线距离作为筛选条件，False=不使用，True=使用
检查成交量放大 = False  # 是否检查成交量放大条件，False=不使用，True=使用
检查价格均线关系 = True  # 是否要求价格必须高于均线，False=不要求，True=要求
使用红线止损 = True  # 是否使用红线支撑止损，False=不使用，True=使用
使用动态红线止损 = True  # 是否使用动态红线止损阈值，False=使用固定阈值，True=使用动态阈值
使用均线止损 = True  # 是否使用均线支撑止损，False=不使用，True=使用
使用总体止损 = True  # 是否使用总体亏损止损，False=不使用，True=使用
使用个股止损 = True  # 是否使用个股亏损止损，False=不使用，True=使用
使用回撤止损 = True  # 是否使用最高点回撤止损，False=不使用，True=使用
检查市值条件 = True  # 是否检查市值条件，False=不使用，True=使用
市值上限 = 300  # 市值上限（亿元）

import math
import time
import traceback
import numpy as np
import pandas as pd
import datetime
import os

# 账户设置 - 确保这些变量在全局范围内定义
account = "********"  # 请替换为您的实际账户ID
accountType = "STOCK"  # 股票账户类型

# 获取股票价格的函数，定义在全局范围内
def get_price(C, s):
    try:
        full = C.get_full_tick([s])
        if s in full and 'lastPrice' in full[s]:
            return full[s]['lastPrice']*1.01
        else:
            print(f"警告: 无法获取{s}的实时价格")
            return 0
    except Exception as e:
        print(f"获取股票{s}价格时出错: {str(e)}")
        return 0

# 禁用DataFrame切片赋值警告
pd.options.mode.chained_assignment = None

class G():
    pass


g = G()
g.price = {}
g.select= False
g.sell = []
g.max_profit = {}
g.last_trading_day = ""  # 记录上一个交易日
g.bought_stocks = set()  # 记录当天已买入的股票
g.log_file = "trading_log.txt"  # 日志文件名
g.holding_days = {}  # 记录每只股票的持有天数
g.entry_prices = {}  # 记录每只股票的买入价格
g.stock_volatility = {}  # 记录每只股票的波动率
g.next_day_orders = []  # 存储次日需要执行的订单
g.signal_counts = {}  # 记录每只股票的连续买入信号次数
g.position_stage = {}  # 记录每只股票的建仓阶段 (1=第一批, 2=第二批, 3=第三批)
g.last_signal_time = {}  # 记录每只股票最近一次信号时间

# 添加配置类
class Config:
    # 红白线与价格距离阈值参数
    RED_LINE_DISTANCE_THRESHOLD = 0.05  # 做多时价格与红线最大偏离百分比，从0.55%调整为5%
    WHITE_LINE_DISTANCE_THRESHOLD = 0.0055  # 做空时价格与白线最大偏离百分比，默认0.55%
    
    # 均线参数(可调整)
    TREND_MA_PERIOD = 18  # 趋势判断均线周期，默认18日
    MA_DISTANCE_THRESHOLD = 0.005  # 价格与均线的最大偏离百分比，默认0.5%
    
    # 添加红白线转换强度参数
    PRICE_STRENGTH_THRESHOLD = 0.005  # 降低到0.5%，原来是0.8%
    VOLUME_STRENGTH_THRESHOLD = 1.2   # 成交量强度阈值（相对于前3根K线均值），从1.3降低到1.2
    CANDLE_BODY_RATIO = 0.5          # K线实体占比阈值，从0.6降低到0.5
    
    # 成交量参数
    VOLUME_THRESHOLD_LONG = 1.0  # 做多成交量阈值，从1.2调整为1.0
    
    # 涨幅阈值参数
    DAILY_CHANGE_THRESHOLD = 2.0  # 当日涨幅阈值，默认2%
    
    # 止损参数
    RED_LINE_STOP_LOSS_THRESHOLD = 0.01  # 价格跌破红线的百分比阈值，默认1%
    MA_STOP_LOSS_THRESHOLD = 0.01  # 价格跌破均线的百分比阈值，默认1%
    TOTAL_STOP_LOSS_THRESHOLD = 0.07  # 总体亏损止损阈值，默认7%
    STOCK_STOP_LOSS_THRESHOLD = 0.08  # 个股亏损止损阈值，默认8%
    MAX_DRAWDOWN_THRESHOLD = 0.05  # 从最高点回撤止损阈值，默认5%
    
    # 新增时间止损参数
    MAX_HOLDING_DAYS = 12  # 最大持有天数，超过则考虑清仓
    MAX_HOLDING_DAYS_NO_PROFIT = 8  # 无盈利情况下的最大持有天数
    TIME_STOP_LOSS_FIRST_BATCH = 0.5  # 时间止损第一批卖出比例
    
    # 新增动态回撤止损参数
    VOLATILITY_LOOKBACK = 20  # 计算波动率的回看天数
    VOLATILITY_ADJUSTMENT_FACTOR = 1.5  # 波动率调整因子
    MIN_DRAWDOWN_THRESHOLD = 0.03  # 最小回撤止损阈值
    MAX_DRAWDOWN_THRESHOLD = 0.08  # 最大回撤止损阈值
    
    # 止盈参数
    FIRST_TAKE_PROFIT_THRESHOLD = 0.01  # 第一批止盈阈值，修改为3%
    FIRST_TAKE_PROFIT_RATIO = 0.33  # 第一批止盈比例，默认1/3
    SECOND_TAKE_PROFIT_THRESHOLD = 0.05  # 第二批止盈阈值，修改为5%
    SECOND_TAKE_PROFIT_RATIO = 0.5  # 第二批止盈比例，默认一半
    THIRD_TAKE_PROFIT_THRESHOLD = 0.10  # 第三批止盈阈值，修改为10%
    
    # 分批建仓参数
    FIRST_POSITION_RATIO = 0.3  # 第一批建仓比例，默认30%
    SECOND_POSITION_RATIO = 0.3  # 第二批建仓比例，默认30%
    THIRD_POSITION_RATIO = 0.4  # 第三批建仓比例，默认40%
    POSITION_UPGRADE_DAYS = 2  # 连续信号天数触发加仓，默认2天
    SIGNAL_EXPIRE_HOURS = 24  # 信号有效期(小时)，超过这个时间不计入连续信号
    
    # 持仓资金阈值参数
    SMALL_POSITION_THRESHOLD = 10000  # 小额持仓阈值(元)，低于此值的持仓盈利达到阈值时全部平仓
    MEDIUM_POSITION_THRESHOLD = 50000  # 中等持仓阈值(元)，可用于多级止盈策略
    LARGE_POSITION_THRESHOLD = 100000  # 大额持仓阈值(元)，可用于多级止盈策略
    
    # 动态红线止损参数
    DYNAMIC_RED_LINE_VOLATILITY_DAYS = 10  # 计算股票波动率的天数
    DYNAMIC_RED_LINE_VOLATILITY_DIVISOR = 2.0  # 波动率除数，影响波动率因子
    DYNAMIC_RED_LINE_VOLATILITY_MIN = 0.8  # 波动率因子最小值
    DYNAMIC_RED_LINE_VOLATILITY_MAX = 3.0  # 波动率因子最大值
    
    DYNAMIC_RED_LINE_TIME_BASE = 0.8  # 时间因子基础值
    DYNAMIC_RED_LINE_TIME_INCREMENT = 0.05  # 每天增加的时间因子
    DYNAMIC_RED_LINE_TIME_MAX = 1.5  # 时间因子最大值
    
    DYNAMIC_RED_LINE_MARKET_HIGH_VOLATILITY = 2.0  # 高波动市场阈值
    DYNAMIC_RED_LINE_MARKET_LOW_VOLATILITY = 1.0  # 低波动市场阈值
    DYNAMIC_RED_LINE_MARKET_DIVISOR = 1.5  # 市场波动率除数
    DYNAMIC_RED_LINE_MARKET_HIGH_FACTOR = 1.5  # 高波动市场最大因子
    DYNAMIC_RED_LINE_MARKET_LOW_FACTOR = 0.9  # 低波动市场因子
    
    DYNAMIC_RED_LINE_PROFIT_HIGH = 0.05  # 高盈利阈值(5%)
    DYNAMIC_RED_LINE_LOSS_HIGH = -0.03  # 高亏损阈值(-3%)
    DYNAMIC_RED_LINE_PROFIT_FACTOR = 1.2  # 高盈利时的因子
    DYNAMIC_RED_LINE_LOSS_FACTOR = 0.8  # 高亏损时的因子
    
    DYNAMIC_RED_LINE_MIN_THRESHOLD = 0.005  # 最小红线止损阈值(0.5%)
    DYNAMIC_RED_LINE_MAX_THRESHOLD = 0.03  # 最大红线止损阈值(3%)
    
# 定义日志和工具函数
def log_message(message):
    """记录日志消息到文件和控制台"""
    timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
    log_entry = f"[{timestamp}] {message}"
    print(log_entry)
    
    try:
        with open(g.log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry + '\n')
    except Exception as e:
        print(f"写入日志文件出错: {str(e)}")

def update_stock_signals(stock_code):
    """更新单个股票的信号状态"""
    global stock_signals
    
    try:
        # 获取股票数据
        price_data = g.price.get(stock_code)
        if price_data is None or len(price_data) < 30:  # 需要足够的数据计算均线和趋势
            return 0  # 返回0表示无信号
        
        # 从价格字典获取成交量数据
        volume_data = None
        if 'volume' in price_data:
            volume_data = price_data['volume']
        
        # 如果成交量数据不存在或为空，尝试从估算数据中获取
        if volume_data is None or len(volume_data) == 0:
            if stock_code in g.estimated_volume_data:
                # 使用之前估算的成交量数据
                estimated_volume = g.estimated_volume_data[stock_code]
                # 创建一个与价格数据等长的成交量数据数组
                volume_data = [estimated_volume] * len(price_data['close'])
                # 添加一些随机波动以模拟真实成交量变化
                import random
                for i in range(1, len(volume_data)):
                    volume_data[i] = int(volume_data[i-1] * (0.9 + 0.2 * random.random()))
            else:
                # 尝试估算成交量数据
                estimated_volume = handle_missing_volume_data(stock_code, ContextInfo)
                if estimated_volume is not None:
                    # 存储估算的成交量数据以备后用
                    g.estimated_volume_data[stock_code] = estimated_volume
                    # 创建一个与价格数据等长的成交量数据数组
                    volume_data = [estimated_volume] * len(price_data['close'])
                    # 添加一些随机波动以模拟真实成交量变化
                    import random
                    for i in range(1, len(volume_data)):
                        volume_data[i] = int(volume_data[i-1] * (0.9 + 0.2 * random.random()))
                else:
                    # 如果无法估算成交量，使用一个默认值
                    volume_data = [10000000] * len(price_data['close'])  # 默认1000万成交量
        
        # 计算红白线
        white_line, red_line = calculate_red_white_lines_exact(price_data)
        
        # 计算18日均线
        ma18 = MA(price_data['close'], Config.TREND_MA_PERIOD)
        
        # 检查趋势
        trend = check_trend(price_data, ma18[-1] if len(ma18) > 0 else None)
        
        # 判断当前是否有信号
        current_price = price_data['close'][-1]
        
        # 更新信号计数
        last_signal_time = g.last_signal_time.get(stock_code)
        current_time = time.time()
        
        # 检查是否满足买入信号条件
        is_buy_signal = False
        
        # 检查是否有红线存在
        has_red = red_line is not None and len(red_line) > 0 and not pd.isna(red_line.iloc[-1])
        
        # 检查是否为白线转红线 (简单判断，实际逻辑在do_select更复杂)
        prev_white_exists = white_line is not None and len(white_line) > 1 and not pd.isna(white_line.iloc[-2])
        red_exists = red_line is not None and len(red_line) > 0 and not pd.isna(red_line.iloc[-1])
        is_white_to_red = prev_white_exists and red_exists
        
        # 当满足基本条件时视为买入信号
        if has_red and is_white_to_red:
            is_buy_signal = True
        
        # 更新信号计数
        if is_buy_signal:
            # 如果上次信号时间存在且未过期，增加计数
            if last_signal_time is not None and (current_time - last_signal_time) / 3600 < Config.SIGNAL_EXPIRE_HOURS:
                g.signal_counts[stock_code] = g.signal_counts.get(stock_code, 0) + 1
            else:
                # 否则重置为1
                g.signal_counts[stock_code] = 1
            
            # 更新信号时间
            g.last_signal_time[stock_code] = current_time
        else:
            # 如果超过有效期，重置信号计数
            if last_signal_time is None or (current_time - last_signal_time) / 3600 >= Config.SIGNAL_EXPIRE_HOURS:
                g.signal_counts[stock_code] = 0
        
        # 获取当前信号计数
        signal_count = g.signal_counts.get(stock_code, 0)
        
        # 添加或更新信号记录
        signal_status = {
            'price': current_price,
            'red_line': red_line.iloc[-1] if red_line is not None and len(red_line) > 0 and not pd.isna(red_line.iloc[-1]) else None,
            'white_line': white_line.iloc[-1] if white_line is not None and len(white_line) > 0 and not pd.isna(white_line.iloc[-1]) else None,
            'prev_white_line': white_line.iloc[-2] if white_line is not None and len(white_line) > 1 and not pd.isna(white_line.iloc[-2]) else None,
            'ma18': ma18[-1] if len(ma18) > 0 else None,
            'trend': trend,
            'volume': volume_data[-1] if volume_data is not None and len(volume_data) > 0 else None,
            'volume_history': volume_data[-4:-1] if volume_data is not None and len(volume_data) > 3 else None,
            'updated_time': current_time,
            'signal_count': signal_count,
            'is_buy_signal': is_buy_signal
        }
        
        # 存储信号状态
        stock_signals[stock_code] = signal_status
        
        return signal_count
        
    except Exception as e:
        if g.show_volume_warnings:
            log_message(f"更新股票 {stock_code} 信号出错: {str(e)}")
        return 0

def print_bought_stocks():
    """打印当天已买入的股票列表"""
    if g.bought_stocks:
        print("\n====== 当天已买入的股票 ======")
        for stock in g.bought_stocks:
            print(f"  {stock}")
    else:
        print("当天尚未买入任何股票")

def is_stock_in_position(ContextInfo, stock_code):
    try:
        positions = get_trade_detail_data(account, accountType, 'POSITION')
        print(f"持仓数据获取结果: {positions}")  # 添加日志，查看返回的持仓数据
        
        if not positions:
            print("持仓数据为空")
            return False
            
        for p in positions:
            position_code = p.m_strInstrumentID + '.' + p.m_strExchangeID
            print(f"检查持仓: {position_code}, 总量: {p.m_nVolume}, 可用量: {p.m_nCanUseVolume}")
            
            # 修改判断条件，使用总持仓量判断而不是可用量
            if position_code == stock_code and p.m_nVolume > 0:  # 使用总量判断
                return True
        return False
    except Exception as e:
        print(f"检查持仓时出错: {str(e)}")
        return False

def init(ContextInfo):
    # 初始化日志
    log_message("策略启动初始化")
    
    # 添加标志位控制是否显示成交量数据缺失警告
    g.show_volume_warnings = False  # 设置为False可以关闭"无法获取成交量数据"的警告
    
    # 添加存储估算成交量数据的字典
    g.estimated_volume_data = {}
    
    # 添加存储股票市值的字典
    g.stock_market_value = {}
    
    # 改为获取沪深300成分股
    g.stock_list = get_stock_list_in_sector('沪深A股') 
    print(f"初始股票池数量(沪深A股): {len(g.stock_list)}")
    
    # 添加过滤条件
    # 1. 剔除ST、退市等 - 添加错误处理
    stock_name = {}
    valid_stocks = []
    
    for s in g.stock_list:
        try:
            detail = ContextInfo.get_instrumentdetail(s)
            if detail is None or 'InstrumentName' not in detail or detail['InstrumentName'] is None:
                print(f"无法获取{s}的股票名称信息")
                continue
                
            name = detail['InstrumentName']
            stock_name[s] = name
            
            # 检查是否ST或退市
            if 'st' in name.lower() or name.endswith('退'):
                print(f"过滤ST/退市股票: {s} {name}")
                continue
                
            valid_stocks.append(s)
        except Exception as e:
            print(f"处理{s}的股票信息时出错: {str(e)}")
            continue
    
    g.stock_list = valid_stocks
    print(f"过滤ST后股票池数量: {len(g.stock_list)}")
    
    # 2. 可以添加流动性过滤，但不再将无法获取成交量数据的股票排除
    # 获取近20日平均成交量，添加错误处理
    volume_data = {}
    stocks_with_volume = []
    
    # 统计成交量数据缺失的股票数量
    missing_volume_count = 0
    estimated_volume_count = 0
    missing_volume_stocks = []  # 记录缺失成交量的股票
    
    for s in g.stock_list:
        try:
            data = ContextInfo.get_market_data_ex_ori(['volume'], [s], period='1d', count=20)
            if 'volume' in data and len(data['volume']) > 0:
                volume_data[s] = data['volume'].mean()
                # 只有成功获取到成交量的股票才加入
                if volume_data[s] > 5000000:  # 500万成交量阈值
                    stocks_with_volume.append(s)
            else:
                # 尝试估算成交量数据
                estimated_volume = handle_missing_volume_data(s, ContextInfo)
                if estimated_volume is not None:
                    # 存储估算的成交量数据
                    g.estimated_volume_data[s] = estimated_volume
                    estimated_volume_count += 1
                    
                    # 将股票添加到具有成交量的股票列表
                    stocks_with_volume.append(s)
                
                # 记录缺失成交量的股票
                missing_volume_count += 1
                missing_volume_stocks.append(s)
                
                # 即使无法获取成交量数据，也将股票保留在池中
                if s not in stocks_with_volume:
                    stocks_with_volume.append(s)
                    
        except Exception as e:
            if g.show_volume_warnings:
                print(f"处理{s}的成交量数据时出错: {str(e)}")
            # 出错时也将股票保留在池中
            stocks_with_volume.append(s)
    
    # 显示汇总信息而不是每个股票的警告
    if missing_volume_count > 0:
        print(f"共有{missing_volume_count}只股票无法获取成交量数据，已成功估算{estimated_volume_count}只")
        # 如果需要，可以显示部分缺失成交量的股票
        if g.show_volume_warnings and len(missing_volume_stocks) > 0:
            display_count = min(5, len(missing_volume_stocks))
            print(f"部分缺失成交量的股票示例: {', '.join(missing_volume_stocks[:display_count])}...")
    
    # 过滤掉成交量过小的股票，但保留无法获取成交量数据的股票
    g.stock_list = stocks_with_volume
    
    # 获取股票市值信息
    if 检查市值条件:
        get_stock_market_value(ContextInfo, g.stock_list)
    
    print(f"最终股票池数量: {len(g.stock_list)}")
    
    # 初始化全局变量
    g.price = {}
    g.select = False  # 每次策略启动时重置选股标志
    g.sell = []
    g.max_profit = {}
    g.last_trading_day = ""  # 重置上一个交易日记录
    g.bought_stocks = set()  # 重置当天已买入的股票集合
    g.holding_days = {}  # 重置持有天数记录
    g.entry_prices = {}  # 重置买入价格记录
    g.stock_volatility = {}  # 重置股票波动率记录
    g.signal_counts = {}  # 重置信号计数
    g.position_stage = {}  # 重置建仓阶段
    g.last_signal_time = {}  # 重置信号时间
    
    ContextInfo.run_time("myHandlebar","3nSecond","2019-10-14 13:20:00")

def get_stock_market_value(ContextInfo, stock_list):
    """获取股票的市值信息"""
    print("开始获取股票市值信息...")
    count = 0
    for s in stock_list:
        try:
            # 获取股票详情信息
            detail = ContextInfo.get_instrumentdetail(s)
            
            if detail and 'InstrumentID' in detail:
                # 使用基于股票代码特征的估算值计算市值
                # 沪深300成分股一般为大盘股
                total_shares = 0
                if s.startswith('60'):  # 上证主板大盘股
                    total_shares = 5000000000  # 50亿股
                elif s.startswith('00'):  # 深证主板
                    total_shares = 3000000000  # 30亿股
                elif s.startswith('30'):  # 创业板
                    total_shares = 1000000000  # 10亿股
                elif s.startswith('68'):  # 科创板
                    total_shares = 800000000  # 8亿股
                else:
                    total_shares = 2000000000  # 20亿股默认值
                
                # 获取最新价格
                last_price = 0
                if 'LastPrice' in detail and detail['LastPrice'] > 0:
                    last_price = detail['LastPrice']
                else:
                    # 尝试从行情获取最新价格
                    try:
                        full_tick = ContextInfo.get_full_tick([s])
                        if s in full_tick and 'lastPrice' in full_tick[s]:
                            last_price = full_tick[s]['lastPrice']
                    except:
                        # 如果获取不到价格，使用默认值
                        last_price = 20  # 默认20元
                
                # 计算市值（亿元）
                market_value = (total_shares * last_price) / 100000000
                
                # 确保市值是一个合理的值
                if market_value <= 0 or market_value > 10000:  # 超过1万亿或小于0都不合理
                    market_value = 500  # 使用一个中等市值
                
                # 存储市值信息
                g.stock_market_value[s] = market_value
                
                count += 1
                if count % 20 == 0:
                    print(f"已获取{count}只股票的市值信息")
            else:
                # 如果无法获取股票信息，设置一个默认市值
                g.stock_market_value[s] = 250  # 默认250亿
                
        except Exception as e:
            print(f"获取{s}市值信息出错: {str(e)}")
            # 出错时设置一个默认值
            g.stock_market_value[s] = 250  # 默认250亿
    
    print(f"完成获取{count}只股票的市值信息")

# 实际实现中，可能需要通过其他API或数据源获取准确的市值信息
def get_market_value(ContextInfo, stock_code):
    """获取单个股票的市值（亿元）"""
    try:
        # 首先检查是否已有缓存的市值数据
        if hasattr(g, 'stock_market_value') and stock_code in g.stock_market_value:
            return g.stock_market_value[stock_code]
        
        # 否则尝试获取股票信息计算市值
        detail = ContextInfo.get_instrumentdetail(stock_code)
        
        if detail and 'InstrumentID' in detail:
            # 使用与上面相同的估算方法
            # 根据股票代码特征估算总股本
            total_shares = 0
            if stock_code.startswith('60'):  # 上证主板
                total_shares = 5000000000  # 50亿股
            elif stock_code.startswith('00'):  # 深证主板
                total_shares = 3000000000  # 30亿股
            elif stock_code.startswith('30'):  # 创业板
                total_shares = 1000000000  # 10亿股
            elif stock_code.startswith('68'):  # 科创板
                total_shares = 800000000  # 8亿股
            else:
                total_shares = 2000000000  # 20亿股默认值
            
            # 获取最新价格
            last_price = 0
            if 'LastPrice' in detail and detail['LastPrice'] > 0:
                last_price = detail['LastPrice']
            else:
                # 尝试从行情获取最新价格
                try:
                    full_tick = ContextInfo.get_full_tick([stock_code])
                    if stock_code in full_tick and 'lastPrice' in full_tick[stock_code]:
                        last_price = full_tick[stock_code]['lastPrice']
                except:
                    # 如果获取不到价格，使用默认值
                    last_price = 20  # 默认20元
            
            # 计算市值（亿元）
            market_value = (total_shares * last_price) / 100000000
            
            # 确保市值是一个合理的值
            if market_value <= 0 or market_value > 10000:  # 超过1万亿或小于0都不合理
                market_value = 250  # 使用一个中等市值
            
            # 缓存结果
            if hasattr(g, 'stock_market_value'):
                g.stock_market_value[stock_code] = market_value
                
            return market_value
        else:
            return 250  # 默认返回250亿
    except Exception as e:
        print(f"获取{stock_code}市值出错: {str(e)}")
        return 250  # 出错时返回默认值

def myHandlebar(ContextInfo):
    # 初始化返回变量
    stocks = []
    ma18_dict = {}
    
    # 测试下单功能（禁用）
    test_order = False  # 设置为False禁用测试
    if test_order:
        log_message("执行下单功能测试")
        try:
            # 执行测试函数
            test_order_functionality(ContextInfo)
        except Exception as e:
            log_message(f"执行下单测试时出错: {str(e)}")
    
    if not g.price:
        # 获取日线历史数据，策略基于日线周期运行
        try:
            # 只有当股票池不为空时才尝试获取价格数据
            if len(g.stock_list) > 0:
                g.price = ContextInfo.get_market_data_ex_ori(['close', 'open', 'high', 'low', 'volume'],g.stock_list,period='1d',fill_data=False, subscribe=False)
                print(f"成功获取{len(g.price)}只股票的价格数据")
            else:
                print("警告: 股票池为空，无法获取价格数据")
                g.price = {}  # 确保g.price被初始化为空字典
        except Exception as e:
            print(f"获取价格数据时出错: {str(e)}")
            g.price = {}  # 错误时也确保g.price被初始化
    
    now = time.strftime('%H%M%S')
    today = time.strftime('%Y%m%d')
    print(f"\n【{time.strftime('%Y-%m-%d %H:%M:%S')}】 策略运行")
    
    # 打印当天已买入的股票
    print_bought_stocks()
    
    # 同步持仓状态，确保系统状态与实际持仓一致
    current_positions = sync_position_status(ContextInfo)
    
    # 检查是否是新的交易日，如果是则重置选股标志
    if today != g.last_trading_day:
        print(f"检测到新交易日: {today}, 上一交易日: {g.last_trading_day}")
        log_message(f"检测到新交易日: {today}, 上一交易日: {g.last_trading_day}")
        g.select = False
        
        # 新的交易日，更新持有天数
        if g.last_trading_day != "":  # 确保不是第一次运行
            for stock in list(g.holding_days.keys()):
                g.holding_days[stock] += 1
                
        g.last_trading_day = today
        g.bought_stocks = set()  # 重置当天已买入股票的集合
    
    # 修改交易模式为实盘交易模式
    is_simulation = False  # 默认为实盘模式
    
    # 只在实盘模式下检查交易时间
    if not is_simulation and not '093000'<= now<='150000':
        print("实盘模式：非交易时间")
        return stocks, ma18_dict
    
    # 优化选股检查触发条件
    is_check_time = False
    
    # 1. 开盘时刻检查 - 刚开盘就检查一次
    if 选股开始时间 <= now <= '093100':
        is_check_time = True
    
    # 2. 定时检查 - 按照设定的间隔时间检查
    elif now[-2:] in 选股检查间隔:
        is_check_time = True
    
    # 3. 尾盘检查 - 收盘前特别检查
    elif 尾盘选股时间 <= now <= '145100':
        is_check_time = True
    
    # 执行选股检查
    if is_check_time:
        print(f"\n【{time.strftime('%Y-%m-%d %H:%M:%S')}】 ====== 检查股票池开仓条件 ======")
        # 检查g.price是否为空
        if not g.price:
            print("价格数据为空，无法进行选股")
            stocks = []
            ma18_dict = {}  # 确保ma18_dict被初始化
        else:
            stocks, ma18_dict = do_select(ContextInfo, True)
        
        if stocks:
            print(f"符合条件的股票: {len(stocks)}只 - {stocks}")
            # 执行买入操作
            for stock_code in stocks:
                try:
                    # 获取股票实时行情
                    tick_data = ContextInfo.get_full_tick([stock_code])
                    if stock_code not in tick_data:
                        print(f"无法获取{stock_code}的实时行情，跳过买入")
                        continue
                        
                    current_price = tick_data[stock_code]['lastPrice']
                    
                    # 获取账户可用资金
                    accounts = get_trade_detail_data(account, accountType, 'account')
                    if not accounts:
                        print("账号错误或账号掉线，跳过买入")
                        continue
                        
                    available_cash = accounts[0].m_dAvailable
                    
                    # 计算买入数量（根据资金情况决定买入比例）
                    if available_cash <= 10000:
                        # 资金不足10000元，全仓买入
                        buy_ratio = 1.0  # 100%
                        print(f"资金不足10000元（当前{available_cash:.2f}元），使用全仓买入")
                    else:
                        # 资金大于10000元，按30%买入
                        buy_ratio = Config.FIRST_POSITION_RATIO  # 默认为30%
                        print(f"资金大于10000元（当前{available_cash:.2f}元），使用{buy_ratio*100}%资金买入")
                    
                    buy_amount = available_cash * buy_ratio
                    quantity = int(buy_amount / current_price / 100) * 100  # 买入数量，取整百
                    
                    if quantity < 100:
                        print(f"{stock_code}计算的买入数量不足100股，跳过买入")
                        continue
                    
                    # 执行买入
                    print(f"准备买入 {stock_code}，价格={current_price}，数量={quantity}")
                    # 上下文信息使用ContextInfo对象而不是空字符串
                    stock_code_without_suffix = normalize_stock_code(stock_code)
                    print(f"处理后的股票代码: {stock_code} -> {stock_code_without_suffix}")
                    log_message(f"处理后的股票代码: {stock_code} -> {stock_code_without_suffix}")
                    
                    # 添加详细调试信息
                    log_message(f"买入参数详情: 账户={account}, 代码={stock_code_without_suffix}, 类型=0, 价格={current_price}, 数量={quantity}")
                    try:
                        passorder(23, 1101, account, stock_code_without_suffix, 0, current_price, quantity, '', 2, '', ContextInfo)
                        log_message(f"买入下单成功: {stock_code}, 价格: {current_price}, 数量: {quantity}")
                    except Exception as e:
                        log_message(f"买入下单失败: {stock_code}, 错误: {str(e)}")
                        print(f"买入下单失败: {stock_code}, 错误: {str(e)}")
                    
                    # 记录买入信息
                    log_message(f"买入股票: {stock_code}, 价格: {current_price}, 数量: {quantity}")
                    record_new_position(stock_code, current_price, quantity)
                    
                    # 将股票添加到已买入集合
                    g.bought_stocks.add(stock_code)
                    
                except Exception as e:
                    print(f"买入{stock_code}时出错: {str(e)}")
    
    return stocks, ma18_dict

def test_order_functionality(ContextInfo):
    """
    测试下单功能是否正常工作
    """
    log_message("开始测试下单功能")
    
    # 测试股票代码
    test_stock_code = "000001.SZ"  # 平安银行
    
    # 获取当前价格
    try:
        full_tick = ContextInfo.get_full_tick([test_stock_code])
        if test_stock_code in full_tick and 'lastPrice' in full_tick[test_stock_code]:
            current_price = full_tick[test_stock_code]['lastPrice']
        else:
            current_price = 10.0  # 默认价格
            log_message(f"无法获取{test_stock_code}价格，使用默认价格{current_price}")
    except Exception as e:
        current_price = 10.0  # 默认价格
        log_message(f"获取{test_stock_code}价格时出错: {str(e)}，使用默认价格{current_price}")
    
    # 测试买入
    log_message(f"测试买入: {test_stock_code}")
    stock_code_without_suffix = normalize_stock_code(test_stock_code)
    log_message(f"处理后的股票代码: {test_stock_code} -> {stock_code_without_suffix}")
    
    # 测试买入数量
    quantity = 100  # 1手
    
    # 详细记录参数
    log_message(f"买入参数详情: 账户={account}, 代码={stock_code_without_suffix}, 类型=0, 价格={current_price}, 数量={quantity}")
    
    # 尝试买入
    try:
        passorder(23, 1101, account, stock_code_without_suffix, 0, current_price, quantity, '', 2, '', ContextInfo)
        log_message(f"测试买入下单成功: {test_stock_code}, 价格: {current_price}, 数量: {quantity}")
    except Exception as e:
        log_message(f"测试买入下单失败: {test_stock_code}, 错误: {str(e)}")
    
    # 测试卖出
    log_message(f"测试卖出: {test_stock_code}")
    
    # 尝试卖出
    try:
        passorder(24, 1101, account, stock_code_without_suffix, 8, 0, quantity, '', 2, '', ContextInfo)
        log_message(f"测试卖出下单成功: {test_stock_code}, 数量: {quantity}")
    except Exception as e:
        log_message(f"测试卖出下单失败: {test_stock_code}, 错误: {str(e)}")
    
    log_message("下单功能测试完成")

# 在文件末尾添加测试函数
def init_test_order(ContextInfo):
    """
    初始化测试下单功能
    """
    log_message("开始初始化测试下单功能")
    
    # 初始化全局变量
    g.price = {}
    g.select = False
    g.sell = []
    g.max_profit = {}
    g.last_trading_day = ""
    g.bought_stocks = set()
    g.log_file = "trading_log.txt"
    g.holding_days = {}
    g.entry_prices = {}
    g.stock_volatility = {}
    g.next_day_orders = []
    g.signal_counts = {}
    g.position_stage = {}
    g.last_signal_time = {}
    g.estimated_volume_data = {}
    
    # 设置日志文件
    try:
        with open(g.log_file, 'w', encoding='utf-8') as f:
            f.write(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 下单测试日志开始\n")
    except Exception as e:
        print(f"创建日志文件出错: {str(e)}")
    
    # 测试下单功能
    test_order_functionality(ContextInfo)
    
    log_message("测试下单功能初始化完成")











