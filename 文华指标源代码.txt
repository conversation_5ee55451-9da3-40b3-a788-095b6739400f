HX:=HHV(HIGH,2);
LX:=LLV(LOW,2);
H1:=IFELSE(HX<REF(HX,1)&&HX<REF(HX,2)&&HX<REF(HX,4)&&LX<REF(LX,1)&&LX<REF(LX,3)&&LX<REF(LX,5)&&OPEN>CLOSE&&(HHV(OPEN,0)-CLOSE)>0,REF(HX,4),0);
L1:=IFELSE(LX>REF(LX,1)&&LX>REF(LX,3)&&LX>REF(LX,5)&&HX>REF(HX,1)&&HX>REF(HX,2)&&HX>REF(HX,4)&&OPEN<CLOSE&&(CLOSE-LLV(OPEN,0))>0,REF(LX,4),0);
H2:=VALUEWHEN(H1>0,H1);
L2:=VALUEWHEN(L1>0,L1);
K1:=IFELSE(CLOSE>H2,-3,IFELSE(CLOSE<L2,1,0));
K2:=VALUEWHEN(K1<>0,K1);
G:=IFELSE(K2=1,H2,L2);
G1:=VALUEWHEN(ISLASTBAR,G);
DRAWNUMBER(LX>0,G1,G1,0,COLORWHITE);
TMP:=K2;
W1:=K2;
W2:=OPEN-CLOSE;
HT:=IFELSE(OPEN>CLOSE,OPEN,CLOSE);
LT:=IFELSE(OPEN<CLOSE,OPEN,CLOSE);
DRAWLINE(W1=1,HIGH,W1=1,HT,COLORWHITE);
DRAWLINE(W1=1,LOW,W1=1,LT,COLORWHITE);
DRAWLINE(W1=-3,HIGH,W1=-3,HT,COLORRED);
DRAWLINE(W1=-3,LOW,W1=-3,LT,COLORRED);
STICKLINE(W1>0,OPEN,CLOSE,COLORWHITE,1);
STICKLINE(W1<=0,OPEN,CLOSE,COLORRED,1);
STICKLINE(W2>0&&W1<=0,OPEN,CLOSE,COLORRED,0);
STICKLINE(W2>0&&W1>0,OPEN,CLOSE,COLORWHITE,0);
DRAWLINE(TMP=1&&REF(TMP,1)=1,G,TMP=1&&REF(TMP,1)=1,REF(G,1),COLORWHITE);
DRAWLINE(TMP=-3&&REF(TMP,1)=-3,G,TMP=-3&&REF(TMP,1)=-3,REF(G,1),COLORRED);
DRAWSL(K2=1,G,0,1,0,COLORWHITE);
DRAWSL(K2=-3,G,0,1,0,COLORRED);
DRAWTEXT(CROSS(TMP,0),HX,'卖'),COLORGREEN,FONTSIZE22;
DRAWTEXT(CROSS(0,TMP),LX,'买'),COLORYELLOW,FONTSIZE22;
MA55:=MA(CLOSE,55);
DRAWTEXT(CROSS(TMP,0)&&CLOSE<=MA55,HX,'卖'),COLORGREEN,FONTSIZE22;
DRAWTEXT(CROSS(0,TMP)&&CLOSE>=MA55,LX,'买'),COLORYELLOW,FONTSIZE22;

