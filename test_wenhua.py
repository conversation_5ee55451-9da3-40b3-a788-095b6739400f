import pandas as pd
import numpy as np

def calculate_wenhua_lines(price_data):
    """按照文华指标计算红白线值"""
    if len(price_data) == 0:
        return 0, 0, 0
        
    # 1. 计算基础数据
    high = price_data['high']
    low = price_data['low']
    open_price = price_data['open']
    close = price_data['close']
    
    # HX:=HHV(HIGH,2);
    hx = high.rolling(window=2).max()
    # LX:=LLV(LOW,2);
    lx = low.rolling(window=2).min()
    
    # 2. 计算参考值
    def REF(S, N=1):
        return pd.Series(S).shift(N).values
    
    ref_hx_1 = REF(hx, 1)
    ref_hx_2 = REF(hx, 2)
    ref_hx_4 = REF(hx, 4)
    ref_lx_1 = REF(lx, 1)
    ref_lx_3 = REF(lx, 3)
    ref_lx_5 = REF(lx, 5)
    
    # 3. 计算H1和L1
    latest_open = open_price.iloc[-1]
    latest_close = close.iloc[-1]
    
    # H1条件判断
    h1_condition = (
        hx.iloc[-1] < ref_hx_1[-1] and 
        hx.iloc[-1] < ref_hx_2[-1] and 
        hx.iloc[-1] < ref_hx_4[-1] and 
        lx.iloc[-1] < ref_lx_1[-1] and 
        lx.iloc[-1] < ref_lx_3[-1] and 
        lx.iloc[-1] < ref_lx_5[-1] and 
        latest_open > latest_close and 
        (open_price.max() - latest_close) > 0
    )
    
    # L1条件判断
    l1_condition = (
        lx.iloc[-1] > ref_lx_1[-1] and 
        lx.iloc[-1] > ref_lx_3[-1] and 
        lx.iloc[-1] > ref_lx_5[-1] and 
        hx.iloc[-1] > ref_hx_1[-1] and 
        hx.iloc[-1] > ref_hx_2[-1] and 
        hx.iloc[-1] > ref_hx_4[-1] and 
        latest_open < latest_close and 
        (latest_close - open_price.min()) > 0
    )
    
    # 4. 计算H2和L2
    H2 = ref_hx_4[-1] if h1_condition else high.iloc[-1]
    L2 = ref_lx_4[-1] if l1_condition else low.iloc[-1]
    
    # 5. 计算K1和K2
    if latest_close > H2:
        K1 = -3
    elif latest_close < L2:
        K1 = 1
    else:
        K1 = 0
    
    K2 = K1 if K1 != 0 else 0  # 简化处理
    
    # 6. 根据文华指标逻辑计算G值
    # G:=IFELSE(K2=1,H2,L2) - 当K2=1时取H2，否则取L2
    G = H2 if K2 == 1 else L2
    
    # 7. 根据K2值确定显示的颜色
    # 当K2=1时显示白色线，当K2=-3时显示红色线
    white_line = G if K2 == 1 else None
    red_line = G if K2 == -3 else None
    
    return white_line, red_line, K2

# 创建测试数据
np.random.seed(42)
dates = pd.date_range('2024-01-01', periods=10, freq='5min')
base_price = 100.0

# 模拟价格数据 - 先下跌后上涨
prices = []
for i in range(10):
    if i < 5:
        # 前5个周期：下跌趋势
        price = base_price - i * 0.5 + np.random.normal(0, 0.1)
    else:
        # 后5个周期：上涨趋势
        price = base_price - 2.5 + (i-5) * 0.8 + np.random.normal(0, 0.1)
    prices.append(price)

# 创建OHLC数据
data = []
for i, price in enumerate(prices):
    open_price = price + np.random.normal(0, 0.05)
    high = max(open_price, price) + abs(np.random.normal(0, 0.05))
    low = min(open_price, price) - abs(np.random.normal(0, 0.05))
    close = price + np.random.normal(0, 0.05)
    
    data.append({
        'open': open_price,
        'high': high,
        'low': low,
        'close': close
    })

price_data = pd.DataFrame(data, index=dates)

print("测试数据:")
print(price_data)

# 测试文华指标计算
white_line, red_line, k2 = calculate_wenhua_lines(price_data)

print(f"\n测试结果:")
print(f"K2值: {k2}")
print(f"白色线: {f'{white_line:.4f}' if white_line is not None else 'None'}")
print(f"红色线: {f'{red_line:.4f}' if red_line is not None else 'None'}")

# 测试不同K2值的情况
print(f"\n不同K2值的显示逻辑:")
print(f"K2=1时: 显示白色线 (G=H2)")
print(f"K2=-3时: 显示红色线 (G=L2)")
print(f"K2=0时: 显示红色线 (G=L2)")

# 验证互斥性
if white_line is not None and red_line is not None:
    print("错误：红白线同时存在！")
elif white_line is not None:
    print("正确：只显示白色线")
elif red_line is not None:
    print("正确：只显示红色线")
else:
    print("错误：没有显示任何线！") 