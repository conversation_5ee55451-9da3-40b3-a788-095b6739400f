import re

def replace_in_file(file_path):
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # 使用正则表达式替换所有的get_market_data调用
    pattern = r'ContextInfo\.get_market_data\('
    replacement = r'ContextInfo.get_market_data_ex('
    new_content = re.sub(pattern, replacement, content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as file:
        file.write(new_content)
    
    print(f"已完成替换，文件已保存: {file_path}")

if __name__ == "__main__":
    file_path = "期货策略.py"
    replace_in_file(file_path) 