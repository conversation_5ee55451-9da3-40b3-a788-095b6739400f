import re

# 尝试不同的编码
encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'utf-16']

content = None
for encoding in encodings:
    try:
        # 读取文件内容
        with open('股票策略.py', 'r', encoding=encoding) as f:
            content = f.read()
        print(f"成功使用 {encoding} 编码读取文件")
        break
    except UnicodeDecodeError:
        print(f"使用 {encoding} 编码读取失败，尝试下一种编码")
        continue

if content is None:
    print("无法读取文件，所有编码都失败")
    exit(1)

# 替换第一处市值检查代码
pattern1 = r'# 检查市值是否大于300亿[\s\S]+?if market_cap > 300:[\s\S]+?continue'
replacement1 = '# 检查市值是否大于300亿\n                    if check_market_cap_limit(stock_details, s, 300):\n                        print(f"跳过 {s}: 市值超过300亿限制")\n                        continue'

modified_content = re.sub(pattern1, replacement1, content, count=2)

# 修复第二处代码中的逻辑错误（if current_price < 40 改为 if current_price >= 40）
pattern2 = r'if current_price < 40:[\s\S]+?股价过高\(>40元\)[\s\S]+?continue'
replacement2 = 'if current_price >= 40:\n                        print(f"跳过 {s}: 股价过高(>40元)")\n                        continue'

modified_content = re.sub(pattern2, replacement2, modified_content)

# 写回文件
try:
    with open('股票策略.py', 'w', encoding=encoding) as f:
        f.write(modified_content)
    print(f"文件修改完成！使用 {encoding} 编码写回")
except Exception as e:
    print(f"写回文件时出错: {e}") 