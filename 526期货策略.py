import numpy as np

def check_price_line_distance(current_price, white_line, red_line):
    """
    检查价格与红白线的距离
    
    参数:
    current_price: 当前价格
    white_line: 白线值
    red_line: 红线值
    
    返回:
    (red_line_distance_ok, white_line_distance_ok): 是否符合做多和做空的距离要求
    """
    red_line_distance_ok = True
    white_line_distance_ok = True
    
    # 检查价格与白线的距离（用于做空）
    if white_line is not None and not pd.isna(white_line) and white_line != 0:
        try:
            white_line_distance = (white_line - current_price) / white_line * 100
            if not np.isfinite(white_line_distance):  # 检查结果是否为有限数
                print("警告: 计算白线距离时出现无效值，跳过白线距离检查")
            else:
                white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
                print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
        except Exception as e:
            print(f"计算白线距离时出错: {str(e)}")
            white_line_distance_ok = False
    else:
        print("白线不存在或为空或为零，无法计算与白线的距离")
        
    # 检查价格与红线的距离（用于做多）
    if red_line is not None and not pd.isna(red_line) and red_line != 0:
        try:
            red_line_distance = (current_price - red_line) / red_line * 100
            if not np.isfinite(red_line_distance):  # 检查结果是否为有限数
                print("警告: 计算红线距离时出现无效值，跳过红线距离检查")
            else:
                red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
                print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
        except Exception as e:
            print(f"计算红线距离时出错: {str(e)}")
            red_line_distance_ok = False
    else:
        print("红线不存在或为空或为零，无法计算与红线的距离")
        
    return red_line_distance_ok, white_line_distance_ok

def check_and_print_line_distance(current_price, white_line, red_line):
    """
    检查并打印价格与红白线的距离
    
    参数:
    current_price: 当前价格
    white_line: 白线值
    red_line: 红线值
    
    返回:
    (red_line_distance_ok, white_line_distance_ok): 距离是否符合要求
    """
    red_line_distance_ok = True
    white_line_distance_ok = True
    
    print("\n=== 价格与红白线距离检查 ===")
    print(f"当前价格: {current_price}")
    
    # 检查价格与白线的距离（用于做空）
    if white_line is not None and not pd.isna(white_line) and white_line != 0:
        try:
            white_line_distance = (white_line - current_price) / white_line * 100
            if not np.isfinite(white_line_distance):  # 检查结果是否为有限数
                print("警告: 计算白线距离时出现无效值，跳过白线距离检查")
            else:
                white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
                print(f"白线价格: {white_line}")
                print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
                if not white_line_distance_ok:
                    print(f"警告: 价格与白线距离超过{Config.WHITE_LINE_DISTANCE_THRESHOLD * 100:.2f}%阈值，建议等待反弹后再考虑做空")
        except Exception as e:
            print(f"计算白线距离时出错: {str(e)}")
            white_line_distance_ok = False
    else:
        print("白线不存在或为空或为零，无法计算距离")
    
    # 检查价格与红线的距离（用于做多）
    if red_line is not None and not pd.isna(red_line) and red_line != 0:
        try:
            red_line_distance = (current_price - red_line) / red_line * 100
            if not np.isfinite(red_line_distance):  # 检查结果是否为有限数
                print("警告: 计算红线距离时出现无效值，跳过红线距离检查")
            else:
                red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
                print(f"红线价格: {red_line}")
                print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
                if not red_line_distance_ok:
                    print(f"警告: 价格与红线距离超过{Config.RED_LINE_DISTANCE_THRESHOLD * 100:.2f}%阈值，建议等待回调后再考虑做多")
        except Exception as e:
            print(f"计算红线距离时出错: {str(e)}")
            red_line_distance_ok = False
    else:
        print("红线不存在或为空或为零，无法计算距离")
    
    return red_line_distance_ok, white_line_distance_ok

def print_line_details(price_data, white_line, red_line):
    """
    打印红白线详细信息
    
    参数:
    price_data: 价格数据
    white_line: 白线值
    red_line: 红线值
    """
    try:
        print("\n=== 红白线详细信息 ===")
        
        if price_data is None or price_data.empty:
            print("价格数据为空")
            return
            
        current_price = price_data['close'].iloc[-1]
        print(f"当前价格: {current_price}")
        print(f"白线价格: {white_line}")
        print(f"红线价格: {red_line}")
        
        # 检查价格与白线的距离（用于做空）
        if white_line is not None and not pd.isna(white_line) and white_line != 0 and current_price is not None:
            try:
                white_line_distance = (white_line - current_price) / white_line * 100
                if not np.isfinite(white_line_distance):  # 检查结果是否为有限数
                    print("警告: 计算白线距离时出现无效值")
                else:
                    white_line_distance_ok = white_line_distance <= Config.WHITE_LINE_DISTANCE_THRESHOLD * 100
                    print(f"价格与白线距离: {white_line_distance:.2f}% {'(符合做空条件)' if white_line_distance_ok else '(超出做空阈值)'}")
                    
                    # 检查是否突破白线
                    if current_price > white_line:
                        print(f"突破白线: 价格{current_price} > 白线{white_line}")
                    else:
                        print("无白线突破")
            except Exception as e:
                print(f"计算白线距离时出错: {str(e)}")
        else:
            print("无白线或白线为空或为零")
            
        # 检查价格与红线的距离（用于做多）
        if red_line is not None and not pd.isna(red_line) and red_line != 0 and current_price is not None:
            try:
                red_line_distance = (current_price - red_line) / red_line * 100
                if not np.isfinite(red_line_distance):  # 检查结果是否为有限数
                    print("警告: 计算红线距离时出现无效值")
                else:
                    red_line_distance_ok = red_line_distance <= Config.RED_LINE_DISTANCE_THRESHOLD * 100
                    print(f"价格与红线距离: {red_line_distance:.2f}% {'(符合做多条件)' if red_line_distance_ok else '(超出做多阈值)'}")
                    
                    # 检查是否跌破红线
                    if current_price < red_line:
                        print(f"跌破红线: 价格{current_price} < 红线{red_line}")
                    else:
                        print("无红线跌破")
            except Exception as e:
                print(f"计算红线距离时出错: {str(e)}")
        else:
            print("无红线或红线为空或为零")
            
        # 打印最高价和最低价
        if 'high' in price_data.columns and 'low' in price_data.columns:
            print(f"最高价: {price_data['high'].iloc[-1]}")
            print(f"最低价: {price_data['low'].iloc[-1]}")
            
    except Exception as e:
        print(f"打印红白线详情出错: {e}") 

def calculate_red_white_lines_exact(price_data):
    try:
        # 确保数据足够
        if price_data is None or len(price_data) < 10:
            print("计算红白线错误: 数据不足")
            return pd.Series([None] * (0 if price_data is None else len(price_data))), pd.Series([None] * (0 if price_data is None else len(price_data)))
        
        # 检查必需的列是否存在
        required_columns = ['high', 'low', 'open', 'close']
        for col in required_columns:
            if col not in price_data.columns:
                print(f"计算红白线错误: 缺少必需的列 '{col}'")
                return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)
        
        # 检查数据是否包含NaN或无限值
        for col in required_columns:
            if price_data[col].isnull().any() or (~np.isfinite(price_data[col])).any():
                print(f"计算红白线警告: '{col}'列包含NaN或无限值")
                # 替换NaN和无限值为前一个有效值
                price_data[col] = price_data[col].replace([np.inf, -np.inf], np.nan).ffill()
        
        # 计算HX和LX
        high = price_data['high']
        low = price_data['low']
        open_price = price_data['open']
        close = price_data['close']
        
        # HX:=HHV(HIGH,2);
        hx = high.rolling(window=2).max()
        
        # LX:=LLV(LOW,2);
        lx = low.rolling(window=2).min()
        
        # 初始化H1, L1, K1, K2, G
        h1 = pd.Series([0] * len(price_data), index=price_data.index)
        l1 = pd.Series([0] * len(price_data), index=price_data.index)
        k1 = pd.Series([0] * len(price_data), index=price_data.index)
        k2 = pd.Series([0] * len(price_data), index=price_data.index)
        g = pd.Series([None] * len(price_data), index=price_data.index)
        
        # 安全处理所有计算，避免除零错误
        try:
            # H1 和 L1 计算
            for i in range(1, len(price_data)):
                # H1:=IFELSE(CLOSE>HX,IFELSE(CLOSE<H1[1],CLOSE,H1[1]),HX);
                if close.iloc[i] > hx.iloc[i]:
                    if i > 0 and close.iloc[i] < h1.iloc[i-1]:
                        h1.iloc[i] = close.iloc[i]
                    elif i > 0:
                        h1.iloc[i] = h1.iloc[i-1]
                else:
                    h1.iloc[i] = hx.iloc[i]
                
                # L1:=IFELSE(CLOSE<LX,IFELSE(CLOSE>L1[1],CLOSE,L1[1]),LX);
                if close.iloc[i] < lx.iloc[i]:
                    if i > 0 and close.iloc[i] > l1.iloc[i-1]:
                        l1.iloc[i] = close.iloc[i]
                    elif i > 0:
                        l1.iloc[i] = l1.iloc[i-1]
                else:
                    l1.iloc[i] = lx.iloc[i]
        except Exception as e:
            print(f"计算H1和L1时出错: {e}")
            return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)
            
        # H2:=VALUEWHEN(H1>0,H1);
        h2 = pd.Series([None] * len(price_data), index=price_data.index)
        last_valid_h1 = None
        for i in range(len(price_data)):
            if h1.iloc[i] > 0:
                last_valid_h1 = h1.iloc[i]
            h2.iloc[i] = last_valid_h1
        
        # L2:=VALUEWHEN(L1>0,L1);
        l2 = pd.Series([None] * len(price_data), index=price_data.index)
        last_valid_l1 = None
        for i in range(len(price_data)):
            if l1.iloc[i] > 0:
                last_valid_l1 = l1.iloc[i]
            l2.iloc[i] = last_valid_l1
        
        try:
            # K1:=IFELSE(CLOSE>H2,-3,IFELSE(CLOSE<L2,1,0));
            for i in range(len(price_data)):
                if h2.iloc[i] is not None and close.iloc[i] > h2.iloc[i]:
                    k1.iloc[i] = -3
                elif l2.iloc[i] is not None and close.iloc[i] < l2.iloc[i]:
                    k1.iloc[i] = 1
                else:
                    k1.iloc[i] = 0
        except Exception as e:
            print(f"计算K1时出错: {e}")
            return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index)
        
        # K2:=VALUEWHEN(K1<>0,K1);
        last_valid_k1 = 0
        for i in range(len(price_data)):
            if k1.iloc[i] != 0:
                last_valid_k1 = k1.iloc[i]
            k2.iloc[i] = last_valid_k1
        
        # G:=IFELSE(K2=1,H2,L2);
        for i in range(len(price_data)):
            if k2.iloc[i] == 1:
                g.iloc[i] = h2.iloc[i]
            else:
                g.iloc[i] = l2.iloc[i]
        
        # 转换为红白线
        white_line = pd.Series([None] * len(price_data), index=price_data.index)
        red_line = pd.Series([None] * len(price_data), index=price_data.index)
        
        for i in range(len(price_data)):
            if k2.iloc[i] == 1:  # 白线
                white_line.iloc[i] = g.iloc[i]
                red_line.iloc[i] = None
            elif k2.iloc[i] == -3:  # 红线
                red_line.iloc[i] = g.iloc[i]
                white_line.iloc[i] = None
        
        # 检查结果是否有效
        white_count = white_line.notna().sum()
        red_count = red_line.notna().sum()
        print(f"文华指标红白线计算完成: 数据长度={len(price_data)}, 白线非空值={white_count}, 红线非空值={red_count}")
        
        # 检查最后一个值是否为NaN或无限值
        if white_line.iloc[-1] is not None and not np.isfinite(white_line.iloc[-1]):
            print("警告: 白线最后一个值无效，已替换为None")
            white_line.iloc[-1] = None
            
        if red_line.iloc[-1] is not None and not np.isfinite(red_line.iloc[-1]):
            print("警告: 红线最后一个值无效，已替换为None")
            red_line.iloc[-1] = None
            
        return white_line, red_line
    
    except Exception as e:
        print(f"计算文华指标红白线出错: {e}")
        import traceback
        traceback.print_exc()  # 打印详细的堆栈跟踪
        # 返回空序列
        return pd.Series([None] * len(price_data), index=price_data.index), pd.Series([None] * len(price_data), index=price_data.index) 

def should_trade_with_transition(transition_signal, current_price, MA_Line, trend, volume_data, 
                               white_line=None, red_line=None, time_filter=True, ma55_value=None,
                               prev_white=None, prev_red=None):  # 添加前一根K线的线值
    """
    根据红白线转换信号判断是否交易
    
    参数:
    transition_signal: 转换信号，包含"red_to_white", "white_to_red", "cross_white", "cross_red"等
    current_price: 当前价格
    MA_Line: MA均线值
    trend: 当前趋势，可以是"up", "down", "sideways"
    volume_data: 成交量数据
    white_line: 白线值
    red_line: 红线值
    time_filter: 是否应用时间过滤
    ma55_value: 55均线值
    prev_white: 前一根K线的白线值
    prev_red: 前一根K线的红线值
    
    返回:
    (transition_long, transition_short): 是否应该做多和做空
    """
    try:
        # 初始化交易信号
        transition_long = False
        transition_short = False
        
        # 解析转换信号
        if transition_signal is None:
            return False, False
            
        # 检查与MA均线的关系
        above_ma = False
        below_ma = False
        
        if MA_Line is not None and not pd.isna(MA_Line):
            try:
                above_ma = current_price > MA_Line
                below_ma = current_price < MA_Line
            except Exception as e:
                print(f"检查价格与MA均线关系时出错: {e}")
        
        # 检查成交量增加
        volume_increase = False
        if volume_data is not None and len(volume_data) >= 4:
            try:
                volume_increase = check_volume_increase(volume_data)
            except Exception as e:
                print(f"检查成交量增加时出错: {e}")
        
        # 检查与红白线的距离
        red_line_distance_ok = True
        white_line_distance_ok = True
        
        if "check_price_line_distance" in globals():
            try:
                red_line_distance_ok, white_line_distance_ok = check_price_line_distance(current_price, white_line, red_line)
            except Exception as e:
                print(f"检查价格与红白线距离时出错: {e}")
        
        # 根据转换信号和其他条件决定交易信号
        if "red_to_white" in transition_signal:
            # 红线转白线，考虑做空
            if trend == "down" and below_ma and white_line_distance_ok:
                transition_short = True
                print("红转白，趋势向下，价格低于MA均线，距离合适，生成做空信号")
            else:
                reasons = []
                if trend != "down":
                    reasons.append(f"趋势不向下({trend})")
                if not below_ma:
                    reasons.append("价格不低于MA均线")
                if not white_line_distance_ok:
                    reasons.append("价格与白线距离不合适")
                print(f"红转白，不做空原因: {', '.join(reasons)}")
                
        elif "white_to_red" in transition_signal:
            # 白线转红线，考虑做多
            if trend == "up" and above_ma and red_line_distance_ok:
                transition_long = True
                print("白转红，趋势向上，价格高于MA均线，距离合适，生成做多信号")
            else:
                reasons = []
                if trend != "up":
                    reasons.append(f"趋势不向上({trend})")
                if not above_ma:
                    reasons.append("价格不高于MA均线")
                if not red_line_distance_ok:
                    reasons.append("价格与红线距离不合适")
                print(f"白转红，不做多原因: {', '.join(reasons)}")
                
        elif "cross_white" in transition_signal:
            # 价格突破白线，考虑做多
            if trend == "up" and above_ma and volume_increase:
                transition_long = True
                print("突破白线，趋势向上，价格高于MA均线，成交量增加，生成做多信号")
            else:
                reasons = []
                if trend != "up":
                    reasons.append(f"趋势不向上({trend})")
                if not above_ma:
                    reasons.append("价格不高于MA均线")
                if not volume_increase:
                    reasons.append("成交量未增加")
                print(f"突破白线，不做多原因: {', '.join(reasons)}")
                
        elif "cross_red" in transition_signal:
            # 价格跌破红线，考虑做空
            if trend == "down" and below_ma and volume_increase:
                transition_short = True
                print("跌破红线，趋势向下，价格低于MA均线，成交量增加，生成做空信号")
            else:
                reasons = []
                if trend != "down":
                    reasons.append(f"趋势不向下({trend})")
                if not below_ma:
                    reasons.append("价格不低于MA均线")
                if not volume_increase:
                    reasons.append("成交量未增加")
                print(f"跌破红线，不做空原因: {', '.join(reasons)}")
        
        # 打印最终决策
        if transition_long:
            print("*** 生成红白线转换做多信号 ***")
        if transition_short:
            print("*** 生成红白线转换做空信号 ***")
            
        return transition_long, transition_short
        
    except Exception as e:
        print(f"判断红白线转换交易信号时出错: {e}")
        import traceback
        traceback.print_exc()
        return False, False 