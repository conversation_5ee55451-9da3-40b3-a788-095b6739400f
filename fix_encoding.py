import os

def convert_file_encoding(input_file, output_file):
    try:
        # 尝试用不同的编码读取文件
        encodings = ['utf-8', 'gbk', 'cp936', 'gb2312', 'gb18030']
        
        for encoding in encodings:
            try:
                with open(input_file, 'r', encoding=encoding) as f:
                    content = f.read()
                
                # 添加编码声明到文件开头
                if not content.startswith("# -*- coding: utf-8 -*-"):
                    content = "# -*- coding: utf-8 -*-\n" + content
                
                # 成功读取，写入utf-8编码的文件
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"文件转换成功：{input_file} -> {output_file}，原编码：{encoding}")
                return True
            except UnicodeDecodeError:
                continue
        
        print(f"无法转换文件：{input_file}，尝试了所有可能的编码")
        return False
    except Exception as e:
        print(f"转换文件时出错：{e}")
        return False

if __name__ == "__main__":
    input_file = "多腿期权.py"
    output_file = "multileg_options.py"
    
    if convert_file_encoding(input_file, output_file):
        # 尝试运行转换后的文件
        print("尝试运行转换后的文件...")
        try:
            os.system(f"python {output_file}")
        except Exception as e:
            print(f"运行文件时出错：{e}") 