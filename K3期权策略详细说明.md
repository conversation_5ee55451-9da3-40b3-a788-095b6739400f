# K3期权策略详细说明

## 📋 策略概述

K3期权策略是基于文华指标红白线系统的期权交易策略，通过K2信号变化来判断市场趋势，执行认购/认沽期权的买入操作。

## 🎯 开仓条件详解

### 1. 核心信号源：文华指标K2值

#### 信号计算逻辑：
```
HX := HHV(HIGH,2)     # 2周期最高价
LX := LLV(LOW,2)      # 2周期最低价

# 复杂的条件判断生成H1和L1
H1 := 满足特定条件时取REF(HX,4)，否则为0
L1 := 满足特定条件时取REF(LX,4)，否则为0

# 关键价位
H2 := VALUEWHEN(H1>0,H1)  # 记录最近的H1值
L2 := VALUEWHEN(L1>0,L1)  # 记录最近的L1值

# 信号生成
K1 := IFELSE(CLOSE>H2,-3,IFELSE(CLOSE<L2,1,0))
K2 := VALUEWHEN(K1<>0,K1)  # 保持最近的非零K1值
```

#### 信号含义：
- **K2 = 1**: 看涨信号（白线），对应文华指标显示"买"
- **K2 = -3**: 看跌信号（红线），对应文华指标显示"卖"

### 2. 开仓信号检测

#### 买入认购期权信号（做多）：
```python
# 5分钟周期信号
k2_buy_signal_5m = (prev_k2_5m != 1 and k2_5m == 1)

# 30分钟周期信号
k2_buy_signal_30m = (prev_k2_30m != 1 and k2_30m == 1)

# 最终买入信号
buy_signal = k2_buy_signal_5m or k2_buy_signal_30m
```

#### 买入认沽期权信号（做空）：
```python
# 5分钟周期信号
k2_sell_signal_5m = (prev_k2_5m != -3 and k2_5m == -3)

# 30分钟周期信号
k2_sell_signal_30m = (prev_k2_30m != -3 and k2_30m == -3)

# 最终卖出信号
sell_signal = k2_sell_signal_5m or k2_sell_signal_30m
```

### 3. MA55过滤条件（可选）

当 `enable_ma55_filter = True` 时：
```python
# 买信号需要收盘价>=MA55
k2_buy_signal_5m = (prev_k2_5m != 1 and k2_5m == 1 and close >= MA55)

# 卖信号需要收盘价<=MA55
k2_sell_signal_5m = (prev_k2_5m != -3 and k2_5m == -3 and close <= MA55)
```

### 4. 开仓限制条件

#### 基本限制：
- **无持仓状态**: `g.hold == 0`
- **时间间隔**: 距离上次同方向开仓超过5分钟
- **次数限制**: 开多次数 < 2次，开空次数 < 2次

#### 期权合约选择：
- **认购期权**: 选择实值期权（行权价 < 标的价格）中最接近标的价格的合约
- **认沽期权**: 选择实值期权（行权价 > 标的价格）中最接近标的价格的合约
- **到期日管理**: 自动选择距离到期日>10天的合约，不足时切换到下月合约

## 🛡️ 止盈止损体系

### 1. 固定止损（第一道防线）
```python
best_stoploss_ratio = -30%  # 固定止损比例

# 计算盈亏比例
if g.hold == 1:  # 持有认购期权
    profit_ratio = (current_price - hold_price) / hold_price * 100
else:  # 持有认沽期权
    profit_ratio = (hold_price - current_price) / hold_price * 100

# 触发条件
if profit_ratio <= -30%:
    平仓并重置状态
```

### 2. 浮盈追踪止盈（第二道防线）
```python
best_trail_start = 50%      # 启动追踪的盈利阈值
best_trail_drawdown = 20%   # 回撤止盈阈值

# 记录最高盈利
g.max_profit_ratio = max(g.max_profit_ratio, current_profit_ratio)

# 触发条件
if g.max_profit_ratio >= 50% and current_profit_ratio <= (g.max_profit_ratio - 20%):
    平仓并重置状态
```

### 3. 时间止损（第三道防线）
```python
best_max_hold_days = 5  # 最大持仓天数

# 计算持仓天数
hold_days = (datetime.now() - g.open_time).days

# 触发条件
if hold_days >= 5:
    平仓并重置状态
```

### 4. K2信号变化止盈止损（第四道防线）

#### 持有认购期权时：
```python
# 出现卖信号时平仓认购并开仓认沽
if k2_sell_signal_5m or k2_sell_signal_30m:
    平仓当前认购期权
    立即开仓认沽期权
    更新持仓状态
```

#### 持有认沽期权时：
```python
# 出现买信号时平仓认沽并开仓认购
if k2_buy_signal_5m or k2_buy_signal_30m:
    平仓当前认沽期权
    立即开仓认购期权
    更新持仓状态
```

### 5. 合约到期管理
```python
expiry_days_threshold = 10  # 到期日阈值

# 检查到期日
days_to_expiry = (expiry_date - current_date).days

if days_to_expiry <= 10:
    平仓当前持仓
    重置持仓状态
    等待新信号重新开仓
```

## ⚙️ 关键参数配置

### 交易参数
```python
hand = 1                    # 每次交易张数
sleep_time = 5              # 同方向开仓间隔（分钟）
open_long_num = 2           # 最大开多次数
open_short_num = 2          # 最大开空次数
```

### 风控参数
```python
expiry_days_threshold = 10  # 到期日切换阈值（天）
hold_days_limit = 5         # 最大持仓天数
enable_ma55_filter = False  # MA55过滤开关
```

### 止盈止损参数
```python
best_stoploss_ratio = -30   # 固定止损比例（%）
best_trail_start = 50       # 追踪止盈启动点（%）
best_trail_drawdown = 20    # 追踪止盈回撤（%）
```

## 🔄 策略执行流程

1. **数据获取**: 获取5分钟和30分钟K线数据
2. **指标计算**: 计算文华指标K2值
3. **信号检测**: 检测K2值变化产生的买卖信号
4. **开仓判断**: 检查开仓条件和限制
5. **期权选择**: 选择合适的期权合约
6. **风险控制**: 执行多层止盈止损机制
7. **持仓管理**: 监控合约到期和信号变化

## 💡 实际执行示例

### 开仓示例：
```
时间: 09:35:00
5分钟K2: -3 → 1 (触发买信号)
30分钟K2: -3 (无变化)
MA55过滤: 关闭
持仓状态: 无持仓
距离上次开多: 8分钟 > 5分钟 ✓

执行动作:
1. 获取实值认购期权合约
2. 下单买入1张认购期权
3. 更新持仓状态: g.hold = 1
4. 记录开仓价格和时间
```

### 止损示例：
```
持仓: 认购期权
开仓价格: 100点
当前价格: 65点
盈亏比例: (65-100)/100 = -35%
固定止损阈值: -30%

触发条件: -35% <= -30% ✓
执行动作: 立即平仓认购期权
```

### 追踪止盈示例：
```
持仓: 认沽期权
最高盈利: 60%
当前盈利: 35%
回撤幅度: 60% - 35% = 25%
追踪阈值: 20%

触发条件: 最高盈利60% >= 50% 且 回撤25% >= 20% ✓
执行动作: 立即平仓认沽期权
```

### 信号变化调仓示例：
```
当前持仓: 认购期权
5分钟K2: 1 → -3 (触发卖信号)
执行动作:
1. 平仓当前认购期权
2. 立即开仓认沽期权
3. 更新持仓状态: g.hold = -1
```

## 📊 策略特点

### 优势：
- **多时间周期确认**: 5分钟+30分钟双重确认，提高信号可靠性
- **多层风控体系**: 4道止盈止损防线，全方位风险控制
- **自动合约管理**: 智能处理到期切换，避免交割风险
- **信号跟随**: 根据K2信号变化及时调仓，捕捉趋势反转
- **实值期权策略**: 选择实值期权，降低时间价值损失
- **参数可调**: 所有关键参数都可以根据市场情况调整

### 风险点：
- **期权时间价值衰减**: 需要及时止损，避免时间价值归零
- **信号频繁变化**: 可能产生过度交易，增加交易成本
- **市场流动性**: 期权合约流动性风险，特别是临近到期时
- **参数敏感性**: 需要根据不同市场环境调优参数
- **跳空风险**: 开盘跳空可能导致止损失效
- **系统风险**: 网络延迟、系统故障等技术风险

## 🎯 使用建议

### 1. 参数调优建议：
- **活跃市场**: 可适当放宽MA55过滤，增加交易机会
- **震荡市场**: 建议启用MA55过滤，减少假信号
- **高波动期**: 可适当放宽止损比例至-35%
- **低波动期**: 可收紧止损比例至-25%

### 2. 风险管理建议：
- **资金管理**: 单次交易资金不超过总资金的5%
- **持仓监控**: 密切关注持仓盈亏和到期时间
- **信号确认**: 重要信号建议人工确认后执行
- **定期回测**: 定期回测参数有效性，及时调整

### 3. 市场适应性：
- **趋势市场**: 策略表现较好，能够捕捉趋势
- **震荡市场**: 需要谨慎使用，可能产生频繁交易
- **单边市场**: 注意及时止盈，避免趋势反转损失
