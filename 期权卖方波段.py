#encoding:gbk


open_long_num =1
open_short_num = 1
hand = 1  #交易张数
moving_tick = 0.020  # 移动止损点位  #期权的波动单位为0.0001
fs_line = 0.002 #  FS即时价位—分时均线价位<0.1
sleep_time = 5 #分钟 1分钟内不连续开同方向仓位

# 新增期权选择参数
option_strike_offset = 1  # 平值上下的档数：1表示上下一档，2表示上下两档，依此类推
call_strike_adjust = 0  # 认购期权档位微调：0表示不调整，正数增加档位，负数减少档位
put_strike_adjust = 0   # 认沽期权档位微调：0表示不调整，正数增加档位，负数减少档位

# 条件启用开关
enable_condition1 = False  # 三色线条件
enable_condition3 = False  # B1-B2条件
enable_condition5 = False  # 底背驰/顶背驰条件
enable_condition6 = True   # 通达信副图信号条件

# ==================== 止盈止损参数（重要）==================== #
# 【期权卖方】止损止盈说明：
# 对于期权卖方(义务仓)：
# - 期权价格上涨时亏损，价格下跌时盈利！
# - 止损：期权价格上涨超过设定百分比(正值)时，分批平仓控制风险
# - 止盈：期权价格下跌超过设定百分比(负值)时，分批平仓锁定利润

# 止损参数（期权价格上涨=亏损）
option_seller_stop_loss_1 = 5     # % 第一次止损比例：期权价格上涨超过5%时平掉1/3仓位(亏损)
option_seller_stop_loss_2 = 10    # % 第二次止损比例：期权价格上涨超过10%时平掉1/3仓位(亏损)
option_seller_stop_loss_3 = 15    # % 第三次止损比例：期权价格上涨超过15%时平掉剩余仓位(亏损)

# 止盈参数（期权价格下跌=盈利）
option_seller_profit_1 = -5     # % 第一次止盈比例：期权价格下跌超过5%时平掉1/3仓位(盈利)
option_seller_profit_2 = -10    # % 第二次止盈比例：期权价格下跌超过10%时平掉1/3仓位(盈利)
option_seller_profit_3 = -15    # % 第三次止盈比例：期权价格下跌超过15%时平掉剩余仓位(盈利)

switch_days_before_expiry = 15  # 期权到期前多少天换月

# 在文件开头的参数配置区域添加以下参数
B1_sample_period = 20  # 计算B1平均值和标准差的周期
B1_std_multiplier = 1.5  # B1标准差倍数
volume_check_ratio = 1.2  # 成交量放大倍数
atr_period = 14  # 计算ATR的周期
atr_threshold = 1.5  # ATR阈值倍数
ma_periods = [5, 10]  # 多周期均线

# 添加条件6参数 - 通达信副图信号
x7_period = 5  # 通达信X_7指标周期
x9_period = 2  # 通达信X_9指标周期
oversold_threshold = 20  # 超卖阈值（图表中的安全区）
overbought_threshold = 80  # 超买阈值（图表中的风险系数值）
min_data_periods = 30  # 最小数据周期数，从10改为30确保更多历史数据

# 假信号过滤参数
enable_volume_filter = True    # 启用成交量过滤
enable_trend_filter = True     # 启用趋势过滤
enable_macd_filter = True      # 启用MACD过滤
enable_signal_confirm = True   # 启用信号确认机制
min_signal_interval = 15       # 最小信号间隔(分钟)，避免频繁交易
volume_threshold = 1.5         # 成交量放大倍数
trend_period = 60              # 趋势判断周期

import math
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta


class G():
    pass


g = G()
g.buy_long = 0
g.buy_short = 0
g.hold = 0
g.hold_price = 0
g.open_price = 0
g.trace_time_long = 0
g.trace_time_short = 0
g.opened_t = []
g.hold_code = ''
g.closed_ratio = 0  # 已平仓的比例：0, 0.33, 0.67, 1


def init(ContextInfo):
    g.remark = ContextInfo.request_id[-10:]
    
    g.call_one = None
    g.put_one = None
    ContextInfo.set_account(account)
    g.undl_code = g.code = g.stock = ContextInfo.stockcode+'.'+ContextInfo.market
    g.curr_hold = None
    g.closed_ratio = 0  # 初始化已平仓比例


def after_init(ContextInfo):
    download_history_data(g.code,'1d','********','********')
    return ContextInfo.get_trading_dates('SHO','','********',count=2, period='1d')
    

def handlebar(ContextInfo):
    timetag = ContextInfo.get_bar_timetag(ContextInfo.barpos)
    bar_date = timetag_to_datetime(timetag, '%Y%m%d%H%M%S')
    
        
    if not ContextInfo.is_last_bar():
        return
    price_1d=ContextInfo.get_market_data_ex(['close','open'],[g.code],
                            period='1d',count=2,subscribe=False)[g.code]
    if price_1d.index[-1]==bar_date[:8]:
        CC = price_1d.iloc[-2]['close']
        OO = price_1d.iloc[-2]['open']
    else:
        CC = price_1d.iloc[-1]['close']
        OO = price_1d.iloc[-1]['open']
    
    start, end = after_init(ContextInfo)
    
    price = ContextInfo.get_market_data_ex(['close','amount','volume','high','low'],[g.code],period='1m',
                    start_time=end+'093000',
                    end_time=end+'150000',
                    )[g.code]
    C = CLOSE = price['close']
    H = HIGH = price['high']
    L = LOW = price['low']
    VOL = price['volume']
    price_len = price.shape[0]
    if price_len<=20:
        return
    t = price.index[-1][-6:-2]
    if t<='0930':# 9：56开始执行策略
        return
    CG,FL,MA_Line,FS,B1,B2 = cal_vba(ContextInfo, price)
    first, last_time = get_shape(CG,FL,MA_Line,FS,B1,B2)
    pct = time.strftime("%H%M%S")
    b1 = not pct >='145400' # 收盘前10分钟清仓
    if any([math.isnan(CG[i]) for i in [-1,-2,-3,-4,-5]] ):
        return
    
    # 计算多周期均线
    MA_5 = MA(C, ma_periods[0])
    MA_10 = MA(C, ma_periods[1])
    
    # 计算ATR波动率
    TR = np.zeros(len(H))
    for i in range(1, len(H)):
        hl = H[i] - L[i]
        hc = abs(H[i] - C[i-1])
        lc = abs(L[i] - C[i-1])
        TR[i] = max(hl, hc, lc)
    ATR = pd.Series(TR).rolling(atr_period).mean().values
    is_low_volatility = ATR[-1] < np.mean(ATR[-atr_period:]) * atr_threshold
    
    # 计算B1自适应阈值
    B1_mean = np.mean(B1[-B1_sample_period:])
    B1_std = np.std(B1[-B1_sample_period:])
    B1_high_threshold = B1_mean + B1_std_multiplier * B1_std
    B1_low_threshold = B1_mean - B1_std_multiplier * B1_std
    
    # 计算成交量比较
    vol_ratio = VOL[-1] / MA(VOL, 5)[-1]
    is_volume_high = vol_ratio > volume_check_ratio
    
    # 计算B1-B2交叉信号
    b1b2_golden_cross = (B1[-1]-B2[-1]>0) and (B1[-2]-B2[-2]<=0)  # 金叉
    b1b2_death_cross = (B1[-1]-B2[-1]<0) and (B1[-2]-B2[-2]>=0)   # 死叉
    
    # 基本市场数据输出 - 简洁格式
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][行情] {g.code} {t} C:{C[-1]:.3f} MA:{MA_Line[-1]:.3f} FS:{FS[-1]:.3f} B1-B2:{B1[-1]-B2[-1]:.2f}')
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][自适应阈值] B1均值:{B1_mean:.2f} B1标准差:{B1_std:.2f} B1高阈值:{B1_high_threshold:.2f} B1低阈值:{B1_low_threshold:.2f}')
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][过滤条件] 成交量比:{vol_ratio:.2f} ATR:{ATR[-1]:.4f} 低波动:{is_low_volatility} 金叉:{b1b2_golden_cross} 死叉:{b1b2_death_cross}')
    
    last_five_minute_long = all([CG[i]==FL[i]==FS[i] for i in (-1,)])
    ContextInfo.paint('B1',B1[-1],-1,0,)
    ContextInfo.paint('B2',B2[-1],-1,0,)
    last_five_minute_short = all([CG[i]!=FL[i] and CG[i]!=FS[i] and FL[i]!=FS[i] for i in (-1,)])
    
    # 空头图形分析
    first_kong, last_time_kong = get_shape(CG,FL,MA_Line,FS,B1,B2)
    
    # 开多条件计算
    cross_up = FS[-1]<MA_Line[-1] and FS[-2]>=MA_Line[-2] and B1[-1]-B2[-1]<0
    tj_long = first_kong==1 and last_time_kong<=15 and last_five_minute_short and C[-1]<MA_Line[-1] and max(FS[-1],CG[-1],FL[-1])<MA_Line[-1] and B1[-1]-B2[-1]<0 and MA_Line[-1]-FS[-1]<fs_line
    
    # 计算多头条件3 - 优化版本
    b12 = list(B1<B2)
    b12kong = list(B1>B2)
    # 将固定阈值替换为自适应阈值
    tj3_long = B1[-1]>B1_low_threshold and B1[-2]<=B1_low_threshold and MA_Line[-1]>C[-1] and (MA_Line[-1]-C[-1])/C[-1]<=fs_line
    # 增加多周期均线判断和B1-B2交叉信号
    tj3_long = tj3_long and C[-1]<MA_5[-1] and C[-1]<MA_10[-1]
    # 只在低波动环境下开仓
    tj3_long = tj3_long and is_low_volatility
    # 在重要突破时增加成交量确认
    tj3_long = tj3_long and (is_volume_high or b1b2_golden_cross)
    kaikong = tj3_long and C[-1]<(CC+OO)/2
    
    # 空头条件计算
    cross_down = FS[-1]>MA_Line[-1] and FS[-2]<=MA_Line[-2] and B1[-1]-B2[-1]>0
    tj_short = first==1 and last_time<=15 and last_five_minute_long and C[-1]>MA_Line[-1] and FS[-1]>MA_Line[-1] and B1[-1]-B2[-1]>0 and C[-1]-MA_Line[-1]<fs_line
    
    # 计算空头条件3 - 优化版本
    # 将固定阈值替换为自适应阈值
    tj3_short = B1[-1]<B1_high_threshold and B1[-2]>=B1_high_threshold and C[-1]>MA_Line[-1] and (C[-1]-MA_Line[-1])/MA_Line[-1]<=fs_line
    # 增加多周期均线判断和B1-B2交叉信号
    tj3_short = tj3_short and C[-1]>MA_5[-1] and C[-1]>MA_10[-1]
    # 只在低波动环境下开仓
    tj3_short = tj3_short and is_low_volatility
    # 在重要突破时增加成交量确认
    tj3_short = tj3_short and (is_volume_high or b1b2_death_cross)
    kaiduo = tj3_short and C[-1]>(CC+OO)/2
    
    # 计算缠论指标
    DIF26 = EMA(C, 12) - EMA(C, 26)
    DEA26 = EMA(DIF26, 9)
    MACD26 = (DIF26 - DEA26) * 2
    底背驰 = float(L[-1]) == float(LLV(L, 25)[-1]) and float(L[-1]) < float(L[-2]) and float(MACD26[-1]) > float(MACD26[-2])
    顶背驰 = float(H[-1]) == float(HHV(H, 25)[-1]) and float(H[-1]) > float(H[-2]) and float(MACD26[-1]) < float(MACD26[-2])
    
    # 添加条件6：通达信副图信号计算 - 完全基于截图中的逻辑，但增加假信号过滤
    if enable_condition6:
        # 计算KDJ指标（用于X_2和X_3）
        RSV = (C - LLV(L, 9)) / (HHV(H, 9) - LLV(L, 9) + 0.0001) * 100
        K = SMA(RSV, 3, 1)
        D = SMA(K, 3, 1)
        J = 3 * K - 2 * D
        
        # 计算X_2和X_3（KDJ.J < -10判断）
        X_2 = np.where(J < -10, 10, 0)
        # 计算X_3，X_2由10变为0的点
        X_3 = np.zeros(len(X_2))
        for i in range(1, len(X_2)):
            if X_2[i-1] == 10 and X_2[i] == 0:
                X_3[i] = 1
        
        # 计算X_4到X_9 - 与通达信副图代码完全一致
        X_4 = (2*C + H + L)/4
        X_5 = LLV(L, 5)
        X_6 = HHV(H, 5)
        X_6_X_5_diff = np.array([max(0.0001, x) for x in (X_6 - X_5)])
        X_7_raw = (X_4 - X_5) / X_6_X_5_diff * 100
        X_7_raw = np.nan_to_num(X_7_raw, nan=50.0)
        X_7 = MA(X_7_raw, x7_period)
        X_9 = MA(X_7, x9_period)
        
        # 确保X_7和X_9没有nan值
        X_7 = np.nan_to_num(X_7, nan=50.0)
        X_9 = np.nan_to_num(X_9, nan=50.0)
        
        # 计算X_10到X_13 - 与通达信副图代码完全一致
        X_10 = (C - LLV(L, 13)) / (HHV(H, 13) - LLV(L, 13) + 0.0001) * 100
        X_11 = SMA(X_10, 3, 1)
        X_12 = SMA(X_11, 3, 1)
        X_13 = 3 * X_11 - 2 * X_12
        
        # 计算X_14和X_15 - 分别表示X_13低位向上和高位向下
        X_14 = np.zeros(len(X_13))
        X_15 = np.zeros(len(X_13))
        for i in range(1, len(X_13)):
            if X_13[i] < 15 and X_13[i] > X_13[i-1]:
                X_14[i] = 1
            if X_13[i] > 95 and X_13[i] < X_13[i-1]:
                X_15[i] = 1
        
        # 检查是否有足够的数据进行信号判断
        has_enough_data = len(C) >= min_data_periods
        
        # X_16计算：做多信号，完全匹配通达信代码
        # X_16:=CROSS(X_7,X_9) AND X_9<20 AND X_7<20;
        X_16_raw = False
        if has_enough_data and len(X_7) > 1 and len(X_9) > 1:
            # 精确匹配通达信：X_7上穿X_9且都在超卖区
            X_16_raw = (X_7[-1] > X_9[-1] and X_7[-2] <= X_9[-2]) and X_9[-1] < oversold_threshold and X_7[-1] < oversold_threshold
        
        # X_17计算：做空信号，完全匹配通达信代码
        # X_17:=CROSS(X_9,X_7) AND X_9>80 AND X_7>80;
        X_17_raw = False
        if has_enough_data and len(X_7) > 1 and len(X_9) > 1:
            # 精确匹配通达信：X_9上穿X_7且都在超买区
            X_17_raw = (X_9[-1] > X_7[-1] and X_9[-2] <= X_7[-2]) and X_9[-1] > overbought_threshold and X_7[-1] > overbought_threshold
        
        # ============= 假信号过滤 ============= #
        # 1. 成交量过滤 - 确认信号时成交量有显著放大
        volume_pass = True
        if enable_volume_filter and len(VOL) > 5:
            avg_vol = np.mean(VOL[-5:-1])  # 前4根K线的平均成交量
            current_vol = VOL[-1]  # 当前K线成交量
            volume_pass = current_vol > avg_vol * volume_threshold
        
        # 2. 趋势过滤 - 确保交易方向与更大周期趋势一致
        trend_pass = True
        if enable_trend_filter and len(C) > trend_period:
            # 计算长周期均线趋势
            MA_60 = MA(C, trend_period)
            long_trend_up = C[-1] > MA_60[-1]  # 价格在长周期均线上方为上涨趋势
            
            # 只允许顺势交易
            if X_16_raw:  # 多头信号需要上涨趋势
                trend_pass = long_trend_up
            elif X_17_raw:  # 空头信号需要下跌趋势
                trend_pass = not long_trend_up
        
        # 3. MACD过滤 - 使用MACD确认信号
        macd_pass = True
        if enable_macd_filter:
            # 只有MACD与信号方向一致时才通过
            if X_16_raw:  # 多头信号需要MACD在0轴下方向上发散
                macd_pass = MACD26[-1] < 0 and MACD26[-1] > MACD26[-2]
            elif X_17_raw:  # 空头信号需要MACD在0轴上方向下发散
                macd_pass = MACD26[-1] > 0 and MACD26[-1] < MACD26[-2]
        
        # 4. 信号确认 - 避免信号过于频繁
        confirm_pass = True
        if enable_signal_confirm:
            # 检查是否满足最小信号间隔
            current_time = time.time()
            if X_16_raw and hasattr(g, 'last_long_signal_time'):
                elapsed_minutes = (current_time - g.last_long_signal_time) / 60
                confirm_pass = elapsed_minutes > min_signal_interval
            elif X_17_raw and hasattr(g, 'last_short_signal_time'):
                elapsed_minutes = (current_time - g.last_short_signal_time) / 60
                confirm_pass = elapsed_minutes > min_signal_interval
        
        # 综合所有过滤条件，确定最终信号
        X_16 = X_16_raw and volume_pass and trend_pass and macd_pass and confirm_pass
        X_17 = X_17_raw and volume_pass and trend_pass and macd_pass and confirm_pass
        
        # 如果信号通过所有过滤，记录时间戳
        if X_16 and not hasattr(g, 'last_long_signal_time'):
            g.last_long_signal_time = time.time()
        elif X_16:
            g.last_long_signal_time = time.time()
            
        if X_17 and not hasattr(g, 'last_short_signal_time'):
            g.last_short_signal_time = time.time()
        elif X_17:
            g.last_short_signal_time = time.time()
        
        # 输出通达信指标状态，包括过滤条件状态
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][通达信指标] X_7:{X_7[-1]:.2f} X_9:{X_9[-1]:.2f} X_13:{X_13[-1]:.2f}')
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][通达信原始信号] 多头:{X_16_raw} 空头:{X_17_raw}')
        if X_16_raw or X_17_raw:
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][信号过滤] 成交量:{volume_pass} 趋势:{trend_pass} MACD:{macd_pass} 时间间隔:{confirm_pass}')
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][通达信最终信号] 多头信号X_16:{X_16} 空头信号X_17:{X_17}')
        
        # 定义条件6的多空信号
        tj6_long = X_16  # 红箭头信号：X_7上穿X_9且都在超卖区 + 通过所有过滤
        tj6_short = X_17  # 绿箭头信号：X_9上穿X_7且都在超买区 + 通过所有过滤
    else:
        tj6_long = False
        tj6_short = False
    
    # 打印关键技术指标状态 - 简洁版
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][指标状态] 三色线多:{last_five_minute_long} 三色线空:{last_five_minute_short} 金叉:{cross_up} 死叉:{cross_down} 底背驰:{底背驰} 顶背驰:{顶背驰}')
    
    # 根据启用开关决定是否使用各条件
    if enable_condition1:
        tj1_long = tj_long
        tj1_short = tj_short
    else:
        tj1_long = False
        tj1_short = False
        
    if enable_condition3:
        tj3_long = kaiduo
        tj3_short = kaikong
    else:
        tj3_long = False
        tj3_short = False
        
    if enable_condition5:
        tj5_long = 底背驰 and C[-1] > MA_Line[-1]
        tj5_short = 顶背驰 and C[-1] < MA_Line[-1]
    else:
        tj5_long = False
        tj5_short = False
    
    # 整合开仓条件 - 只输出最终结果，包含条件6
    多头信号 = tj1_long or tj3_long or tj5_long or tj6_long
    空头信号 = tj1_short or tj3_short or tj5_short or tj6_short
    
    # 打印开仓条件 - 简洁版，增加条件6
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][信号] 多头条件1:{tj1_long} 多头条件3:{tj3_long} 多头底背驰:{tj5_long} 多头通达信:{tj6_long}')
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][信号] 空头条件1:{tj1_short} 空头条件3:{tj3_short} 空头顶背驰:{tj5_short} 空头通达信:{tj6_short}')
    
    # 最终决策
    if g.hold == 0 and b1 and g.buy_long < open_long_num and 多头信号:
        buy_long = True
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][决策] >>> 满足开多条件! <<<')
    else:
        buy_long = False
        if 多头信号:  # 只有在有信号但不能交易时才输出原因
            reason = []
            if g.hold != 0: reason.append("已有持仓")
            if not b1: reason.append("收盘前10分钟")
            if g.buy_long >= open_long_num: reason.append("达到开仓上限")
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][决策] 有多头信号但不开仓，原因: {", ".join(reason)}')
        
    if g.hold == 0 and b1 and g.buy_short < open_short_num and 空头信号:
        buy_short = True
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][决策] >>> 满足开空条件! <<<')
    else:
        buy_short = False
        if 空头信号:  # 只有在有信号但不能交易时才输出原因
            reason = []
            if g.hold != 0: reason.append("已有持仓")
            if not b1: reason.append("收盘前10分钟")
            if g.buy_short >= open_short_num: reason.append("达到开仓上限")
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][决策] 有空头信号但不开仓，原因: {", ".join(reason)}')

    if buy_long and time.time()-g.trace_time_long>sleep_time*60 and t not in g.opened_t and g.hold !=1:
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][执行] 发出开多指令')
        if g.hold == -1:
            passorder(53, 1101, account, g.put_one, 12, 0, hand, '',1,'策略平空'+g.remark,ContextInfo)
        call_one, put_one = get_option_real_one(ContextInfo)
        g.call_one = put_one
        g.buy_long+=1
        passorder(52, 1101, account, put_one, 12,0, hand,'',1,'期权策略开多'+g.remark,ContextInfo)
        g.curr_hold = put_one
        g.hold = 1
        g.trace_time_long = time.time()
        g.hold_price = 0
        g.open_price = 0
        g.opened_t.append(t)
        g.hold_code = put_one
        print(f'{call_one} 开多 {tj_long , tj3_long}')

    if buy_short and time.time()-g.trace_time_short>sleep_time*60 and t not in g.opened_t and g.hold !=-1:
        if g.hold == 1:
            passorder(53, 1101, account, g.call_one, 12, 0, hand, '',1,'策略平多'+g.remark,ContextInfo)
        call_one, put_one = get_option_real_one(ContextInfo)
        g.put_one = call_one
        passorder(52, 1101, account, call_one, 12,0, hand,'',1,'期权策略开空'+g.remark,ContextInfo)
        print(f'{g.put_one} 开空 {tj_short or tj3_short}')
        g.curr_hold = call_one
        g.buy_short+=1
        g.opened_t.append(t)
        g.hold_price = 9999999
        g.open_price = 0
        g.hold = -1
        g.hold_code = call_one
        g.trace_time_short = time.time()

    if g.hold != 0:
        print(g.put_one, g.call_one, g.hold, g.open_price)
        
    if g.open_price >0:
        full = ContextInfo.get_full_tick([g.curr_hold])[g.curr_hold]
        c = full['lastPrice']
        hold_ratio = round((c/g.open_price-1)*100,2)
        if g.hold>0:
            g.hold_price = max(c, g.hold_price)
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][多头][{g.curr_hold}] 成本价:{g.open_price:.4f} 最新价:{c:.4f} 盈亏比例:{hold_ratio:.2f}% 已平仓比例:{g.closed_ratio:.2f}')
        elif g.hold<0:
            g.hold_price = max(c, g.hold_price)
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][空头][{g.curr_hold}] 成本价:{g.open_price:.4f} 最新价:{c:.4f} 盈亏比例:{hold_ratio:.2f}% 已平仓比例:{g.closed_ratio:.2f}')
        
        # 期权卖方止盈止损逻辑：期权价格下跌是盈利，上涨是亏损
        if g.hold == 1:  # 多头持仓（卖出认购期权）
            g.hold_price = max(g.hold_price, c)
            
            # 分批止损：期权价格上涨超过不同阈值时分批平仓
            if g.closed_ratio == 0 and hold_ratio >= option_seller_stop_loss_1 and hold_ratio < option_seller_stop_loss_2:
                # 第一次止损，平1/3仓位
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1][多头] 期权价格上涨{hold_ratio:.2f}%，触发第一次止损条件{option_seller_stop_loss_1}%，平掉1/3仓位')
                first_hand = math.ceil(hand/3)
                passorder(53, 1101, account, g.call_one, 12, 0, first_hand, '',1,'平多止损1'+g.remark,ContextInfo)
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1委托][多头] {g.call_one} 平多止损1 {hold_ratio:.2f}% 数量:{first_hand}')
                g.closed_ratio = 0.33
            
            elif g.closed_ratio <= 0.33 and hold_ratio >= option_seller_stop_loss_2 and hold_ratio < option_seller_stop_loss_3:
                # 第二次止损，平1/3仓位
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2][多头] 期权价格上涨{hold_ratio:.2f}%，触发第二次止损条件{option_seller_stop_loss_2}%，平掉1/3仓位')
                second_hand = math.ceil(hand/3)
                passorder(53, 1101, account, g.call_one, 12, 0, second_hand, '',1,'平多止损2'+g.remark,ContextInfo)
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2委托][多头] {g.call_one} 平多止损2 {hold_ratio:.2f}% 数量:{second_hand}')
                g.closed_ratio = 0.67
            
            elif g.closed_ratio <= 0.67 and hold_ratio >= option_seller_stop_loss_3:
                # 第三次止损，平剩余仓位
                remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
                if remain_hand > 0:
                    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3][多头] 期权价格上涨{hold_ratio:.2f}%，触发第三次止损条件{option_seller_stop_loss_3}%，平掉剩余{remain_hand}仓位')
                    passorder(53, 1101, account, g.call_one, 12, 0, remain_hand, '',1,'平多止损3'+g.remark,ContextInfo)
                    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3委托][多头] {g.call_one} 平多止损3 {hold_ratio:.2f}% 数量:{remain_hand}')
                g.hold = 0
                g.closed_ratio = 1
            
            # 分批止盈：期权价格下跌超过不同阈值时分批平仓
            elif g.closed_ratio == 0 and hold_ratio <= option_seller_profit_1 and hold_ratio > option_seller_profit_2:
                # 第一次止盈，平1/3仓位
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第一次止盈条件{-option_seller_profit_1}%，平掉1/3仓位')
                first_hand = math.ceil(hand/3)
                passorder(53, 1101, account, g.call_one, 12, 0, first_hand, '',1,'平多止盈1'+g.remark,ContextInfo)
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1委托][多头] {g.call_one} 平多止盈1 {hold_ratio:.2f}% 数量:{first_hand}')
                g.closed_ratio = 0.33
            
            elif g.closed_ratio <= 0.33 and hold_ratio <= option_seller_profit_2 and hold_ratio > option_seller_profit_3:
                # 第二次止盈，平1/3仓位
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第二次止盈条件{-option_seller_profit_2}%，平掉1/3仓位')
                second_hand = math.ceil(hand/3)
                passorder(53, 1101, account, g.call_one, 12, 0, second_hand, '',1,'平多止盈2'+g.remark,ContextInfo)
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2委托][多头] {g.call_one} 平多止盈2 {hold_ratio:.2f}% 数量:{second_hand}')
                g.closed_ratio = 0.67
            
            elif g.closed_ratio <= 0.67 and hold_ratio <= option_seller_profit_3:
                # 第三次止盈，平剩余仓位
                remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
                if remain_hand > 0:
                    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3][多头] 期权价格下跌{-hold_ratio:.2f}%，触发第三次止盈条件{-option_seller_profit_3}%，平掉剩余{remain_hand}仓位')
                    passorder(53, 1101, account, g.call_one, 12, 0, remain_hand, '',1,'平多止盈3'+g.remark,ContextInfo)
                    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3委托][多头] {g.call_one} 平多止盈3 {hold_ratio:.2f}% 数量:{remain_hand}')
                g.hold = 0
                g.closed_ratio = 1
                
        if g.hold == -1:  # 空头持仓（卖出认沽期权）
            g.hold_price = max(g.hold_price, c)
            
            # 分批止损：期权价格上涨超过不同阈值时分批平仓
            if g.closed_ratio == 0 and hold_ratio >= option_seller_stop_loss_1 and hold_ratio < option_seller_stop_loss_2:
                # 第一次止损，平1/3仓位
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1][空头] 期权价格上涨{hold_ratio:.2f}%，触发第一次止损条件{option_seller_stop_loss_1}%，平掉1/3仓位')
                first_hand = math.ceil(hand/3)
                passorder(53, 1101, account, g.put_one, 12, 0, first_hand, '',1,'平空止损1'+g.remark,ContextInfo)
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损1委托][空头] {g.put_one} 平空止损1 {hold_ratio:.2f}% 数量:{first_hand}')
                g.closed_ratio = 0.33
            
            elif g.closed_ratio <= 0.33 and hold_ratio >= option_seller_stop_loss_2 and hold_ratio < option_seller_stop_loss_3:
                # 第二次止损，平1/3仓位
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2][空头] 期权价格上涨{hold_ratio:.2f}%，触发第二次止损条件{option_seller_stop_loss_2}%，平掉1/3仓位')
                second_hand = math.ceil(hand/3)
                passorder(53, 1101, account, g.put_one, 12, 0, second_hand, '',1,'平空止损2'+g.remark,ContextInfo)
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损2委托][空头] {g.put_one} 平空止损2 {hold_ratio:.2f}% 数量:{second_hand}')
                g.closed_ratio = 0.67
            
            elif g.closed_ratio <= 0.67 and hold_ratio >= option_seller_stop_loss_3:
                # 第三次止损，平剩余仓位
                remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
                if remain_hand > 0:
                    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3][空头] 期权价格上涨{hold_ratio:.2f}%，触发第三次止损条件{option_seller_stop_loss_3}%，平掉剩余{remain_hand}仓位')
                    passorder(53, 1101, account, g.put_one, 12, 0, remain_hand, '',1,'平空止损3'+g.remark,ContextInfo)
                    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止损3委托][空头] {g.put_one} 平空止损3 {hold_ratio:.2f}% 数量:{remain_hand}')
                g.hold = 0
                g.closed_ratio = 1
            
            # 分批止盈：期权价格下跌超过不同阈值时分批平仓
            elif g.closed_ratio == 0 and hold_ratio <= option_seller_profit_1 and hold_ratio > option_seller_profit_2:
                # 第一次止盈，平1/3仓位
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1][空头] 期权价格下跌{-hold_ratio:.2f}%，触发第一次止盈条件{-option_seller_profit_1}%，平掉1/3仓位')
                first_hand = math.ceil(hand/3)
                passorder(53, 1101, account, g.put_one, 12, 0, first_hand, '',1,'平空止盈1'+g.remark,ContextInfo)
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈1委托][空头] {g.put_one} 平空止盈1 {hold_ratio:.2f}% 数量:{first_hand}')
                g.closed_ratio = 0.33
            
            elif g.closed_ratio <= 0.33 and hold_ratio <= option_seller_profit_2 and hold_ratio > option_seller_profit_3:
                # 第二次止盈，平1/3仓位
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2][空头] 期权价格下跌{-hold_ratio:.2f}%，触发第二次止盈条件{-option_seller_profit_2}%，平掉1/3仓位')
                second_hand = math.ceil(hand/3)
                passorder(53, 1101, account, g.put_one, 12, 0, second_hand, '',1,'平空止盈2'+g.remark,ContextInfo)
                print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈2委托][空头] {g.put_one} 平空止盈2 {hold_ratio:.2f}% 数量:{second_hand}')
                g.closed_ratio = 0.67
            
            elif g.closed_ratio <= 0.67 and hold_ratio <= option_seller_profit_3:
                # 第三次止盈，平剩余仓位
                remain_hand = hand - math.ceil(hand/3) - math.ceil(hand/3)
                if remain_hand > 0:
                    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3][空头] 期权价格下跌{-hold_ratio:.2f}%，触发第三次止盈条件{-option_seller_profit_3}%，平掉剩余{remain_hand}仓位')
                    passorder(53, 1101, account, g.put_one, 12, 0, remain_hand, '',1,'平空止盈3'+g.remark,ContextInfo)
                    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][止盈3委托][空头] {g.put_one} 平空止盈3 {hold_ratio:.2f}% 数量:{remain_hand}')
                g.hold = 0
                g.closed_ratio = 1
                
    if not b1:
        if g.hold !=0:
            orders = get_trade_detail_data(account,'STOCK','STOCK_OPTION')
            for o in orders:
                if o.m_nOrderStatus  in [50,55]: # 委托可撤时再撤单
                    cancel(o.m_strOrderSysID, account, 'stock', ContextInfo)
            time.sleep(1)
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][尾盘平仓] 即将平掉所有持仓')
            passorder(53, 1101, account, g.hold_code, 12, 0, hand, '',1,'',ContextInfo) 
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][尾盘平仓委托] {g.hold_code} 尾盘平仓')
            g.hold = 0
            g.closed_ratio = 1
            

def get_shape(CG,FL,MA_Line,FS,B1,B2):
    # 2.首次出现三色线，且在分时均线上方持续时间<15分钟，再度变成单红色线
    count = 0
    record = []
    compare_ma = []
    for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
        if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
            continue
        record.append(cg==fl==fs)
        compare_ma.append(min(cg,fl,fs)>ma)
    pre=None
    record.reverse()
    compare_ma.reverse()
    i = 0
    if not record:
        return 0, 99
    if not record[0]:
        return 0, 99
    if not compare_ma[0]:
        return 0, 99
    # 获取大于均线的三条线
    uprecord = []
    for r, cpma in zip(record, compare_ma):
        if not cpma:
            break
        uprecord.append(r)
    # 去除重复连续
    drop_uprecord = []
    for i in range(len(uprecord)):
        if i == 0 or uprecord[i] != uprecord[i-1]:
            drop_uprecord.append(uprecord[i])
    if drop_uprecord.count(False)!=1:
        return 0, 99
    else:
        return 1, uprecord.count(False)

# 1.现价线和FS CG FL（三色线）在分时均线下方 且B1-B2<0
# 2.首次出现单条红色线，且在分时均线下方持续时间<15分钟，再度变成三色线
# 3.分时均线价位—FS即时价位<5
def get_shape_kong(CG,FL,MA_Line,FS,B1,B2):
    count = 0
    record = []
    compare_ma = []
    for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
        if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
            continue
        record.append(not cg==fl==fs)
        compare_ma.append(max(cg,fl,fs)<ma)
    # record True 三色线
    pre=None
    record.reverse()
    compare_ma.reverse()
    i = 0
    if not record:
        return 0, 99
    if not record[0]:
        return 0, 99
    if not compare_ma[0]:
        return 0, 99
    
    uprecord = []
    for r, cpma in zip(record, compare_ma):
        if not cpma:
            break
        uprecord.append(r)
    # 去除重复连续
    drop_uprecord = []
    for i in range(len(uprecord)):
        if i == 0 or uprecord[i] != uprecord[i-1]:
            drop_uprecord.append(uprecord[i])
    if drop_uprecord.count(False)!=1:
        return 0, 99
    else:
        return 1, uprecord.count(False)


def get_shape_old(CG,FL,MA_Line,FS,B1,B2):
    #判断是不是首次出现的三色线
    #CG[i]==FL[i]==FS[i]
    count = 0
    record = []
    compare_ma = []
    for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
        if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
            continue
        record.append(cg==fl==fs)
        compare_ma.append(min(cg,fl,fs)>ma)
    pre=None
    for pos, b in enumerate(record):
        if pre is None:
            pre = b
            continue
        if b and pre==False:
            count += 1
            pre = b
            continue
        if not b and pos == len(record) -1:
            count+=1
            pre = b
            continue
        pre = b
    return count, sum([1 for r, cp in zip(record, compare_ma) if not r and cp])


def get_shape_kong_old(CG,FL,MA_Line,FS,B1,B2):
    #判断是不是首次出现的三色线
    #CG[i]==FL[i]==FS[i]
    count = 0
    record = []
    compare_ma = []
    for cg, fl, ma, fs, b1, b2 in zip(CG,FL,MA_Line,FS,B1,B2):
        if math.isnan(cg) or math.isnan(fl) or math.isnan(fs):
            continue
        record.append(not cg==fl==fs)
        compare_ma.append(min(cg,fl,fs)<ma)
    pre=None
    for pos, b in enumerate(record):
        if pre is None:
            pre = b
            continue
        if b and pre==False:
            count += 1
            pre = b
            continue
        if not b and pos == len(record) -1:
            count+=1
            pre = b
            continue
        pre = b
    return count, sum([1 for r, cp in zip(record, compare_ma) if not r and cp])


def order_callback(ContextInfo, orderInfo):
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][订单回调] {orderInfo.m_strRemark}, 状态:{orderInfo.m_nOrderStatus}, 开平标志:{orderInfo.m_nOffsetFlag}, 成交价:{orderInfo.m_dTradedPrice}')
    if orderInfo.m_strRemark not in  ['期权策略开多'+g.remark,'期权策略开空'+g.remark]:
        return
    marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(orderInfo.m_strExchangeID, orderInfo.m_strExchangeID)
    k = orderInfo.m_strInstrumentID+'.'+marekt
    if k not in [g.call_one, g.put_one]:
        return
    #if orderInfo.m_nOrderStatus==56 and orderInfo.m_nOffsetFlag == 48 and orderInfo.m_dTradedPrice>0:
    #    g.open_price = round(orderInfo.m_dTradedPrice,1)
    #    g.hold_price = round(orderInfo.m_dTradedPrice,1)
    if orderInfo.m_nOrderStatus==56 and orderInfo.m_strRemark.startswith('期权策略开多'+g.remark):
        g.buy_long+=1
        g.closed_ratio = 0  # 重置已平仓比例
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][开仓成功][多头] {g.code} 开多次数+1: {g.buy_long}')
    if orderInfo.m_nOrderStatus==56 and orderInfo.m_strRemark.startswith('期权策略开空'+g.remark):
        g.buy_short+=1
        g.closed_ratio = 0  # 重置已平仓比例
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][开仓成功][空头] {g.code} 开空次数+1: {g.buy_short}')

    if orderInfo.m_nOrderStatus==57 and orderInfo.m_nOffsetFlag == 48:
        g.hold = 0
        g.closed_ratio = 1  # 全部平仓
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平仓成功] 持仓状态设置为0')


def orderError_callback(ContextInfo,passOrderInfo,msg):
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][订单错误] 策略名:{passOrderInfo.strategyName}, 错误信息:{msg}')
    if '期权策略'+g.remark in passOrderInfo.strategyName:
        g.hold = 0
        g.closed_ratio = 1  # 全部平仓
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][订单错误] 持仓状态设置为0')
    if '期权策略开空'+g.remark in passOrderInfo.strategyName:
        g.buy_short+=1
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][订单错误][空头] {g.code} 开空次数+1: {g.buy_short}')
    if '期权策略开多'+g.remark in passOrderInfo.strategyName:
        g.buy_long+=1
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][订单错误][多头] {g.code} 开多次数+1: {g.buy_long}')


def deal_callback(ContextInfo, dealInfo):
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][成交回调] 开平标志:[{dealInfo.m_nOffsetFlag}] 策略备注:[{dealInfo.m_strRemark}], 合约:[{dealInfo.m_strInstrumentID}] 交易所:[{dealInfo.m_strExchangeID}] 成交价格:[{dealInfo.m_dPrice}]')
    if dealInfo.m_strRemark not in  ['期权策略开多'+g.remark,'期权策略开空'+g.remark]:
        return
    
    marekt = {"SHFE":'SF',"CZCE":'ZF',"DCE":'DF',"CFFEX":'IF'}.get(dealInfo.m_strExchangeID, dealInfo.m_strExchangeID)
    k = dealInfo.m_strInstrumentID+'.'+marekt
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][成交回调] 合约代码检查: {k in [g.call_one, g.put_one]}, 代码包含: {g.code.find(dealInfo.m_strInstrumentID)}')
    if k not in [g.call_one, g.put_one]:
        return
    if dealInfo.m_nOffsetFlag == 48:
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][成交记录] 记录成交价格: {dealInfo.m_dPrice:.4f}')
        g.open_price = round(dealInfo.m_dPrice, 4)
        g.hold_price = round(dealInfo.m_dPrice,4)
        g.closed_ratio = 0  # 重置已平仓比例
def REF(S, N=1):          #对序列整体下移动N,返回序列(shift后会产生NAN)    
    return pd.Series(S).shift(N).values  
def SMA(S, N, M=1):       #中国式的SMA,至少需要120周期才精确 (雪球180周期)    alpha=1/(1+com)    
    return pd.Series(S).ewm(alpha=M/N,adjust=False).mean().values           #com=N-M/M
def SUM(S, N):            #对序列求N天累计和，返回序列    N=0对序列所有依次求和         
    return pd.Series(S).rolling(N).sum().values if N>0 else pd.Series(S).cumsum().values  
def HHV(S,N):             #HHV(C, 5) 最近5天收盘最高价        
    return pd.Series(S).rolling(N).max().values     

def LLV(S,N):             #LLV(C, 5) 最近5天收盘最低价     
    return pd.Series(S).rolling(N).min().values    
    
def HHVBARS(S,N):         #求N周期内S最高值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmax(x[::-1]),raw=True).values 

def LLVBARS(S,N):         #求N周期内S最低值到当前周期数, 返回序列
    return pd.Series(S).rolling(N).apply(lambda x: np.argmin(x[::-1]),raw=True).values    
  
def MA(S,N):              #求序列的N日简单移动平均值，返回序列                    
    return pd.Series(S).rolling(N).mean().values  
def EMA(S,N):             #指数移动平均,为了精度 S>4*N  EMA至少需要120周期     alpha=2/(span+1)    
    return pd.Series(S).ewm(span=N, adjust=False).mean().values     

def EMA(source, N:int, result_type='np'):
    """
    求指数平滑移动平均.
    用法:
    EMA(X,N),求X的N日指数平滑移动平均.算法：若Y=EMA(X,N)
    则Y=[2*X+(N-1)*Y']/(N+1),其中Y'表示上一周期Y值.
    例如：EMA(CLOSE,30)表示求30日指数平滑均价
    """
    # if not isinstance(source, np.ndarray):
    #     source = np.array(source)
    M = 2
    if N<M:
        raise ValueError(f"N:{N}必须大于{M}")
    result = []
    temp = None
    d = N-1
    M = 2
    for pos, x in enumerate(source):
        if pos == 0:
            result.append(x)
            temp = x
        else:
            temp = (M*x+d*temp)/(N+1)
            result.append(temp)
    if result_type == 'np':
        return np.array(result)
    else:
        return result

def PyLLV(S, N):
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i+1-34):i+1]
        result_list.append(min(l))
    return pd.Series(result_list, index=index)
    
    
def PyHHV(S, N):
    index = S.index
    result_list = []
    slist = list(S)

    for i in range(len(S.index)):
        l = slist[max(0, i+1-34):i+1]
        result_list.append(max(l))
    return pd.Series(result_list, index=index)

def cal_vba(ContextInfo, price):
    C = CLOSE = price['close']
    HIGH = price['close']
    LOW = price['close']
    AMT = price['amount']
    VOL = price['volume']
    MA_Line = SUM(C*VOL,0)/SUM(VOL, 0)
    CG=MA(C,21)
    FL=HHV(CG,3)
    FS=CG-(FL-CG)
    
    VA6=(2*CLOSE+HIGH+LOW)/4
    VA8=LLV(LOW,34)
    VARB=HHV(HIGH,34)
    VARC=EMA((VA6-VA8)/(VARB-VA8)*100,13)
    VARD=EMA(0.667*REF(VARC,1)+0.333*VARC,2)
    生命线:EMA(VARD,10)

    VAR1=HHV(HIGH,9)-LLV(LOW,9)
    VAR2=HHV(HIGH,9)-CLOSE
    VAR3=CLOSE-LLV(LOW,9)
    VAR4=((VAR2)/(VAR1))*(100)-70
    VAR5=((CLOSE-LLV(LOW,60))/(HHV(HIGH,60)-LLV(LOW,60)))*(100)


    VAR6=((2)*(CLOSE)+HIGH+LOW)/(4)
    index = VAR6.index
    VAR6 = pd.Series([v for v in VAR6],index=index)
    VAR7=SMA(((VAR3)/(VAR1))*(100),3,1)
    VAR8=PyLLV(CLOSE,min(34, len(LOW)))
    VAR9=SMA(VAR7,3,1)-SMA(VAR4,9,1)
    VAR10 = pd.Series([v-100 if v>100 else 0 for v in VAR9])
    VAR11=PyHHV(CLOSE,min(34, len(HIGH)))
    vv = ((VAR6-VAR8)/(VAR11-VAR8))*(100)
    vv=vv.fillna(0)
    vv=vv.replace([np.inf, -np.inf], np.nan).fillna(0)
    B1=EMA(vv,8)
    B2=EMA(B1,5)
    print(VAR6[-1],VAR11[-1],VAR8[-1])
    return CG,FL,MA_Line,FS,B1,B2


def get_option_real_one(ContextInfo):
    call_one = put_one = None
    now = time.strftime("%Y%m%d")
    
    # 获取所有当月认购期权
    call_list = get_current_month_option(ContextInfo, g.undl_code, now, 'CALL')
    
    # 标的物现价
    undl_price = ContextInfo.get_full_tick([g.undl_code])[g.undl_code]['lastPrice']
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][标的价格] {g.undl_code} 现价: {undl_price:.4f}')
    
    # 获取所有认购期权的行权价
    call_dict = {call: ContextInfo.get_option_detail_data(call)['OptExercisePrice'] for call in call_list}
    
    # 找出平值行权价（与标的价格最接近的行权价）
    equal_dict = {strike_price: abs(strike_price - undl_price) for strike_price in set(call_dict.values())}
    atm_strike = sorted(equal_dict.keys(), key=lambda x: equal_dict[x])[0]
    print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][平值行权价] 标的价格: {undl_price:.4f}, 平值行权价: {atm_strike:.4f}')
    
    # 按行权价从小到大排序所有行权价
    all_strikes = sorted(set(call_dict.values()))
    atm_index = all_strikes.index(atm_strike)
    
    # 找出实值认购期权（比平值高的行权价）
    if atm_index + option_strike_offset + call_strike_adjust < len(all_strikes):
        # 选择平值上方第N档（实值）认购期权行权价
        itm_call_strike_index = atm_index + option_strike_offset + call_strike_adjust
        itm_call_strike = all_strikes[itm_call_strike_index]
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][认购期权] 选择行权价: {itm_call_strike:.4f} (平值上方第{option_strike_offset + call_strike_adjust}档)')
        
        # 找出具有该行权价的认购期权合约
        itm_calls = [code for code, strike in call_dict.items() if strike == itm_call_strike]
        if itm_calls:
            call_one = itm_calls[0]
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][认购期权] 选择合约: {call_one}')
    else:
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][警告] 无法选择平值上方第{option_strike_offset + call_strike_adjust}档的认购期权，可能超出可用范围')
    
    # 获取所有当月认沽期权
    put_list = get_current_month_option(ContextInfo, g.undl_code, now, 'PUT')
    
    # 获取所有认沽期权的行权价
    put_dict = {put: ContextInfo.get_option_detail_data(put)['OptExercisePrice'] for put in put_list}
    
    # 按行权价从小到大排序所有行权价（使用相同的all_strikes和atm_index）
    
    # 找出实值认沽期权（比平值低的行权价）
    if atm_index >= option_strike_offset + put_strike_adjust:
        # 选择平值下方第N档（实值）认沽期权行权价
        itm_put_strike_index = atm_index - option_strike_offset - put_strike_adjust
        itm_put_strike = all_strikes[itm_put_strike_index]
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][认沽期权] 选择行权价: {itm_put_strike:.4f} (平值下方第{option_strike_offset + put_strike_adjust}档)')
        
        # 找出具有该行权价的认沽期权合约
        itm_puts = [code for code, strike in put_dict.items() if strike == itm_put_strike]
        if itm_puts:
            put_one = itm_puts[0]
            print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][认沽期权] 选择合约: {put_one}')
    else:
        print(f'[{time.strftime("%Y-%m-%d %H:%M:%S")}][警告] 无法选择平值下方第{option_strike_offset + put_strike_adjust}档的认沽期权，可能超出可用范围')
    
    return call_one, put_one


def get_current_month_option(ContextInfo, object, dedate, opttype=""):
    #dedate 日期 %Y%m%d 
    #获取截止到ddate这天还未到行权日的当月期权合约
    isavailavle = True
    result = []
    opt_by_month = {}
    undlMarket = ""
    undlCode = ""
    marketcodeList = object.split('.')
    if(len(marketcodeList) !=2):
        return []
    undlCode = marketcodeList[0]
    undlMarket = marketcodeList[1]
    market = ""
    if(undlMarket == "SH"):
        if undlCode == "000016" or undlCode == "000300" or undlCode == "000852" or undlCode == "000905":
            market = 'IF'
        else:
            market = "SHO"
    elif(undlMarket == "SZ"):
        market = "SZO"
    if(opttype.upper() == "C"):
        opttype = "CALL"
    elif(opttype.upper() == "P"):
        opttype = "PUT"
    optList = []
    if market == 'SHO':
        optList += get_stock_list_in_sector('上证期权')
    elif market == 'SZO':
        optList += get_stock_list_in_sector('深证期权')
    elif market == 'IF':
        optList += get_stock_list_in_sector('中金所')
    
    # 按到期日对期权进行分组
    expiry_groups = {}
    today = datetime.now().strftime('%Y%m%d')
    today_dt = datetime.strptime(today, '%Y%m%d')
    
    for opt in optList:
        if(opt.find(market) < 0):
            continue
        inst = ContextInfo.get_option_detail_data(opt)
        if('optType' not in inst):
            continue
            
        # 获取实际到期日
        endDate = inst['EndDelivDate']
        
        # 跳过已到期合约
        if str(endDate) <= dedate:
            continue
            
        # 计算到期剩余天数
        try:
            end_dt = datetime.strptime(str(endDate), '%Y%m%d')
            days_to_expiry = (end_dt - today_dt).days
        except:
            continue
            
        # 检查是否到提前换月的时间
        if days_to_expiry < switch_days_before_expiry:
            continue
            
        if(opttype.upper() != "" and opttype.upper() != inst["optType"]):
            continue
            
        if 1: # option is trade,guosen demand
            createDate = inst['OpenDate']
            openDate = inst['OpenDate']
            if(createDate >= 1):
                openDate = min(openDate,createDate)
            if(openDate < 20150101 or str(openDate) > dedate):
                continue
                
        if(inst['ProductID'].find(undlCode) > 0 or inst['OptUndlCode'] == undlCode):
            # 按到期日分组
            month_key = str(endDate)[:6]
            if month_key not in expiry_groups:
                expiry_groups[month_key] = {
                    'end_date': str(endDate),
                    'days_to_expiry': days_to_expiry,
                    'options': [opt]
                }
            else:
                expiry_groups[month_key]['options'].append(opt)
    
    # 按到期日排序
    sorted_expiry = sorted(expiry_groups.keys())
    
    # 没有满足条件的期权
    if not sorted_expiry:
        print(object, '未找到符合要求的期权')
        return []
        
    # 返回最近到期的期权列表
    return expiry_groups[sorted_expiry[0]]['options']




